﻿<?xml version="1.0" encoding="UTF-8" ?>
<draw:Canvas
    x:Class="Triggero.MauiMobileApp.Views.ListCategoriesView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:viewModels="clr-namespace:Triggero.MauiMobileApp.ViewModels"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="this"
    x:DataType="viewModels:BaseCategoriesViewModel"
    Gestures="Lock"
    RenderingMode="Accelerated"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">

    <draw:SkiaLayout
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:SkiaScroll
            FrictionScrolled="0.35"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLayout
                x:Name="StackCells"
                HorizontalOptions="Fill"
                ItemTemplate="{Binding ItemTemplate}"
                ItemsSource="{Binding Items}"
                RecyclingTemplate="Disabled"
                Spacing="12"
                Type="Column"
                UseCache="Image" />

        </draw:SkiaScroll>


        <!--<draw:SkiaLabel
            UseCache="Operations"
            Margin="8"
            BackgroundColor="Black"
            HorizontalOptions="Start"
            InputTransparent="True"
            Text="{Binding Source={x:Reference StackCells}, Path=DebugString}"
            VerticalOptions="Start" />-->

        <!--  FPS  -->
        <draw:SkiaLabelFps
            Margin="0,0,4,84"
            BackgroundColor="DarkRed"
            ForceRefresh="False"
            HorizontalOptions="End"
            IsVisible="{x:Static triggeroV2:Globals.ShowFPS}"
            Rotation="-45"
            TextColor="White"
            VerticalOptions="End" />

    </draw:SkiaLayout>

</draw:Canvas>