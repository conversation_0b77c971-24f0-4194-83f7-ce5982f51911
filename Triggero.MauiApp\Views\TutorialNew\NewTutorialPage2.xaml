﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage2"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="170"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="140"/>
            </Grid.RowDefinitions>
            
            <Image
                Grid.RowSpan="3"
                Aspect="Fill"
                Source="tutorialBlur2.png"/>

            <Grid Grid.Row="0">

                <Grid
                    VerticalOptions="End"
                    HeightRequest="125">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>


                    <Grid Grid.Column="0">
                        <StackLayout
                            Spacing="12"
                            HeightRequest="66"
                            VerticalOptions="Center"
                            Orientation="Horizontal">

                            <Frame 
                                Margin="20,0,0,0"
                                CornerRadius="33"
                                Padding="0"
                                IsClippedToBounds="True"
                                BackgroundColor="#DEEDF9"
                                BorderColor="{x:StaticResource blueColor}"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                HeightRequest="66"
                                WidthRequest="66"
                                HasShadow="False">
                                <Image 
                                    x:Name="avatar"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    HeightRequest="66"
                                    WidthRequest="66"/>
                            </Frame>

                            <StackLayout
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                Spacing="8">
                                <Label 
                                    x:Name="hiName"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontSize="17"
                                    FontAttributes="Bold"
                                    HorizontalOptions="Start"
                                    Text="Привет, Кристина"/>
                                <Label 
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontSize="14"
                                    HorizontalOptions="Start"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.WhatWillDo}"/>
                            </StackLayout>

                        </StackLayout>
                    </Grid>

                    <Grid Grid.Column="1">
                        <StackLayout
                            Margin="0,0,20,0"
                            HorizontalOptions="End"
                            VerticalOptions="Center"
                            Spacing="20"
                            Orientation="Horizontal">
                            <ImageButton
                                BackgroundColor="Transparent"
                                WidthRequest="20"
                                HeightRequest="20"
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Source="search.png"/>
                            <ImageButton
                                BackgroundColor="Transparent"
                                WidthRequest="20"
                                HeightRequest="20"
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Source="likeSet.png"/>
                        </StackLayout>
                    </Grid>

                </Grid>
                
            </Grid>

            <Grid Grid.Row="1">

                <StackLayout
                    Margin="20,-10,20,0"
                    Spacing="12">


                    <Frame
                        BackgroundColor="White"
                        CornerRadius="15"
                        Padding="0"
                        HasShadow="False"
                        HeightRequest="{x:OnPlatform Android=267,iOS=267}">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="45"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">
                                <Label 
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontSize="14"
                                    VerticalOptions="Center"
                                    Margin="16,6,0,0"
                                    HorizontalOptions="Start"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Completed}"/>

                                <Frame
                                    VerticalOptions="Center"
                                    HorizontalOptions="End"
                                    Margin="0,6,16,0"
                                    BackgroundColor="White"
                                    Background="White"
                                    BorderColor="{x:StaticResource greyColor}"
                                    WidthRequest="36"
                                    HeightRequest="22"
                                    CornerRadius="9"
                                    HasShadow="False"
                                    Padding="0">
                                    <Label 
                                        TextColor="{x:StaticResource greyColor}"
                                        FontSize="14"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        Text="1/3"/>
                                </Frame>

                            </Grid>

                            <Grid Grid.Row="1">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="40"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="50"/>
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0">
                                    <RadioButton 
                                        IsChecked="True"
                                        InputTransparent="True"
                                        Style="{x:StaticResource yellow_checkMark_rb}"
                                        VerticalOptions="Center"
                                        HorizontalOptions="End"
                                        HeightRequest="24"
                                        WidthRequest="24"/>
                                </Grid>

                                <Grid Grid.Column="1">
                                    <Image 
                                        Source="tutorialToDo1.png"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        HeightRequest="55"
                                        WidthRequest="55">
                                    </Image>
                                </Grid>

                                <Grid Grid.Column="2">
                                    <StackLayout
                                        Spacing="3"
                                        VerticalOptions="Center">
                                        <Label 
                                            TextColor="{x:StaticResource greyTextColor}"
                                            FontSize="17"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Task1Text}"/>
                                        <Label 
                                            TextColor="{x:StaticResource greyTextColor}"
                                            Opacity="0.5"
                                            FontSize="12"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="1 "/>
                                                        <Span Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.MinutesAbbrevated}"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </StackLayout>
                                </Grid>

                                <Grid Grid.Column="3">
                                    <Image 
                                        Source="arrowForwardLightBlue.png"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        WidthRequest="6"
                                        HeightRequest="12" />
                                </Grid>

                                <BoxView
                                    Opacity="0.5"
                                    Margin="20,0,20,0"
                                    BackgroundColor="{x:StaticResource lightBlueColor}"
                                    VerticalOptions="End"
                                    HorizontalOptions="Fill"
                                    HeightRequest="1"
                                    Grid.ColumnSpan="4"/>

                            </Grid>

                            <Grid Grid.Row="2">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="40"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="50"/>
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0">
                                    <RadioButton 
                                        IsChecked="False"
                                        InputTransparent="True"
                                        Style="{x:StaticResource yellow_checkMark_rb}"
                                        VerticalOptions="Center"
                                        HorizontalOptions="End"
                                        HeightRequest="24"
                                        WidthRequest="24"/>
                                </Grid>

                                <Grid Grid.Column="1">
                                    <Image 
                                        Source="tutorialToDo2.png"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        HeightRequest="55"
                                        WidthRequest="55">
                                    </Image>
                                </Grid>

                                <Grid Grid.Column="2">
                                    <StackLayout
                                        Spacing="3"
                                        VerticalOptions="Center">
                                        <Label 
                                            TextColor="{x:StaticResource greyTextColor}"
                                            FontSize="17"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Task2Text}"/>
                                        <Label 
                                            TextColor="{x:StaticResource greyTextColor}"
                                            Opacity="0.5"
                                            FontSize="12"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="3 "/>
                                                        <Span Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.MinutesAbbrevated}"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </StackLayout>
                                </Grid>

                                <Grid Grid.Column="3">
                                    <Image 
                                        Source="arrowForwardLightBlue.png"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        WidthRequest="6"
                                        HeightRequest="12" />
                                </Grid>

                                <BoxView
                                    Opacity="0.5"
                                    Margin="20,0,20,0"
                                    BackgroundColor="{x:StaticResource lightBlueColor}"
                                    VerticalOptions="End"
                                    HorizontalOptions="Fill"
                                    HeightRequest="1"
                                    Grid.ColumnSpan="4"/>
                                
                            </Grid>

                            <Grid Grid.Row="3">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="40"/>
                                    <ColumnDefinition Width="80"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="50"/>
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0">
                                    <RadioButton 
                                        IsChecked="False"
                                        InputTransparent="True"
                                        Style="{x:StaticResource yellow_checkMark_rb}"
                                        VerticalOptions="Center"
                                        HorizontalOptions="End"
                                        HeightRequest="24"
                                        WidthRequest="24"/>
                                </Grid>

                                <Grid Grid.Column="1">
                                    <Image 
                                        Source="tutorialToDo3.png"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        HeightRequest="55"
                                        WidthRequest="55">
                                    </Image>
                                </Grid>

                                <Grid Grid.Column="2">
                                    <StackLayout
                                        Spacing="3"
                                        VerticalOptions="Center">
                                        <Label 
                                            TextColor="{x:StaticResource greyTextColor}"
                                            FontSize="17"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Text='{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Task3Text}'/>
                                        <Label 
                                            TextColor="{x:StaticResource greyTextColor}"
                                            Opacity="0.5"
                                            FontSize="12"
                                            FontFamily="FontTextLight"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start">
                                            <Label.FormattedText>
                                                <FormattedString>
                                                    <FormattedString.Spans>
                                                        <Span Text="10 "/>
                                                        <Span Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.MinutesAbbrevated}"/>
                                                    </FormattedString.Spans>
                                                </FormattedString>
                                            </Label.FormattedText>
                                        </Label>
                                    </StackLayout>
                                </Grid>

                                <Grid Grid.Column="3">
                                    <Image 
                                        Source="arrowForwardLightBlue.png"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"
                                        WidthRequest="6"
                                        HeightRequest="12" />
                                </Grid>
                            </Grid>

                        </Grid>
                    </Frame>
                    
                    
                    <Label 
                        Margin="0,60,0,0"
                        TextColor="#000000"
                        FontAttributes="Bold"
                        FontSize="{x:OnPlatform Android=Large,iOS=19}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="317"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Header}"/>
                    <Label 
                        Margin="0,0,0,0"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="{x:OnPlatform Android=Default,iOS=16}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="307"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.Description}"/>


                </StackLayout>
                
            </Grid>

            <Grid Grid.Row="2">
                <Button 
                    Command="{Binding Source={x:Reference this},Path=GoNext}"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill"
                    Margin="63,0,63,0"
                    HeightRequest="56"
                    FontSize="17"
                    FontAttributes="Bold"
                    Style="{x:StaticResource yellow_btn}"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage2.GoNext}"/>

            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>