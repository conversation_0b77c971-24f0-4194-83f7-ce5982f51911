﻿<?xml version="1.0" encoding="UTF-8"?>
<RadioButton xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             WidthRequest="96"
             HeightRequest="116"
             HorizontalOptions="Center"
             VerticalOptions="Center"
             x:Class="Triggero.MauiMobileApp.Controls.Other.ExerciseEmojiRadioButton">
    <RadioButton.ControlTemplate>
        <ControlTemplate>
            <Frame 
              HasShadow="False"
              CornerRadius="12"
              Padding="0">
                <Frame.Style>
                    <Style TargetType="Frame">
                        <Style.Triggers>
                            <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=IsChecked}" Value="True">
                                <Setter Property="Background" Value="#CEE3F4"/>
                                <Setter Property="BackgroundColor" Value="#CEE3F4"/>
                                <Setter Property="BorderColor" Value="#8D98A0"/>
                            </DataTrigger>
                            <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=IsChecked}" Value="False">
                                <Setter Property="Background" Value="Transparent"/>
                                <Setter Property="BackgroundColor" Value="Transparent"/>
                                <Setter Property="BorderColor" Value="Transparent"/>
                            </DataTrigger>
                        </Style.Triggers>
                    </Style>
                </Frame.Style>
                <Grid>
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Tapped="onTapped"/>
                    </Grid.GestureRecognizers>
                    <StackLayout 
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        Spacing="11">
                        <ImageButton
                            InputTransparent="True"
                            BackgroundColor="Transparent"
                            CornerRadius="0"
                            Source="{Binding Source={x:Reference this},Path=ImgPath}"
                            HeightRequest="68"
                            WidthRequest="68"
                            VerticalOptions="Center"
                            HorizontalOptions="Center"/>
                        <Label 
                            TextColor="#000000"
                            FontSize="12"
                            HorizontalOptions="Center"
                            Text="{Binding Source={x:Reference this},Path=Text}"/>
                    </StackLayout>
                </Grid>
            </Frame>
        </ControlTemplate>
    </RadioButton.ControlTemplate>
</RadioButton>