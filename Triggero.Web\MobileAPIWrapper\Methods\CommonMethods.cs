﻿using Triggero.Domain.Models;
using Triggero.Domain.Models.Enums;
using Newtonsoft.Json;
using Odintcovo.API.Helpers;
using RestSharp;
using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.General;
using Triggero.Models.General.Influence;
using Triggero.Models.Localization;
using Triggero.Models.MoodTracker;
using Triggero.Models.Tests;

namespace MobileAPIWrapper.Methods
{
    public class CommonMethods
    {
        private string BASE_HOST => TriggeroMobileAPI.AddBaseUrl("Common/");

        public string GetDownloadUrl(string filename, ThumbnailSize thumbnailSize, string ext = ".png")
        {
            return BASE_HOST + $"GetImage/{thumbnailSize}/{ext}/{filename}";
        }

        /// <summary>
        /// Установлена ли заглушка (если да, то не показываем юкассу)
        /// </summary>
        /// <returns></returns>
        [Obsolete("Мобильный клиент не использует")]
        public async Task<bool> CheckHui()
        {
            string url = BASE_HOST + "CheckHui";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }

        public async Task<List<UserAvatar>> GetAvatars()
        {
            string url = BASE_HOST + "GetAvatars";
            try
            {
                var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

                var obj = JsonConvert.DeserializeObject<List<UserAvatar>>(response.Content);
                return obj;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }
        }
        public async Task<List<VideoBg>> GetVideoBGs()
        {
            string url = BASE_HOST + "GetVideoBGs";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<VideoBg>>(response.Content);
            return obj;
        }



        public async Task<List<Factor>> GetFactors()
        {
            string url = BASE_HOST + "GetFactors";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Factor>>(response.Content);
            return obj;
        }
        public async Task<List<FactorDetail>> GetFactorDetails()
        {
            string url = BASE_HOST + "GetFactorDetails";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<FactorDetail>>(response.Content);
            return obj;
        }











        public async Task<List<ToDoListItem>> GetToDoListRandom()
        {
            string url = BASE_HOST + $"GetToDoListRandom";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<ToDoListItem>>(str);
            return obj;
        }
        public async Task<List<TagSearchItem>> GetRandomRecomendations()
        {
            string url = BASE_HOST + $"GetRandomRecomendations";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<TagSearchItem>>(str);
            return obj;
        }


        public async Task SetWhatNeedToHandle(int userId, string needToHandleTags)
        {
            string url = BASE_HOST + $"SetWhatNeedToHandle?userId={userId}&needToHandleTags={needToHandleTags}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);

        }
        public async Task<List<NeedToHandle>> GetWhatNeedToHandle(int userId)
        {
            string url = BASE_HOST + $"GetWhatNeedToHandle?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);


            var obj = JsonConvert.DeserializeObject<List<NeedToHandle>>(response.Content);
            return obj;
        }








        public async Task<List<TagSearchItem>> GetAllSearchItems()
        {
            string url = BASE_HOST + $"GetAllSearchItems";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<TagSearchItem>>(str);
            return obj;
        }

        public async Task<List<TagSearchItem>> GetTagSearchItems(string tag)
        {
            string url = BASE_HOST + $"GetTagSearchItems?tag={tag}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);


            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<TagSearchItem>>(str);
            return obj;
        }



        public async Task<List<TagSearchItem>> GetSearchItems(string query)
        {
            string url = BASE_HOST + $"GetSearchItems?query={query}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);


            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<TagSearchItem>>(str);
            return obj;
        }




        public async Task<InterfaceLocalization> GetInterfaceLocalization(string langCode)
        {
            string url = BASE_HOST + $"GetInterfaceLocalization?langCode={langCode}";

            var client = new HttpClient();
            var response = await client.GetAsync(url);
            var content = await response.Content.ReadAsStringAsync();

            //var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<InterfaceLocalization>(content);
            return obj;
        }

        public async Task AddUserDeviceIfNew(int userId, string firebaseToken)
        {
            string url = BASE_HOST + $"AddUserDeviceIfNew?userId={userId}&firebaseToken={firebaseToken}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);
        }
    }
}
