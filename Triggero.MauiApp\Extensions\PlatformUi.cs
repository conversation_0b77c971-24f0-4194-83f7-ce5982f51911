﻿using CommunityToolkit.Maui.Alerts;
using CommunityToolkit.Maui.Core;
using DrawnUi.Models;
using Triggero.MauiMobileApp.Abstractions;

namespace Triggero.MauiMobileApp;

public partial class PlatformUi : IPlatformUi
{

    private static PlatformUi? _instance;
    public static PlatformUi? Instance
    {
        get
        {
            if (_instance == null)
            {
                _instance = new();
            }
            return _instance;
        }
    }

    public Screen Screen { get; } = new();

    //public Screen Screen => Super.Screen;

    static SnackbarOptions snackOptions = new SnackbarOptions
    {
        BackgroundColor = Colors.Black,
        TextColor = Colors.White,
        Font = Microsoft.Maui.Font.OfSize("FontText", 14)
    };

    public void ShowAlert(string text)
    {
        MainThread.BeginInvokeOnMainThread(async () =>
        {
#if WINDOWS
            await App.Current.MainPage.DisplayAlert("Triggero", text, "OK");
#else
            await App.Current.MainPage.DisplaySnackbar(text, null, "", TimeSpan.FromMilliseconds(2500), snackOptions);
#endif
        });
    }

}