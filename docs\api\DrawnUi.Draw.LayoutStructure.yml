### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.LayoutStructure
  commentId: T:DrawnUi.Draw.LayoutStructure
  id: LayoutStructure
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.LayoutStructure.#ctor
  - DrawnUi.Draw.LayoutStructure.#ctor(System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}})
  - DrawnUi.Draw.LayoutStructure.Append(System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}})
  - DrawnUi.Draw.LayoutStructure.Clone
  - DrawnUi.Draw.LayoutStructure.GetForIndex(System.Int32)
  langs:
  - csharp
  - vb
  name: LayoutStructure
  nameWithType: LayoutStructure
  fullName: DrawnUi.Draw.LayoutStructure
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LayoutStructure
    path: ../src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class LayoutStructure : DynamicGrid<ControlInStack>'
    content.vb: Public Class LayoutStructure Inherits DynamicGrid(Of ControlInStack)
  inheritance:
  - System.Object
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  inheritedMembers:
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.grid
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.MaxRows
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.MaxColumns
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Add(DrawnUi.Draw.ControlInStack,System.Int32,System.Int32)
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Get(System.Int32,System.Int32)
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetRow(System.Int32)
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetColumn(System.Int32)
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Clear
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetChildren
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Children
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.FindChildAtIndex(System.Int32)
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Item(System.Int32)
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetChildrenAsSpans
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetCount
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Length
  - DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetColumnCountForRow(System.Int32)
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.LayoutStructure.#ctor
  commentId: M:DrawnUi.Draw.LayoutStructure.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.LayoutStructure
  langs:
  - csharp
  - vb
  name: LayoutStructure()
  nameWithType: LayoutStructure.LayoutStructure()
  fullName: DrawnUi.Draw.LayoutStructure.LayoutStructure()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public LayoutStructure()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.LayoutStructure.#ctor*
  nameWithType.vb: LayoutStructure.New()
  fullName.vb: DrawnUi.Draw.LayoutStructure.New()
  name.vb: New()
- uid: DrawnUi.Draw.LayoutStructure.Clone
  commentId: M:DrawnUi.Draw.LayoutStructure.Clone
  id: Clone
  parent: DrawnUi.Draw.LayoutStructure
  langs:
  - csharp
  - vb
  name: Clone()
  nameWithType: LayoutStructure.Clone()
  fullName: DrawnUi.Draw.LayoutStructure.Clone()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Clone
    path: ../src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Returns a new instance of LayoutStructure with the same items.

    This performs a shallow copy of the existing structure.
  example: []
  syntax:
    content: public LayoutStructure Clone()
    return:
      type: DrawnUi.Draw.LayoutStructure
    content.vb: Public Function Clone() As LayoutStructure
  overload: DrawnUi.Draw.LayoutStructure.Clone*
- uid: DrawnUi.Draw.LayoutStructure.GetForIndex(System.Int32)
  commentId: M:DrawnUi.Draw.LayoutStructure.GetForIndex(System.Int32)
  id: GetForIndex(System.Int32)
  parent: DrawnUi.Draw.LayoutStructure
  langs:
  - csharp
  - vb
  name: GetForIndex(int)
  nameWithType: LayoutStructure.GetForIndex(int)
  fullName: DrawnUi.Draw.LayoutStructure.GetForIndex(int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetForIndex
    path: ../src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ControlInStack GetForIndex(int index)
    parameters:
    - id: index
      type: System.Int32
    return:
      type: DrawnUi.Draw.ControlInStack
    content.vb: Public Function GetForIndex(index As Integer) As ControlInStack
  overload: DrawnUi.Draw.LayoutStructure.GetForIndex*
  nameWithType.vb: LayoutStructure.GetForIndex(Integer)
  fullName.vb: DrawnUi.Draw.LayoutStructure.GetForIndex(Integer)
  name.vb: GetForIndex(Integer)
- uid: DrawnUi.Draw.LayoutStructure.#ctor(System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}})
  commentId: M:DrawnUi.Draw.LayoutStructure.#ctor(System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}})
  id: '#ctor(System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}})'
  parent: DrawnUi.Draw.LayoutStructure
  langs:
  - csharp
  - vb
  name: LayoutStructure(List<List<ControlInStack>>)
  nameWithType: LayoutStructure.LayoutStructure(List<List<ControlInStack>>)
  fullName: DrawnUi.Draw.LayoutStructure.LayoutStructure(System.Collections.Generic.List<System.Collections.Generic.List<DrawnUi.Draw.ControlInStack>>)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
    startLine: 29
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public LayoutStructure(List<List<ControlInStack>> grid)
    parameters:
    - id: grid
      type: System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}}
    content.vb: Public Sub New(grid As List(Of List(Of ControlInStack)))
  overload: DrawnUi.Draw.LayoutStructure.#ctor*
  nameWithType.vb: LayoutStructure.New(List(Of List(Of ControlInStack)))
  fullName.vb: DrawnUi.Draw.LayoutStructure.New(System.Collections.Generic.List(Of System.Collections.Generic.List(Of DrawnUi.Draw.ControlInStack)))
  name.vb: New(List(Of List(Of ControlInStack)))
- uid: DrawnUi.Draw.LayoutStructure.Append(System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}})
  commentId: M:DrawnUi.Draw.LayoutStructure.Append(System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}})
  id: Append(System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}})
  parent: DrawnUi.Draw.LayoutStructure
  langs:
  - csharp
  - vb
  name: Append(List<List<ControlInStack>>)
  nameWithType: LayoutStructure.Append(List<List<ControlInStack>>)
  fullName: DrawnUi.Draw.LayoutStructure.Append(System.Collections.Generic.List<System.Collections.Generic.List<DrawnUi.Draw.ControlInStack>>)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Append
    path: ../src/Maui/DrawnUi/Draw/Layout/LayoutStructure.cs
    startLine: 47
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public void Append(List<List<ControlInStack>> grid)
    parameters:
    - id: grid
      type: System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}}
    content.vb: Public Sub Append(grid As List(Of List(Of ControlInStack)))
  overload: DrawnUi.Draw.LayoutStructure.Append*
  nameWithType.vb: LayoutStructure.Append(List(Of List(Of ControlInStack)))
  fullName.vb: DrawnUi.Draw.LayoutStructure.Append(System.Collections.Generic.List(Of System.Collections.Generic.List(Of DrawnUi.Draw.ControlInStack)))
  name.vb: Append(List(Of List(Of ControlInStack)))
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  commentId: T:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  parent: DrawnUi.Draw
  definition: DrawnUi.Draw.DynamicGrid`1
  href: DrawnUi.Draw.DynamicGrid-1.html
  name: DynamicGrid<ControlInStack>
  nameWithType: DynamicGrid<ControlInStack>
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>
  nameWithType.vb: DynamicGrid(Of ControlInStack)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack)
  name.vb: DynamicGrid(Of ControlInStack)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1
    name: DynamicGrid
    href: DrawnUi.Draw.DynamicGrid-1.html
  - name: <
  - uid: DrawnUi.Draw.ControlInStack
    name: ControlInStack
    href: DrawnUi.Draw.ControlInStack.html
  - name: '>'
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1
    name: DynamicGrid
    href: DrawnUi.Draw.DynamicGrid-1.html
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.ControlInStack
    name: ControlInStack
    href: DrawnUi.Draw.ControlInStack.html
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.grid
  commentId: F:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.grid
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.grid
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_grid
  name: grid
  nameWithType: DynamicGrid<ControlInStack>.grid
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.grid
  nameWithType.vb: DynamicGrid(Of ControlInStack).grid
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).grid
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.MaxRows
  commentId: P:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.MaxRows
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.MaxRows
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_MaxRows
  name: MaxRows
  nameWithType: DynamicGrid<ControlInStack>.MaxRows
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.MaxRows
  nameWithType.vb: DynamicGrid(Of ControlInStack).MaxRows
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).MaxRows
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.MaxColumns
  commentId: P:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.MaxColumns
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.MaxColumns
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_MaxColumns
  name: MaxColumns
  nameWithType: DynamicGrid<ControlInStack>.MaxColumns
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.MaxColumns
  nameWithType.vb: DynamicGrid(Of ControlInStack).MaxColumns
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).MaxColumns
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Add(DrawnUi.Draw.ControlInStack,System.Int32,System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Add(DrawnUi.Draw.ControlInStack,System.Int32,System.Int32)
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.Add(`0,System.Int32,System.Int32)
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Add__0_System_Int32_System_Int32_
  name: Add(ControlInStack, int, int)
  nameWithType: DynamicGrid<ControlInStack>.Add(ControlInStack, int, int)
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.Add(DrawnUi.Draw.ControlInStack, int, int)
  nameWithType.vb: DynamicGrid(Of ControlInStack).Add(ControlInStack, Integer, Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).Add(DrawnUi.Draw.ControlInStack, Integer, Integer)
  name.vb: Add(ControlInStack, Integer, Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Add(DrawnUi.Draw.ControlInStack,System.Int32,System.Int32)
    name: Add
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Add__0_System_Int32_System_Int32_
  - name: (
  - uid: DrawnUi.Draw.ControlInStack
    name: ControlInStack
    href: DrawnUi.Draw.ControlInStack.html
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Add(DrawnUi.Draw.ControlInStack,System.Int32,System.Int32)
    name: Add
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Add__0_System_Int32_System_Int32_
  - name: (
  - uid: DrawnUi.Draw.ControlInStack
    name: ControlInStack
    href: DrawnUi.Draw.ControlInStack.html
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Get(System.Int32,System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Get(System.Int32,System.Int32)
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.Get(System.Int32,System.Int32)
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Get_System_Int32_System_Int32_
  name: Get(int, int)
  nameWithType: DynamicGrid<ControlInStack>.Get(int, int)
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.Get(int, int)
  nameWithType.vb: DynamicGrid(Of ControlInStack).Get(Integer, Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).Get(Integer, Integer)
  name.vb: Get(Integer, Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Get(System.Int32,System.Int32)
    name: Get
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Get_System_Int32_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Get(System.Int32,System.Int32)
    name: Get
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Get_System_Int32_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetRow(System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetRow(System.Int32)
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.GetRow(System.Int32)
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetRow_System_Int32_
  name: GetRow(int)
  nameWithType: DynamicGrid<ControlInStack>.GetRow(int)
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.GetRow(int)
  nameWithType.vb: DynamicGrid(Of ControlInStack).GetRow(Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).GetRow(Integer)
  name.vb: GetRow(Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetRow(System.Int32)
    name: GetRow
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetRow_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetRow(System.Int32)
    name: GetRow
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetRow_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetColumn(System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetColumn(System.Int32)
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.GetColumn(System.Int32)
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumn_System_Int32_
  name: GetColumn(int)
  nameWithType: DynamicGrid<ControlInStack>.GetColumn(int)
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.GetColumn(int)
  nameWithType.vb: DynamicGrid(Of ControlInStack).GetColumn(Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).GetColumn(Integer)
  name.vb: GetColumn(Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetColumn(System.Int32)
    name: GetColumn
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumn_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetColumn(System.Int32)
    name: GetColumn
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumn_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Clear
  commentId: M:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Clear
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.Clear
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Clear
  name: Clear()
  nameWithType: DynamicGrid<ControlInStack>.Clear()
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.Clear()
  nameWithType.vb: DynamicGrid(Of ControlInStack).Clear()
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).Clear()
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Clear
    name: Clear
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Clear
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Clear
    name: Clear
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Clear
  - name: (
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetChildren
  commentId: M:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetChildren
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.GetChildren
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildren
  name: GetChildren()
  nameWithType: DynamicGrid<ControlInStack>.GetChildren()
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.GetChildren()
  nameWithType.vb: DynamicGrid(Of ControlInStack).GetChildren()
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).GetChildren()
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetChildren
    name: GetChildren
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildren
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetChildren
    name: GetChildren
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildren
  - name: (
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Children
  commentId: P:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Children
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.Children
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Children
  name: Children
  nameWithType: DynamicGrid<ControlInStack>.Children
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.Children
  nameWithType.vb: DynamicGrid(Of ControlInStack).Children
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).Children
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.FindChildAtIndex(System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.FindChildAtIndex(System.Int32)
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.FindChildAtIndex(System.Int32)
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_FindChildAtIndex_System_Int32_
  name: FindChildAtIndex(int)
  nameWithType: DynamicGrid<ControlInStack>.FindChildAtIndex(int)
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.FindChildAtIndex(int)
  nameWithType.vb: DynamicGrid(Of ControlInStack).FindChildAtIndex(Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).FindChildAtIndex(Integer)
  name.vb: FindChildAtIndex(Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.FindChildAtIndex(System.Int32)
    name: FindChildAtIndex
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_FindChildAtIndex_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.FindChildAtIndex(System.Int32)
    name: FindChildAtIndex
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_FindChildAtIndex_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Item(System.Int32)
  commentId: P:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Item(System.Int32)
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.Item(System.Int32)
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: this[int]
  nameWithType: DynamicGrid<ControlInStack>.this[int]
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.this[int]
  nameWithType.vb: DynamicGrid(Of ControlInStack).this[](Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).this[](Integer)
  name.vb: this[](Integer)
  spec.csharp:
  - name: this
  - name: '['
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ']'
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Item(System.Int32)
    name: this[]
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Item_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetChildrenAsSpans
  commentId: M:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetChildrenAsSpans
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.GetChildrenAsSpans
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildrenAsSpans
  name: GetChildrenAsSpans()
  nameWithType: DynamicGrid<ControlInStack>.GetChildrenAsSpans()
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.GetChildrenAsSpans()
  nameWithType.vb: DynamicGrid(Of ControlInStack).GetChildrenAsSpans()
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).GetChildrenAsSpans()
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetChildrenAsSpans
    name: GetChildrenAsSpans
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildrenAsSpans
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetChildrenAsSpans
    name: GetChildrenAsSpans
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildrenAsSpans
  - name: (
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetCount
  commentId: M:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetCount
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.GetCount
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetCount
  name: GetCount()
  nameWithType: DynamicGrid<ControlInStack>.GetCount()
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.GetCount()
  nameWithType.vb: DynamicGrid(Of ControlInStack).GetCount()
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).GetCount()
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetCount
    name: GetCount
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetCount
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetCount
    name: GetCount
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetCount
  - name: (
  - name: )
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Length
  commentId: P:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.Length
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.Length
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Length
  name: Length
  nameWithType: DynamicGrid<ControlInStack>.Length
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.Length
  nameWithType.vb: DynamicGrid(Of ControlInStack).Length
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).Length
- uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetColumnCountForRow(System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetColumnCountForRow(System.Int32)
  parent: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}
  definition: DrawnUi.Draw.DynamicGrid`1.GetColumnCountForRow(System.Int32)
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumnCountForRow_System_Int32_
  name: GetColumnCountForRow(int)
  nameWithType: DynamicGrid<ControlInStack>.GetColumnCountForRow(int)
  fullName: DrawnUi.Draw.DynamicGrid<DrawnUi.Draw.ControlInStack>.GetColumnCountForRow(int)
  nameWithType.vb: DynamicGrid(Of ControlInStack).GetColumnCountForRow(Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of DrawnUi.Draw.ControlInStack).GetColumnCountForRow(Integer)
  name.vb: GetColumnCountForRow(Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetColumnCountForRow(System.Int32)
    name: GetColumnCountForRow
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumnCountForRow_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid{DrawnUi.Draw.ControlInStack}.GetColumnCountForRow(System.Int32)
    name: GetColumnCountForRow
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumnCountForRow_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.DynamicGrid`1
  commentId: T:DrawnUi.Draw.DynamicGrid`1
  href: DrawnUi.Draw.DynamicGrid-1.html
  name: DynamicGrid<T>
  nameWithType: DynamicGrid<T>
  fullName: DrawnUi.Draw.DynamicGrid<T>
  nameWithType.vb: DynamicGrid(Of T)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T)
  name.vb: DynamicGrid(Of T)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1
    name: DynamicGrid
    href: DrawnUi.Draw.DynamicGrid-1.html
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1
    name: DynamicGrid
    href: DrawnUi.Draw.DynamicGrid-1.html
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.grid
  commentId: F:DrawnUi.Draw.DynamicGrid`1.grid
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_grid
  name: grid
  nameWithType: DynamicGrid<T>.grid
  fullName: DrawnUi.Draw.DynamicGrid<T>.grid
  nameWithType.vb: DynamicGrid(Of T).grid
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).grid
- uid: DrawnUi.Draw.DynamicGrid`1.MaxRows
  commentId: P:DrawnUi.Draw.DynamicGrid`1.MaxRows
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_MaxRows
  name: MaxRows
  nameWithType: DynamicGrid<T>.MaxRows
  fullName: DrawnUi.Draw.DynamicGrid<T>.MaxRows
  nameWithType.vb: DynamicGrid(Of T).MaxRows
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).MaxRows
- uid: DrawnUi.Draw.DynamicGrid`1.MaxColumns
  commentId: P:DrawnUi.Draw.DynamicGrid`1.MaxColumns
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_MaxColumns
  name: MaxColumns
  nameWithType: DynamicGrid<T>.MaxColumns
  fullName: DrawnUi.Draw.DynamicGrid<T>.MaxColumns
  nameWithType.vb: DynamicGrid(Of T).MaxColumns
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).MaxColumns
- uid: DrawnUi.Draw.DynamicGrid`1.Add(`0,System.Int32,System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid`1.Add(`0,System.Int32,System.Int32)
  isExternal: true
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Add__0_System_Int32_System_Int32_
  name: Add(T, int, int)
  nameWithType: DynamicGrid<T>.Add(T, int, int)
  fullName: DrawnUi.Draw.DynamicGrid<T>.Add(T, int, int)
  nameWithType.vb: DynamicGrid(Of T).Add(T, Integer, Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).Add(T, Integer, Integer)
  name.vb: Add(T, Integer, Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1.Add(`0,System.Int32,System.Int32)
    name: Add
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Add__0_System_Int32_System_Int32_
  - name: (
  - name: T
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.Add(`0,System.Int32,System.Int32)
    name: Add
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Add__0_System_Int32_System_Int32_
  - name: (
  - name: T
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.Get(System.Int32,System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid`1.Get(System.Int32,System.Int32)
  isExternal: true
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Get_System_Int32_System_Int32_
  name: Get(int, int)
  nameWithType: DynamicGrid<T>.Get(int, int)
  fullName: DrawnUi.Draw.DynamicGrid<T>.Get(int, int)
  nameWithType.vb: DynamicGrid(Of T).Get(Integer, Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).Get(Integer, Integer)
  name.vb: Get(Integer, Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1.Get(System.Int32,System.Int32)
    name: Get
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Get_System_Int32_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.Get(System.Int32,System.Int32)
    name: Get
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Get_System_Int32_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.GetRow(System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid`1.GetRow(System.Int32)
  isExternal: true
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetRow_System_Int32_
  name: GetRow(int)
  nameWithType: DynamicGrid<T>.GetRow(int)
  fullName: DrawnUi.Draw.DynamicGrid<T>.GetRow(int)
  nameWithType.vb: DynamicGrid(Of T).GetRow(Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).GetRow(Integer)
  name.vb: GetRow(Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetRow(System.Int32)
    name: GetRow
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetRow_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetRow(System.Int32)
    name: GetRow
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetRow_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.GetColumn(System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid`1.GetColumn(System.Int32)
  isExternal: true
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumn_System_Int32_
  name: GetColumn(int)
  nameWithType: DynamicGrid<T>.GetColumn(int)
  fullName: DrawnUi.Draw.DynamicGrid<T>.GetColumn(int)
  nameWithType.vb: DynamicGrid(Of T).GetColumn(Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).GetColumn(Integer)
  name.vb: GetColumn(Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetColumn(System.Int32)
    name: GetColumn
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumn_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetColumn(System.Int32)
    name: GetColumn
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumn_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.Clear
  commentId: M:DrawnUi.Draw.DynamicGrid`1.Clear
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Clear
  name: Clear()
  nameWithType: DynamicGrid<T>.Clear()
  fullName: DrawnUi.Draw.DynamicGrid<T>.Clear()
  nameWithType.vb: DynamicGrid(Of T).Clear()
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).Clear()
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1.Clear
    name: Clear
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Clear
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.Clear
    name: Clear
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Clear
  - name: (
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.GetChildren
  commentId: M:DrawnUi.Draw.DynamicGrid`1.GetChildren
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildren
  name: GetChildren()
  nameWithType: DynamicGrid<T>.GetChildren()
  fullName: DrawnUi.Draw.DynamicGrid<T>.GetChildren()
  nameWithType.vb: DynamicGrid(Of T).GetChildren()
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).GetChildren()
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetChildren
    name: GetChildren
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildren
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetChildren
    name: GetChildren
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildren
  - name: (
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.Children
  commentId: P:DrawnUi.Draw.DynamicGrid`1.Children
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Children
  name: Children
  nameWithType: DynamicGrid<T>.Children
  fullName: DrawnUi.Draw.DynamicGrid<T>.Children
  nameWithType.vb: DynamicGrid(Of T).Children
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).Children
- uid: DrawnUi.Draw.DynamicGrid`1.FindChildAtIndex(System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid`1.FindChildAtIndex(System.Int32)
  isExternal: true
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_FindChildAtIndex_System_Int32_
  name: FindChildAtIndex(int)
  nameWithType: DynamicGrid<T>.FindChildAtIndex(int)
  fullName: DrawnUi.Draw.DynamicGrid<T>.FindChildAtIndex(int)
  nameWithType.vb: DynamicGrid(Of T).FindChildAtIndex(Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).FindChildAtIndex(Integer)
  name.vb: FindChildAtIndex(Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1.FindChildAtIndex(System.Int32)
    name: FindChildAtIndex
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_FindChildAtIndex_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.FindChildAtIndex(System.Int32)
    name: FindChildAtIndex
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_FindChildAtIndex_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.Item(System.Int32)
  commentId: P:DrawnUi.Draw.DynamicGrid`1.Item(System.Int32)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: this[int]
  nameWithType: DynamicGrid<T>.this[int]
  fullName: DrawnUi.Draw.DynamicGrid<T>.this[int]
  nameWithType.vb: DynamicGrid(Of T).this[](Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).this[](Integer)
  name.vb: this[](Integer)
  spec.csharp:
  - name: this
  - name: '['
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ']'
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.Item(System.Int32)
    name: this[]
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Item_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.GetChildrenAsSpans
  commentId: M:DrawnUi.Draw.DynamicGrid`1.GetChildrenAsSpans
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildrenAsSpans
  name: GetChildrenAsSpans()
  nameWithType: DynamicGrid<T>.GetChildrenAsSpans()
  fullName: DrawnUi.Draw.DynamicGrid<T>.GetChildrenAsSpans()
  nameWithType.vb: DynamicGrid(Of T).GetChildrenAsSpans()
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).GetChildrenAsSpans()
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetChildrenAsSpans
    name: GetChildrenAsSpans
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildrenAsSpans
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetChildrenAsSpans
    name: GetChildrenAsSpans
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetChildrenAsSpans
  - name: (
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.GetCount
  commentId: M:DrawnUi.Draw.DynamicGrid`1.GetCount
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetCount
  name: GetCount()
  nameWithType: DynamicGrid<T>.GetCount()
  fullName: DrawnUi.Draw.DynamicGrid<T>.GetCount()
  nameWithType.vb: DynamicGrid(Of T).GetCount()
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).GetCount()
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetCount
    name: GetCount
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetCount
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetCount
    name: GetCount
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetCount
  - name: (
  - name: )
- uid: DrawnUi.Draw.DynamicGrid`1.Length
  commentId: P:DrawnUi.Draw.DynamicGrid`1.Length
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_Length
  name: Length
  nameWithType: DynamicGrid<T>.Length
  fullName: DrawnUi.Draw.DynamicGrid<T>.Length
  nameWithType.vb: DynamicGrid(Of T).Length
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).Length
- uid: DrawnUi.Draw.DynamicGrid`1.GetColumnCountForRow(System.Int32)
  commentId: M:DrawnUi.Draw.DynamicGrid`1.GetColumnCountForRow(System.Int32)
  isExternal: true
  href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumnCountForRow_System_Int32_
  name: GetColumnCountForRow(int)
  nameWithType: DynamicGrid<T>.GetColumnCountForRow(int)
  fullName: DrawnUi.Draw.DynamicGrid<T>.GetColumnCountForRow(int)
  nameWithType.vb: DynamicGrid(Of T).GetColumnCountForRow(Integer)
  fullName.vb: DrawnUi.Draw.DynamicGrid(Of T).GetColumnCountForRow(Integer)
  name.vb: GetColumnCountForRow(Integer)
  spec.csharp:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetColumnCountForRow(System.Int32)
    name: GetColumnCountForRow
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumnCountForRow_System_Int32_
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.DynamicGrid`1.GetColumnCountForRow(System.Int32)
    name: GetColumnCountForRow
    href: DrawnUi.Draw.DynamicGrid-1.html#DrawnUi_Draw_DynamicGrid_1_GetColumnCountForRow_System_Int32_
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.LayoutStructure.#ctor*
  commentId: Overload:DrawnUi.Draw.LayoutStructure.#ctor
  href: DrawnUi.Draw.LayoutStructure.html#DrawnUi_Draw_LayoutStructure__ctor
  name: LayoutStructure
  nameWithType: LayoutStructure.LayoutStructure
  fullName: DrawnUi.Draw.LayoutStructure.LayoutStructure
  nameWithType.vb: LayoutStructure.New
  fullName.vb: DrawnUi.Draw.LayoutStructure.New
  name.vb: New
- uid: DrawnUi.Draw.LayoutStructure.Clone*
  commentId: Overload:DrawnUi.Draw.LayoutStructure.Clone
  href: DrawnUi.Draw.LayoutStructure.html#DrawnUi_Draw_LayoutStructure_Clone
  name: Clone
  nameWithType: LayoutStructure.Clone
  fullName: DrawnUi.Draw.LayoutStructure.Clone
- uid: DrawnUi.Draw.LayoutStructure
  commentId: T:DrawnUi.Draw.LayoutStructure
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LayoutStructure.html
  name: LayoutStructure
  nameWithType: LayoutStructure
  fullName: DrawnUi.Draw.LayoutStructure
- uid: DrawnUi.Draw.LayoutStructure.GetForIndex*
  commentId: Overload:DrawnUi.Draw.LayoutStructure.GetForIndex
  href: DrawnUi.Draw.LayoutStructure.html#DrawnUi_Draw_LayoutStructure_GetForIndex_System_Int32_
  name: GetForIndex
  nameWithType: LayoutStructure.GetForIndex
  fullName: DrawnUi.Draw.LayoutStructure.GetForIndex
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.ControlInStack
  commentId: T:DrawnUi.Draw.ControlInStack
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ControlInStack.html
  name: ControlInStack
  nameWithType: ControlInStack
  fullName: DrawnUi.Draw.ControlInStack
- uid: System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}}
  commentId: T:System.Collections.Generic.List{System.Collections.Generic.List{DrawnUi.Draw.ControlInStack}}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<List<ControlInStack>>
  nameWithType: List<List<ControlInStack>>
  fullName: System.Collections.Generic.List<System.Collections.Generic.List<DrawnUi.Draw.ControlInStack>>
  nameWithType.vb: List(Of List(Of ControlInStack))
  fullName.vb: System.Collections.Generic.List(Of System.Collections.Generic.List(Of DrawnUi.Draw.ControlInStack))
  name.vb: List(Of List(Of ControlInStack))
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Draw.ControlInStack
    name: ControlInStack
    href: DrawnUi.Draw.ControlInStack.html
  - name: '>'
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.ControlInStack
    name: ControlInStack
    href: DrawnUi.Draw.ControlInStack.html
  - name: )
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Draw.LayoutStructure.Append*
  commentId: Overload:DrawnUi.Draw.LayoutStructure.Append
  href: DrawnUi.Draw.LayoutStructure.html#DrawnUi_Draw_LayoutStructure_Append_System_Collections_Generic_List_System_Collections_Generic_List_DrawnUi_Draw_ControlInStack___
  name: Append
  nameWithType: LayoutStructure.Append
  fullName: DrawnUi.Draw.LayoutStructure.Append
