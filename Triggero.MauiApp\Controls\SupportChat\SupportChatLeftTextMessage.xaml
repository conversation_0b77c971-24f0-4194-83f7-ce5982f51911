﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Chat.SupportChatLeftTextMessage"
             x:Name="this">
  <ContentView.Content>
      <Frame
          CornerRadius="12"
          BackgroundColor="#EEF5FB"
          HasShadow="False"
          Padding="0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="50"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <Label 
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="{x:OnPlatform Android=Default,iOS=16}"
                        Margin="20,8,0,8"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Reference this},Path=Message.Text}"/>
                </Grid>

                <Grid Grid.Column="1">
                    <Label 
                        x:Name="timeLabel"
                        TextColor="{x:StaticResource ColorTextGray}"

                        FontSize="12"
                        Margin="0,12,0,8"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        Text=""/>
                </Grid>

            </Grid>
      </Frame>
  </ContentView.Content>
</ContentView>