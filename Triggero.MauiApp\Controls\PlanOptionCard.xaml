﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:buttons="clr-namespace:Syncfusion.Maui.Buttons;assembly=Syncfusion.Maui.Buttons"
             x:Name="this"
             xmlns:app="clr-namespace:Triggero"
             xmlns:controls="clr-namespace:Triggero.Controls;assembly=Triggero.MauiMobileApp"
             xmlns:controls1="clr-namespace:Triggero.Controls"
             x:Class="Triggero.Controls.PlanOptionCard">
    <ContentView.Content>
        <Grid>
            <StackLayout
                HorizontalOptions="Start">
                <Label 
                    x:Name="titleLabel"
                    TextColor="{x:StaticResource greyTextColor}"
                    FontSize="17"
                    FontAttributes="Bold"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    Text=""/>
                <Label 
                    x:Name="descriptionLabel"
                    Opacity="0.5"
                    TextColor="{x:StaticResource greyTextColor}"
                    FontSize="14"
                    WidthRequest="225"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    Text=""/>
            </StackLayout>
            <StackLayout
                HorizontalOptions="End">
                <buttons:SfSwitch 
                    x:Name="MainSwitch"
                    x:DataType="controls1:PlanOptionCard"
                    IsOn="{Binding Source={x:Reference this},Path=IsSelected,Mode=TwoWay}"
                    Style="{x:StaticResource sf_switch_yellow}"
                    HorizontalOptions="End"
                    VerticalOptions="Start"
                    WidthRequest="51"
                    HeightRequest="31"/>
                <Label 
                    TextColor="{x:StaticResource greyTextColor}"
                    FontSize="17"
                    FontAttributes="Bold"
                    VerticalOptions="Center"
                    HorizontalOptions="End">
                    <Label.FormattedText>
                        <FormattedString>
                            <FormattedString.Spans>
                                <Span x:Name="priceSpan" Text=""/>
                                <Span Text=" р."/>
                            </FormattedString.Spans>
                        </FormattedString>
                    </Label.FormattedText>
                </Label>
            </StackLayout>
            <BoxView
                Opacity="0.5"
                BackgroundColor="{x:StaticResource lightBlueColor}"
                VerticalOptions="End"
                HeightRequest="1"/>
        </Grid>
    </ContentView.Content>
</ContentView>