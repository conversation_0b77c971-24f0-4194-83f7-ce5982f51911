### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo
  commentId: T:DrawnUi.Draw.SkiaLayout.MeasuredItemInfo
  id: SkiaLayout.MeasuredItemInfo
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.Cell
  - DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.IsInViewport
  - DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.LastAccessed
  langs:
  - csharp
  - vb
  name: SkiaLayout.MeasuredItemInfo
  nameWithType: SkiaLayout.MeasuredItemInfo
  fullName: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasuredItemInfo
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 176
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Information about a measured item for sliding window management
  example: []
  syntax:
    content: public class SkiaLayout.MeasuredItemInfo
    content.vb: Public Class SkiaLayout.MeasuredItemInfo
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.Cell
  commentId: P:DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.Cell
  id: Cell
  parent: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo
  langs:
  - csharp
  - vb
  name: Cell
  nameWithType: SkiaLayout.MeasuredItemInfo.Cell
  fullName: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.Cell
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Cell
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 178
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ControlInStack Cell { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.ControlInStack
    content.vb: Public Property Cell As ControlInStack
  overload: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.Cell*
- uid: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.LastAccessed
  commentId: P:DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.LastAccessed
  id: LastAccessed
  parent: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo
  langs:
  - csharp
  - vb
  name: LastAccessed
  nameWithType: SkiaLayout.MeasuredItemInfo.LastAccessed
  fullName: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.LastAccessed
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LastAccessed
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 179
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public DateTime LastAccessed { get; set; }
    parameters: []
    return:
      type: System.DateTime
    content.vb: Public Property LastAccessed As Date
  overload: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.LastAccessed*
- uid: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.IsInViewport
  commentId: P:DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.IsInViewport
  id: IsInViewport
  parent: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo
  langs:
  - csharp
  - vb
  name: IsInViewport
  nameWithType: SkiaLayout.MeasuredItemInfo.IsInViewport
  fullName: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.IsInViewport
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsInViewport
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 180
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsInViewport { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsInViewport As Boolean
  overload: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.IsInViewport*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.Cell*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.Cell
  href: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.html#DrawnUi_Draw_SkiaLayout_MeasuredItemInfo_Cell
  name: Cell
  nameWithType: SkiaLayout.MeasuredItemInfo.Cell
  fullName: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.Cell
- uid: DrawnUi.Draw.ControlInStack
  commentId: T:DrawnUi.Draw.ControlInStack
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ControlInStack.html
  name: ControlInStack
  nameWithType: ControlInStack
  fullName: DrawnUi.Draw.ControlInStack
- uid: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.LastAccessed*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.LastAccessed
  href: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.html#DrawnUi_Draw_SkiaLayout_MeasuredItemInfo_LastAccessed
  name: LastAccessed
  nameWithType: SkiaLayout.MeasuredItemInfo.LastAccessed
  fullName: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.LastAccessed
- uid: System.DateTime
  commentId: T:System.DateTime
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.datetime
  name: DateTime
  nameWithType: DateTime
  fullName: System.DateTime
  nameWithType.vb: Date
  fullName.vb: Date
  name.vb: Date
- uid: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.IsInViewport*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.IsInViewport
  href: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.html#DrawnUi_Draw_SkiaLayout_MeasuredItemInfo_IsInViewport
  name: IsInViewport
  nameWithType: SkiaLayout.MeasuredItemInfo.IsInViewport
  fullName: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.IsInViewport
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
