### YamlMime:ManagedReference
items:
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  commentId: T:DrawnUi.Animate.Animators.VelocitySkiaAnimator
  id: VelocitySkiaAnimator
  parent: DrawnUi.Animate.Animators
  children:
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.#ctor(DrawnUi.Draw.SkiaControl)
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.ClampOnStart
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.Friction
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.InverseK
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.InvertOnLimits
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ALPHA
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_PIXELS
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ROTATION_DEGREES
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_SCALE
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.MaxLimit
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.MinLimit
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.Preset
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.RemainingVelocity
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.Scale
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction(System.Single)
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity(System.Single)
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.Stop
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.THRESHOLD_MULTIPLIER
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.UNSET
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue(System.Int64,System.Int64)
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.mFlingForce
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMassState
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxOverscrollValue
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxVelocity
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinOverscrollValue
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVelocity
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVisibleChange
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVelocity
  - DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVerticalVelocityChange
  langs:
  - csharp
  - vb
  name: VelocitySkiaAnimator
  nameWithType: VelocitySkiaAnimator
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  type: Class
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: VelocitySkiaAnimator
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 27
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  summary: Basically a modified port of Android FlingAnimation
  example: []
  syntax:
    content: 'public class VelocitySkiaAnimator : SkiaValueAnimator, ISkiaAnimator, IDisposable'
    content.vb: Public Class VelocitySkiaAnimator Inherits SkiaValueAnimator Implements ISkiaAnimator, IDisposable
  inheritance:
  - System.Object
  - DrawnUi.Draw.AnimatorBase
  - DrawnUi.Draw.SkiaValueAnimator
  implements:
  - DrawnUi.Draw.ISkiaAnimator
  - System.IDisposable
  inheritedMembers:
  - DrawnUi.Draw.SkiaValueAnimator.Dispose
  - DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
  - DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
  - DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
  - DrawnUi.Draw.SkiaValueAnimator.CycleFInished
  - DrawnUi.Draw.SkiaValueAnimator.Finished
  - DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
  - DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
  - DrawnUi.Draw.SkiaValueAnimator.UseInterpolator
  - DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
  - DrawnUi.Draw.SkiaValueAnimator.Repeat
  - DrawnUi.Draw.SkiaValueAnimator.mValue
  - DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
  - DrawnUi.Draw.SkiaValueAnimator.mMaxValue
  - DrawnUi.Draw.SkiaValueAnimator.mMinValue
  - DrawnUi.Draw.SkiaValueAnimator.Easing
  - DrawnUi.Draw.SkiaValueAnimator.Speed
  - DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(System.Int64)
  - DrawnUi.Draw.SkiaValueAnimator.Debug
  - DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
  - DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
  - DrawnUi.Draw.SkiaValueAnimator.Progress
  - DrawnUi.Draw.SkiaValueAnimator.OnUpdated
  - DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
  - DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
  - DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
  - DrawnUi.Draw.AnimatorBase.IsPostAnimator
  - DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
  - DrawnUi.Draw.AnimatorBase.Radians(System.Double)
  - DrawnUi.Draw.AnimatorBase.runDelayMs
  - DrawnUi.Draw.AnimatorBase.Register
  - DrawnUi.Draw.AnimatorBase.Unregister
  - DrawnUi.Draw.AnimatorBase.Cancel
  - DrawnUi.Draw.AnimatorBase.Pause
  - DrawnUi.Draw.AnimatorBase.Resume
  - DrawnUi.Draw.AnimatorBase.IsPaused
  - DrawnUi.Draw.AnimatorBase.OnStop
  - DrawnUi.Draw.AnimatorBase.OnStart
  - DrawnUi.Draw.AnimatorBase.Parent
  - DrawnUi.Draw.AnimatorBase.IsDeactivated
  - DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
  - DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
  - DrawnUi.Draw.AnimatorBase.Uid
  - DrawnUi.Draw.AnimatorBase.IsRunning
  - DrawnUi.Draw.AnimatorBase.WasStarted
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinOverscrollValue
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinOverscrollValue
  id: mMinOverscrollValue
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: mMinOverscrollValue
  nameWithType: VelocitySkiaAnimator.mMinOverscrollValue
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinOverscrollValue
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: mMinOverscrollValue
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 32
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  summary: Must be over 0
  example: []
  syntax:
    content: public float mMinOverscrollValue { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property mMinOverscrollValue As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinOverscrollValue*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxOverscrollValue
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxOverscrollValue
  id: mMaxOverscrollValue
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: mMaxOverscrollValue
  nameWithType: VelocitySkiaAnimator.mMaxOverscrollValue
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxOverscrollValue
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: mMaxOverscrollValue
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 37
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  summary: Must be over 0
  example: []
  syntax:
    content: public float mMaxOverscrollValue { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property mMaxOverscrollValue As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxOverscrollValue*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MinLimit
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.MinLimit
  id: MinLimit
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: MinLimit
  nameWithType: VelocitySkiaAnimator.MinLimit
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MinLimit
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MinLimit
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 39
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float MinLimit { get; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public ReadOnly Property MinLimit As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MinLimit*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MaxLimit
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.MaxLimit
  id: MaxLimit
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: MaxLimit
  nameWithType: VelocitySkiaAnimator.MaxLimit
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MaxLimit
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MaxLimit
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 47
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float MaxLimit { get; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public ReadOnly Property MaxLimit As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MaxLimit*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan
  id: SnapBouncingIfVelocityLessThan
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: SnapBouncingIfVelocityLessThan
  nameWithType: VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SnapBouncingIfVelocityLessThan
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 56
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float SnapBouncingIfVelocityLessThan { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property SnapBouncingIfVelocityLessThan As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.InvertOnLimits
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.InvertOnLimits
  id: InvertOnLimits
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: InvertOnLimits
  nameWithType: VelocitySkiaAnimator.InvertOnLimits
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.InvertOnLimits
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: InvertOnLimits
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 57
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public bool InvertOnLimits { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property InvertOnLimits As Boolean
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.InvertOnLimits*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVelocity
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVelocity
  id: mVelocity
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: mVelocity
  nameWithType: VelocitySkiaAnimator.mVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVelocity
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: mVelocity
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 58
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float mVelocity { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property mVelocity As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVelocity*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxVelocity
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxVelocity
  id: mMaxVelocity
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: mMaxVelocity
  nameWithType: VelocitySkiaAnimator.mMaxVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxVelocity
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: mMaxVelocity
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 60
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float mMaxVelocity { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property mMaxVelocity As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxVelocity*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVelocity
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVelocity
  id: mMinVelocity
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: mMinVelocity
  nameWithType: VelocitySkiaAnimator.mMinVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVelocity
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: mMinVelocity
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 62
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float mMinVelocity { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property mMinVelocity As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVelocity*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVerticalVelocityChange
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVerticalVelocityChange
  id: mVerticalVelocityChange
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: mVerticalVelocityChange
  nameWithType: VelocitySkiaAnimator.mVerticalVelocityChange
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVerticalVelocityChange
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: mVerticalVelocityChange
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 64
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float mVerticalVelocityChange { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property mVerticalVelocityChange As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVerticalVelocityChange*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Stop
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.Stop
  id: Stop
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: Stop()
  nameWithType: VelocitySkiaAnimator.Stop()
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Stop()
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Stop
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 70
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  example: []
  syntax:
    content: public override void Stop()
    content.vb: Public Overrides Sub [Stop]()
  overridden: DrawnUi.Draw.SkiaValueAnimator.Stop
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Stop*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.ClampOnStart
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.ClampOnStart
  id: ClampOnStart
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: ClampOnStart()
  nameWithType: VelocitySkiaAnimator.ClampOnStart()
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.ClampOnStart()
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ClampOnStart
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 77
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  example: []
  syntax:
    content: protected override void ClampOnStart()
    content.vb: Protected Overrides Sub ClampOnStart()
  overridden: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.ClampOnStart*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.InverseK
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.InverseK
  id: InverseK
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: InverseK
  nameWithType: VelocitySkiaAnimator.InverseK
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.InverseK
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: InverseK
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 90
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float InverseK { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property InverseK As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.InverseK*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue(System.Int64,System.Int64)
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue(System.Int64,System.Int64)
  id: UpdateValue(System.Int64,System.Int64)
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: UpdateValue(long, long)
  nameWithType: VelocitySkiaAnimator.UpdateValue(long, long)
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue(long, long)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UpdateValue
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 92
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  summary: >-
    Update mValue using time distance between rendered frames.

    Return true if anims is finished.
  example: []
  syntax:
    content: protected override bool UpdateValue(long deltaT, long deltaFromStart)
    parameters:
    - id: deltaT
      type: System.Int64
      description: ''
    - id: deltaFromStart
      type: System.Int64
    return:
      type: System.Boolean
      description: ''
    content.vb: Protected Overrides Function UpdateValue(deltaT As Long, deltaFromStart As Long) As Boolean
  overridden: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue*
  nameWithType.vb: VelocitySkiaAnimator.UpdateValue(Long, Long)
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue(Long, Long)
  name.vb: UpdateValue(Long, Long)
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity(System.Single)
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity(System.Single)
  id: SetVelocity(System.Single)
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: SetVelocity(float)
  nameWithType: VelocitySkiaAnimator.SetVelocity(float)
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity(float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetVelocity
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 242
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public VelocitySkiaAnimator SetVelocity(float value)
    parameters:
    - id: value
      type: System.Single
    return:
      type: DrawnUi.Animate.Animators.VelocitySkiaAnimator
    content.vb: Public Function SetVelocity(value As Single) As VelocitySkiaAnimator
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity*
  nameWithType.vb: VelocitySkiaAnimator.SetVelocity(Single)
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity(Single)
  name.vb: SetVelocity(Single)
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction(System.Single)
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction(System.Single)
  id: SetFriction(System.Single)
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: SetFriction(float)
  nameWithType: VelocitySkiaAnimator.SetFriction(float)
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction(float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetFriction
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 248
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public VelocitySkiaAnimator SetFriction(float value)
    parameters:
    - id: value
      type: System.Single
    return:
      type: DrawnUi.Animate.Animators.VelocitySkiaAnimator
    content.vb: Public Function SetFriction(value As Single) As VelocitySkiaAnimator
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction*
  nameWithType.vb: VelocitySkiaAnimator.SetFriction(Single)
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction(Single)
  name.vb: SetFriction(Single)
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVisibleChange
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVisibleChange
  id: mMinVisibleChange
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: mMinVisibleChange
  nameWithType: VelocitySkiaAnimator.mMinVisibleChange
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVisibleChange
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: mMinVisibleChange
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 255
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float mMinVisibleChange { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property mMinVisibleChange As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVisibleChange*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Friction
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.Friction
  id: Friction
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: Friction
  nameWithType: VelocitySkiaAnimator.Friction
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Friction
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Friction
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 289
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  summary: The bigger the sooner animation will slow down, default is 1.0
  example: []
  syntax:
    content: public float Friction { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Friction As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Friction*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.RemainingVelocity
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.RemainingVelocity
  id: RemainingVelocity
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: RemainingVelocity
  nameWithType: VelocitySkiaAnimator.RemainingVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.RemainingVelocity
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RemainingVelocity
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 308
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  summary: This is set after we are done so we will know at OnStop if we have some energy left
  example: []
  syntax:
    content: public float RemainingVelocity { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property RemainingVelocity As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.RemainingVelocity*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMassState
  commentId: F:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMassState
  id: mMassState
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: mMassState
  nameWithType: VelocitySkiaAnimator.mMassState
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMassState
  type: Field
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: mMassState
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 310
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: protected VelocitySkiaAnimator.MassState mMassState
    return:
      type: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
    content.vb: Protected mMassState As VelocitySkiaAnimator.MassState
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mFlingForce
  commentId: F:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mFlingForce
  id: mFlingForce
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: mFlingForce
  nameWithType: VelocitySkiaAnimator.mFlingForce
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mFlingForce
  type: Field
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: mFlingForce
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 316
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: protected VelocitySkiaAnimator.DragForce mFlingForce
    return:
      type: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
    content.vb: Protected mFlingForce As VelocitySkiaAnimator.DragForce
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Preset
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.Preset
  id: Preset
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: Preset
  nameWithType: VelocitySkiaAnimator.Preset
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Preset
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Preset
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 407
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public VelocitySkiaAnimator.PresetType Preset { get; set; }
    parameters: []
    return:
      type: DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType
    content.vb: Public Property Preset As VelocitySkiaAnimator.PresetType
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Preset*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Scale
  commentId: P:DrawnUi.Animate.Animators.VelocitySkiaAnimator.Scale
  id: Scale
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: Scale
  nameWithType: VelocitySkiaAnimator.Scale
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Scale
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Scale
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 424
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public float Scale { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Scale As Single
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Scale*
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.#ctor(DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Animate.Animators.VelocitySkiaAnimator.#ctor(DrawnUi.Draw.SkiaControl)
  id: '#ctor(DrawnUi.Draw.SkiaControl)'
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: VelocitySkiaAnimator(SkiaControl)
  nameWithType: VelocitySkiaAnimator.VelocitySkiaAnimator(SkiaControl)
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.VelocitySkiaAnimator(DrawnUi.Draw.SkiaControl)
  type: Constructor
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 456
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public VelocitySkiaAnimator(SkiaControl parent)
    parameters:
    - id: parent
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Sub New(parent As SkiaControl)
  overload: DrawnUi.Animate.Animators.VelocitySkiaAnimator.#ctor*
  nameWithType.vb: VelocitySkiaAnimator.New(SkiaControl)
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.New(DrawnUi.Draw.SkiaControl)
  name.vb: New(SkiaControl)
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_PIXELS
  commentId: F:DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_PIXELS
  id: MIN_VISIBLE_CHANGE_PIXELS
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: MIN_VISIBLE_CHANGE_PIXELS
  nameWithType: VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_PIXELS
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_PIXELS
  type: Field
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MIN_VISIBLE_CHANGE_PIXELS
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 463
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public const float MIN_VISIBLE_CHANGE_PIXELS = 1
    return:
      type: System.Single
    content.vb: Public Const MIN_VISIBLE_CHANGE_PIXELS As Single = 1
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ROTATION_DEGREES
  commentId: F:DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ROTATION_DEGREES
  id: MIN_VISIBLE_CHANGE_ROTATION_DEGREES
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: MIN_VISIBLE_CHANGE_ROTATION_DEGREES
  nameWithType: VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ROTATION_DEGREES
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ROTATION_DEGREES
  type: Field
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MIN_VISIBLE_CHANGE_ROTATION_DEGREES
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 464
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public const float MIN_VISIBLE_CHANGE_ROTATION_DEGREES = 0
    return:
      type: System.Single
    content.vb: Public Const MIN_VISIBLE_CHANGE_ROTATION_DEGREES As Single = 0
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ALPHA
  commentId: F:DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ALPHA
  id: MIN_VISIBLE_CHANGE_ALPHA
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: MIN_VISIBLE_CHANGE_ALPHA
  nameWithType: VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ALPHA
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_ALPHA
  type: Field
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MIN_VISIBLE_CHANGE_ALPHA
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 465
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public const float MIN_VISIBLE_CHANGE_ALPHA = 0
    return:
      type: System.Single
    content.vb: Public Const MIN_VISIBLE_CHANGE_ALPHA As Single = 0
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_SCALE
  commentId: F:DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_SCALE
  id: MIN_VISIBLE_CHANGE_SCALE
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: MIN_VISIBLE_CHANGE_SCALE
  nameWithType: VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_SCALE
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MIN_VISIBLE_CHANGE_SCALE
  type: Field
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MIN_VISIBLE_CHANGE_SCALE
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 466
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public const float MIN_VISIBLE_CHANGE_SCALE = 0
    return:
      type: System.Single
    content.vb: Public Const MIN_VISIBLE_CHANGE_SCALE As Single = 0
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.UNSET
  commentId: F:DrawnUi.Animate.Animators.VelocitySkiaAnimator.UNSET
  id: UNSET
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: UNSET
  nameWithType: VelocitySkiaAnimator.UNSET
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.UNSET
  type: Field
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UNSET
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 467
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public const float UNSET = 3.4028235E+38
    return:
      type: System.Single
    content.vb: Public Const UNSET As Single = 3.4028235E+38
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.THRESHOLD_MULTIPLIER
  commentId: F:DrawnUi.Animate.Animators.VelocitySkiaAnimator.THRESHOLD_MULTIPLIER
  id: THRESHOLD_MULTIPLIER
  parent: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  langs:
  - csharp
  - vb
  name: THRESHOLD_MULTIPLIER
  nameWithType: VelocitySkiaAnimator.THRESHOLD_MULTIPLIER
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.THRESHOLD_MULTIPLIER
  type: Field
  source:
    remote:
      path: src/Shared/Features/Animators/VelocitySkiaAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: THRESHOLD_MULTIPLIER
    path: ../src/Shared/Features/Animators/VelocitySkiaAnimator.cs
    startLine: 468
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Animate.Animators
  syntax:
    content: public const float THRESHOLD_MULTIPLIER = 0.75
    return:
      type: System.Single
    content.vb: Public Const THRESHOLD_MULTIPLIER As Single = 0.75
references:
- uid: DrawnUi.Animate.Animators
  commentId: N:DrawnUi.Animate.Animators
  href: DrawnUi.html
  name: DrawnUi.Animate.Animators
  nameWithType: DrawnUi.Animate.Animators
  fullName: DrawnUi.Animate.Animators
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Animate
    name: Animate
    href: DrawnUi.Animate.html
  - name: .
  - uid: DrawnUi.Animate.Animators
    name: Animators
    href: DrawnUi.Animate.Animators.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Animate
    name: Animate
    href: DrawnUi.Animate.html
  - name: .
  - uid: DrawnUi.Animate.Animators
    name: Animators
    href: DrawnUi.Animate.Animators.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.AnimatorBase
  commentId: T:DrawnUi.Draw.AnimatorBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.AnimatorBase.html
  name: AnimatorBase
  nameWithType: AnimatorBase
  fullName: DrawnUi.Draw.AnimatorBase
- uid: DrawnUi.Draw.SkiaValueAnimator
  commentId: T:DrawnUi.Draw.SkiaValueAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaValueAnimator.html
  name: SkiaValueAnimator
  nameWithType: SkiaValueAnimator
  fullName: DrawnUi.Draw.SkiaValueAnimator
- uid: DrawnUi.Draw.ISkiaAnimator
  commentId: T:DrawnUi.Draw.ISkiaAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaAnimator.html
  name: ISkiaAnimator
  nameWithType: ISkiaAnimator
  fullName: DrawnUi.Draw.ISkiaAnimator
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: DrawnUi.Draw.SkiaValueAnimator.Dispose
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Dispose
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose
  name: Dispose()
  nameWithType: SkiaValueAnimator.Dispose()
  fullName: DrawnUi.Draw.SkiaValueAnimator.Dispose()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Dispose
    name: Dispose
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Dispose
    name: Dispose
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_
  name: RunAsync(Action, CancellationToken)
  nameWithType: SkiaValueAnimator.RunAsync(Action, CancellationToken)
  fullName: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action, System.Threading.CancellationToken)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
    name: RunAsync
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_
  - name: (
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: ','
  - name: " "
  - uid: System.Threading.CancellationToken
    name: CancellationToken
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
    name: RunAsync
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_
  - name: (
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: ','
  - name: " "
  - uid: System.Threading.CancellationToken
    name: CancellationToken
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_
  name: OnRunningStateChanged(bool)
  nameWithType: SkiaValueAnimator.OnRunningStateChanged(bool)
  fullName: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(bool)
  nameWithType.vb: SkiaValueAnimator.OnRunningStateChanged(Boolean)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(Boolean)
  name.vb: OnRunningStateChanged(Boolean)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
    name: OnRunningStateChanged
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
    name: OnRunningStateChanged
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_
  name: Seek(float)
  nameWithType: SkiaValueAnimator.Seek(float)
  fullName: DrawnUi.Draw.SkiaValueAnimator.Seek(float)
  nameWithType.vb: SkiaValueAnimator.Seek(Single)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.Seek(Single)
  name.vb: Seek(Single)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
    name: Seek
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
    name: Seek
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_
  - name: (
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.CycleFInished
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.CycleFInished
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_CycleFInished
  name: CycleFInished
  nameWithType: SkiaValueAnimator.CycleFInished
  fullName: DrawnUi.Draw.SkiaValueAnimator.CycleFInished
- uid: DrawnUi.Draw.SkiaValueAnimator.Finished
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Finished
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Finished
  name: Finished
  nameWithType: SkiaValueAnimator.Finished
  fullName: DrawnUi.Draw.SkiaValueAnimator.Finished
- uid: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning
  name: FinishedRunning()
  nameWithType: SkiaValueAnimator.FinishedRunning()
  fullName: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
    name: FinishedRunning
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
    name: FinishedRunning
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
  commentId: F:DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FrameTimeInterpolator
  name: FrameTimeInterpolator
  nameWithType: SkiaValueAnimator.FrameTimeInterpolator
  fullName: DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
- uid: DrawnUi.Draw.SkiaValueAnimator.UseInterpolator
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.UseInterpolator
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UseInterpolator
  name: UseInterpolator
  nameWithType: SkiaValueAnimator.UseInterpolator
  fullName: DrawnUi.Draw.SkiaValueAnimator.UseInterpolator
- uid: DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_
  name: TickFrame(long)
  nameWithType: SkiaValueAnimator.TickFrame(long)
  fullName: DrawnUi.Draw.SkiaValueAnimator.TickFrame(long)
  nameWithType.vb: SkiaValueAnimator.TickFrame(Long)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.TickFrame(Long)
  name.vb: TickFrame(Long)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
    name: TickFrame
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
    name: TickFrame
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Repeat
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Repeat
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Repeat
  name: Repeat
  nameWithType: SkiaValueAnimator.Repeat
  fullName: DrawnUi.Draw.SkiaValueAnimator.Repeat
- uid: DrawnUi.Draw.SkiaValueAnimator.mValue
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mValue
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mValue
  name: mValue
  nameWithType: SkiaValueAnimator.mValue
  fullName: DrawnUi.Draw.SkiaValueAnimator.mValue
- uid: DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mStartValueIsSet
  name: mStartValueIsSet
  nameWithType: SkiaValueAnimator.mStartValueIsSet
  fullName: DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
- uid: DrawnUi.Draw.SkiaValueAnimator.mMaxValue
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mMaxValue
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mMaxValue
  name: mMaxValue
  nameWithType: SkiaValueAnimator.mMaxValue
  fullName: DrawnUi.Draw.SkiaValueAnimator.mMaxValue
- uid: DrawnUi.Draw.SkiaValueAnimator.mMinValue
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mMinValue
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mMinValue
  name: mMinValue
  nameWithType: SkiaValueAnimator.mMinValue
  fullName: DrawnUi.Draw.SkiaValueAnimator.mMinValue
- uid: DrawnUi.Draw.SkiaValueAnimator.Easing
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Easing
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Easing
  name: Easing
  nameWithType: SkiaValueAnimator.Easing
  fullName: DrawnUi.Draw.SkiaValueAnimator.Easing
- uid: DrawnUi.Draw.SkiaValueAnimator.Speed
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Speed
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Speed
  name: Speed
  nameWithType: SkiaValueAnimator.Speed
  fullName: DrawnUi.Draw.SkiaValueAnimator.Speed
- uid: DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(System.Int64)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(System.Int64)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TransformReportedValue_System_Int64_
  name: TransformReportedValue(long)
  nameWithType: SkiaValueAnimator.TransformReportedValue(long)
  fullName: DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(long)
  nameWithType.vb: SkiaValueAnimator.TransformReportedValue(Long)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(Long)
  name.vb: TransformReportedValue(Long)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(System.Int64)
    name: TransformReportedValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TransformReportedValue_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.TransformReportedValue(System.Int64)
    name: TransformReportedValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TransformReportedValue_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Debug
  commentId: F:DrawnUi.Draw.SkiaValueAnimator.Debug
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Debug
  name: Debug
  nameWithType: SkiaValueAnimator.Debug
  fullName: DrawnUi.Draw.SkiaValueAnimator.Debug
- uid: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds
  name: GetNanoseconds()
  nameWithType: SkiaValueAnimator.GetNanoseconds()
  fullName: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
    name: GetNanoseconds
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
    name: GetNanoseconds
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ElapsedMs
  name: ElapsedMs
  nameWithType: SkiaValueAnimator.ElapsedMs
  fullName: DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
- uid: DrawnUi.Draw.SkiaValueAnimator.Progress
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Progress
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Progress
  name: Progress
  nameWithType: SkiaValueAnimator.Progress
  fullName: DrawnUi.Draw.SkiaValueAnimator.Progress
- uid: DrawnUi.Draw.SkiaValueAnimator.OnUpdated
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.OnUpdated
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnUpdated
  name: OnUpdated
  nameWithType: SkiaValueAnimator.OnUpdated
  fullName: DrawnUi.Draw.SkiaValueAnimator.OnUpdated
- uid: DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Start_System_Double_
  name: Start(double)
  nameWithType: SkiaValueAnimator.Start(double)
  fullName: DrawnUi.Draw.SkiaValueAnimator.Start(double)
  nameWithType.vb: SkiaValueAnimator.Start(Double)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.Start(Double)
  name.vb: Start(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
    name: Start
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Start_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
    name: Start
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Start_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_
  name: SetValue(double)
  nameWithType: SkiaValueAnimator.SetValue(double)
  fullName: DrawnUi.Draw.SkiaValueAnimator.SetValue(double)
  nameWithType.vb: SkiaValueAnimator.SetValue(Double)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.SetValue(Double)
  name.vb: SetValue(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
    name: SetValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
    name: SetValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_
  name: SetSpeed(double)
  nameWithType: SkiaValueAnimator.SetSpeed(double)
  fullName: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(double)
  nameWithType.vb: SkiaValueAnimator.SetSpeed(Double)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(Double)
  name.vb: SetSpeed(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
    name: SetSpeed
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
    name: SetSpeed
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.IsPostAnimator
  commentId: P:DrawnUi.Draw.AnimatorBase.IsPostAnimator
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsPostAnimator
  name: IsPostAnimator
  nameWithType: AnimatorBase.IsPostAnimator
  fullName: DrawnUi.Draw.AnimatorBase.IsPostAnimator
- uid: DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
  commentId: P:DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsHiddenInViewTree
  name: IsHiddenInViewTree
  nameWithType: AnimatorBase.IsHiddenInViewTree
  fullName: DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
- uid: DrawnUi.Draw.AnimatorBase.Radians(System.Double)
  commentId: M:DrawnUi.Draw.AnimatorBase.Radians(System.Double)
  parent: DrawnUi.Draw.AnimatorBase
  isExternal: true
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_
  name: Radians(double)
  nameWithType: AnimatorBase.Radians(double)
  fullName: DrawnUi.Draw.AnimatorBase.Radians(double)
  nameWithType.vb: AnimatorBase.Radians(Double)
  fullName.vb: DrawnUi.Draw.AnimatorBase.Radians(Double)
  name.vb: Radians(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Radians(System.Double)
    name: Radians
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Radians(System.Double)
    name: Radians
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.runDelayMs
  commentId: F:DrawnUi.Draw.AnimatorBase.runDelayMs
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_runDelayMs
  name: runDelayMs
  nameWithType: AnimatorBase.runDelayMs
  fullName: DrawnUi.Draw.AnimatorBase.runDelayMs
- uid: DrawnUi.Draw.AnimatorBase.Register
  commentId: M:DrawnUi.Draw.AnimatorBase.Register
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register
  name: Register()
  nameWithType: AnimatorBase.Register()
  fullName: DrawnUi.Draw.AnimatorBase.Register()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Register
    name: Register
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Register
    name: Register
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Unregister
  commentId: M:DrawnUi.Draw.AnimatorBase.Unregister
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister
  name: Unregister()
  nameWithType: AnimatorBase.Unregister()
  fullName: DrawnUi.Draw.AnimatorBase.Unregister()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Unregister
    name: Unregister
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Unregister
    name: Unregister
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Cancel
  commentId: M:DrawnUi.Draw.AnimatorBase.Cancel
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel
  name: Cancel()
  nameWithType: AnimatorBase.Cancel()
  fullName: DrawnUi.Draw.AnimatorBase.Cancel()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Cancel
    name: Cancel
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Cancel
    name: Cancel
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Pause
  commentId: M:DrawnUi.Draw.AnimatorBase.Pause
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause
  name: Pause()
  nameWithType: AnimatorBase.Pause()
  fullName: DrawnUi.Draw.AnimatorBase.Pause()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Pause
    name: Pause
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Pause
    name: Pause
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Resume
  commentId: M:DrawnUi.Draw.AnimatorBase.Resume
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume
  name: Resume()
  nameWithType: AnimatorBase.Resume()
  fullName: DrawnUi.Draw.AnimatorBase.Resume()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Resume
    name: Resume
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Resume
    name: Resume
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.IsPaused
  commentId: P:DrawnUi.Draw.AnimatorBase.IsPaused
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsPaused
  name: IsPaused
  nameWithType: AnimatorBase.IsPaused
  fullName: DrawnUi.Draw.AnimatorBase.IsPaused
- uid: DrawnUi.Draw.AnimatorBase.OnStop
  commentId: P:DrawnUi.Draw.AnimatorBase.OnStop
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_OnStop
  name: OnStop
  nameWithType: AnimatorBase.OnStop
  fullName: DrawnUi.Draw.AnimatorBase.OnStop
- uid: DrawnUi.Draw.AnimatorBase.OnStart
  commentId: P:DrawnUi.Draw.AnimatorBase.OnStart
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_OnStart
  name: OnStart
  nameWithType: AnimatorBase.OnStart
  fullName: DrawnUi.Draw.AnimatorBase.OnStart
- uid: DrawnUi.Draw.AnimatorBase.Parent
  commentId: P:DrawnUi.Draw.AnimatorBase.Parent
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Parent
  name: Parent
  nameWithType: AnimatorBase.Parent
  fullName: DrawnUi.Draw.AnimatorBase.Parent
- uid: DrawnUi.Draw.AnimatorBase.IsDeactivated
  commentId: P:DrawnUi.Draw.AnimatorBase.IsDeactivated
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsDeactivated
  name: IsDeactivated
  nameWithType: AnimatorBase.IsDeactivated
  fullName: DrawnUi.Draw.AnimatorBase.IsDeactivated
- uid: DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
  commentId: P:DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_LastFrameTimeNanos
  name: LastFrameTimeNanos
  nameWithType: AnimatorBase.LastFrameTimeNanos
  fullName: DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
- uid: DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
  commentId: P:DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_StartFrameTimeNanos
  name: StartFrameTimeNanos
  nameWithType: AnimatorBase.StartFrameTimeNanos
  fullName: DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
- uid: DrawnUi.Draw.AnimatorBase.Uid
  commentId: P:DrawnUi.Draw.AnimatorBase.Uid
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Uid
  name: Uid
  nameWithType: AnimatorBase.Uid
  fullName: DrawnUi.Draw.AnimatorBase.Uid
- uid: DrawnUi.Draw.AnimatorBase.IsRunning
  commentId: P:DrawnUi.Draw.AnimatorBase.IsRunning
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsRunning
  name: IsRunning
  nameWithType: AnimatorBase.IsRunning
  fullName: DrawnUi.Draw.AnimatorBase.IsRunning
- uid: DrawnUi.Draw.AnimatorBase.WasStarted
  commentId: P:DrawnUi.Draw.AnimatorBase.WasStarted
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_WasStarted
  name: WasStarted
  nameWithType: AnimatorBase.WasStarted
  fullName: DrawnUi.Draw.AnimatorBase.WasStarted
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinOverscrollValue*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinOverscrollValue
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMinOverscrollValue
  name: mMinOverscrollValue
  nameWithType: VelocitySkiaAnimator.mMinOverscrollValue
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinOverscrollValue
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxOverscrollValue*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxOverscrollValue
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMaxOverscrollValue
  name: mMaxOverscrollValue
  nameWithType: VelocitySkiaAnimator.mMaxOverscrollValue
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxOverscrollValue
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MinLimit*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.MinLimit
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_MinLimit
  name: MinLimit
  nameWithType: VelocitySkiaAnimator.MinLimit
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MinLimit
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MaxLimit*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.MaxLimit
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_MaxLimit
  name: MaxLimit
  nameWithType: VelocitySkiaAnimator.MaxLimit
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MaxLimit
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_SnapBouncingIfVelocityLessThan
  name: SnapBouncingIfVelocityLessThan
  nameWithType: VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SnapBouncingIfVelocityLessThan
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.InvertOnLimits*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.InvertOnLimits
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_InvertOnLimits
  name: InvertOnLimits
  nameWithType: VelocitySkiaAnimator.InvertOnLimits
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.InvertOnLimits
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVelocity*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVelocity
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_mVelocity
  name: mVelocity
  nameWithType: VelocitySkiaAnimator.mVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVelocity
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxVelocity*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxVelocity
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMaxVelocity
  name: mMaxVelocity
  nameWithType: VelocitySkiaAnimator.mMaxVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMaxVelocity
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVelocity*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVelocity
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMinVelocity
  name: mMinVelocity
  nameWithType: VelocitySkiaAnimator.mMinVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVelocity
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVerticalVelocityChange*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVerticalVelocityChange
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_mVerticalVelocityChange
  name: mVerticalVelocityChange
  nameWithType: VelocitySkiaAnimator.mVerticalVelocityChange
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mVerticalVelocityChange
- uid: DrawnUi.Draw.SkiaValueAnimator.Stop
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Stop
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Stop
  name: Stop()
  nameWithType: SkiaValueAnimator.Stop()
  fullName: DrawnUi.Draw.SkiaValueAnimator.Stop()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Stop
    name: Stop
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Stop
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Stop
    name: Stop
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Stop
  - name: (
  - name: )
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Stop*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.Stop
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_Stop
  name: Stop
  nameWithType: VelocitySkiaAnimator.Stop
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Stop
- uid: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ClampOnStart
  name: ClampOnStart()
  nameWithType: SkiaValueAnimator.ClampOnStart()
  fullName: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
    name: ClampOnStart
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ClampOnStart
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
    name: ClampOnStart
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ClampOnStart
  - name: (
  - name: )
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.ClampOnStart*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.ClampOnStart
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_ClampOnStart
  name: ClampOnStart
  nameWithType: VelocitySkiaAnimator.ClampOnStart
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.ClampOnStart
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.InverseK*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.InverseK
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_InverseK
  name: InverseK
  nameWithType: VelocitySkiaAnimator.InverseK
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.InverseK
- uid: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UpdateValue_System_Int64_System_Int64_
  name: UpdateValue(long, long)
  nameWithType: SkiaValueAnimator.UpdateValue(long, long)
  fullName: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(long, long)
  nameWithType.vb: SkiaValueAnimator.UpdateValue(Long, Long)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(Long, Long)
  name.vb: UpdateValue(Long, Long)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
    name: UpdateValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UpdateValue_System_Int64_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: ','
  - name: " "
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
    name: UpdateValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UpdateValue_System_Int64_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: ','
  - name: " "
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_UpdateValue_System_Int64_System_Int64_
  name: UpdateValue
  nameWithType: VelocitySkiaAnimator.UpdateValue
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.UpdateValue
- uid: System.Int64
  commentId: T:System.Int64
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int64
  name: long
  nameWithType: long
  fullName: long
  nameWithType.vb: Long
  fullName.vb: Long
  name.vb: Long
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_SetVelocity_System_Single_
  name: SetVelocity
  nameWithType: VelocitySkiaAnimator.SetVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetVelocity
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator
  commentId: T:DrawnUi.Animate.Animators.VelocitySkiaAnimator
  parent: DrawnUi.Animate.Animators
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  name: VelocitySkiaAnimator
  nameWithType: VelocitySkiaAnimator
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_SetFriction_System_Single_
  name: SetFriction
  nameWithType: VelocitySkiaAnimator.SetFriction
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.SetFriction
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVisibleChange*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVisibleChange
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_mMinVisibleChange
  name: mMinVisibleChange
  nameWithType: VelocitySkiaAnimator.mMinVisibleChange
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.mMinVisibleChange
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Friction*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.Friction
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_Friction
  name: Friction
  nameWithType: VelocitySkiaAnimator.Friction
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Friction
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.RemainingVelocity*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.RemainingVelocity
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_RemainingVelocity
  name: RemainingVelocity
  nameWithType: VelocitySkiaAnimator.RemainingVelocity
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.RemainingVelocity
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
  commentId: T:DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
  parent: DrawnUi.Animate.Animators
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  name: VelocitySkiaAnimator.MassState
  nameWithType: VelocitySkiaAnimator.MassState
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
  spec.csharp:
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator
    name: VelocitySkiaAnimator
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  - name: .
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
    name: MassState
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState.html
  spec.vb:
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator
    name: VelocitySkiaAnimator
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  - name: .
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState
    name: MassState
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.MassState.html
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  commentId: T:DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  parent: DrawnUi.Animate.Animators
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  name: VelocitySkiaAnimator.DragForce
  nameWithType: VelocitySkiaAnimator.DragForce
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
  spec.csharp:
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator
    name: VelocitySkiaAnimator
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  - name: .
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
    name: DragForce
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html
  spec.vb:
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator
    name: VelocitySkiaAnimator
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  - name: .
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce
    name: DragForce
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.DragForce.html
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Preset*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.Preset
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_Preset
  name: Preset
  nameWithType: VelocitySkiaAnimator.Preset
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Preset
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType
  commentId: T:DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType
  parent: DrawnUi.Animate.Animators
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  name: VelocitySkiaAnimator.PresetType
  nameWithType: VelocitySkiaAnimator.PresetType
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType
  spec.csharp:
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator
    name: VelocitySkiaAnimator
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  - name: .
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType
    name: PresetType
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType.html
  spec.vb:
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator
    name: VelocitySkiaAnimator
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html
  - name: .
  - uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType
    name: PresetType
    href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.PresetType.html
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Scale*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.Scale
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator_Scale
  name: Scale
  nameWithType: VelocitySkiaAnimator.Scale
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.Scale
- uid: DrawnUi.Animate.Animators.VelocitySkiaAnimator.#ctor*
  commentId: Overload:DrawnUi.Animate.Animators.VelocitySkiaAnimator.#ctor
  href: DrawnUi.Animate.Animators.VelocitySkiaAnimator.html#DrawnUi_Animate_Animators_VelocitySkiaAnimator__ctor_DrawnUi_Draw_SkiaControl_
  name: VelocitySkiaAnimator
  nameWithType: VelocitySkiaAnimator.VelocitySkiaAnimator
  fullName: DrawnUi.Animate.Animators.VelocitySkiaAnimator.VelocitySkiaAnimator
  nameWithType.vb: VelocitySkiaAnimator.New
  fullName.vb: DrawnUi.Animate.Animators.VelocitySkiaAnimator.New
  name.vb: New
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
