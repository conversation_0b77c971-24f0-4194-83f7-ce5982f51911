﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.PlanCard"
             xmlns:app="clr-namespace:Triggero"
             x:Name="this">
  <ContentView.Content>
      <Grid>
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Grid.GestureRecognizers>

            <RadioButton
                IsChecked="{Binding Source={x:Reference this},Path=IsSelected,Mode=TwoWay}"
                IsVisible="False"
                GroupName="planRB"/>


            <Grid 
                x:Name="uncheckedState"
                VerticalOptions="Center"
                HeightRequest="130">
                
            

                <Frame
                    InputTransparent="True"   
                    VerticalOptions="Center"
                    HeightRequest="130"
                    BackgroundColor="Transparent"
                    IsClippedToBounds="False"
                    BorderColor="{x:StaticResource greyTextColor}"
                    HasShadow="False"
                    Padding="0"
                    CornerRadius="20">

                    <Grid>

                        <Grid Margin="20">

                            <StackLayout
                               Spacing="0">
                                <Label 
                                    x:Name="uncheckedPlanTitleLabel"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontSize="14"
                                    FontAttributes="Bold"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    Text=""/>
                                <Label 
                                    x:Name="uncheckedPlanPriceLabel"
                                    Margin="{x:OnPlatform Android='0,8,0,0',iOS='0,14,0,0'}"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontSize="17"
                                    FontAttributes="Bold"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    Text="699 р."/>
                                <Label 
                                    x:Name="uncheckedPricePerMonthLabel"
                                    Margin="0,10,0,0"
                                    Opacity="0.5"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontSize="10"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Start"
                                    Text=""/>
                            </StackLayout>
                        </Grid>


                    </Grid>



                </Frame>

                <Frame
                    x:Name="uncheckedPlanDiscountFrame"
                    BackgroundColor="{x:StaticResource blueColor}"
                    CornerRadius="15"
                    HasShadow="False"
                    Padding="0"
                    HorizontalOptions="End"
                    VerticalOptions="Start"
                    Margin="0,-10,17,0"
                    HeightRequest="28"
                    WidthRequest="50">
                    <Label 
                        x:Name="uncheckedPlanDiscountLabel"
                        TextColor="White"
                        FontSize="17"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Text="-36%"/>
                </Frame>

            </Grid>

      

            <Grid x:Name="checkedState"
                  InputTransparent="True" 
                  VerticalOptions="Center"
                  HeightRequest="130">



                <Image 
                    Aspect="Fill"
                  Source="planContainer.png"/>

                <Frame
                    x:Name="checkedPlanDiscountFrame"
                    BackgroundColor="{x:StaticResource blueColor}"
                    CornerRadius="15"
                    HasShadow="False"
                    Padding="0"
                    HorizontalOptions="End"
                    VerticalOptions="Start"
                    Margin="0,-10,17,0"
                    HeightRequest="28"
                    WidthRequest="50">
                    <Label 
                        x:Name="checkedPlanDiscountLabel"
                        TextColor="White"
                        FontSize="12"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Text="-36%"/>
                </Frame>


                <StackLayout
                    Margin="20"
                    Spacing="0">
                    <Label 
                        x:Name="checkedPlanTitleLabel"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="14"
                        FontAttributes="Bold"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text=""/>
                    <Label 
                        x:Name="checkedPlanPriceLabel"
                        Margin="{x:OnPlatform Android='0,8,0,0',iOS='0,14,0,0'}"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="17"
                        FontAttributes="Bold"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text="4699 р."/>
                    <Label 
                        x:Name="checkedPricePerMonthLabel"
                        Margin="0,10,0,0"
                        Opacity="0.5"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="10"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text=""/>
                </StackLayout>


            </Grid>

        </Grid>
  </ContentView.Content>
</ContentView>