# Tutorials

Step-by-step tutorials to help you learn and master DrawnUI, from basic concepts to advanced techniques.

## 📚 Step-by-Step Tutorials

### Beginner Tutorials
- **[Your First DrawnUI App (XAML)](first-app.md)** 🚀 - Starting point for beginners:
  - Complete setup from scratch
  - Basic controls and layouts
  - Simple animations
  - Cross-platform deployment

- **[Your First DrawnUI App (C# Fluent)](first-app-code.md)** 🔧 - Same app built with fluent C# syntax:
  - Hot reload support
  - Fluent API patterns
  - Property observation
  - Code-based UI construction

### Enhanced Tutorials

- **[Interactive Cards (XAML)](interactive-cards.md)** ✨ - Build a card gallery with animations and effects:
  - Gesture handling
  - Visual effects and shadows
  - Performance optimization
  - UI patterns

- **[Interactive Cards (C# Fluent)](interactive-cards-code.md)** 🔧 - Same cards built with fluent C# syntax:
  - Fluent API patterns
  - Method chaining
  - Code-based UI construction
  - Advanced C# techniques

### Advanced Tutorials

- **[Create Custom Controls](interactive-button.md)** 🎮 - Create a custom game-style button with optional image inside:
  - Custom control development
  - Bevel/Emboss visual effects
  - Gradient backgrounds
  - Press animations and feedback
  - Performance optimization techniques

- **[News Feed Tutorial](news-feed-tutorial.md)** 📱 - Build a news feed scroller:
  - Data binding and MVVM
  - Recycled cells and virtualization
  - Large dataset handling
  - Production-ready patterns

