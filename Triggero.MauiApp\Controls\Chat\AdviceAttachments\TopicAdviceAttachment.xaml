﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             
             x:Class="Triggero.Controls.Chat.AdviceAttachments.TopicAdviceAttachment">
  <ContentView.Content>
      <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="80"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onClick"/>
            </Grid.GestureRecognizers>

            <Grid Grid.Column="0">

                <Frame 
                    Padding="0"
                    HasShadow="False"
                    IsClippedToBounds="True"
                    CornerRadius="16"
                    VerticalOptions="Start"
                    HorizontalOptions="Start"
                    HeightRequest="80"
                    WidthRequest="80">
                    <!--<Frame.Background>
                        <LinearGradientBrush>
                            <LinearGradientBrush.GradientStops>
                                <GradientStop Color="#F9FCFF" Offset="0.1" />
                                <GradientStop Color="#CDE9FF" Offset="1.0" />
                            </LinearGradientBrush.GradientStops>
                        </LinearGradientBrush>
                    </Frame.Background>-->
                    <Image 
                        Aspect="Fill"
                        x:Name="img"/>
                </Frame>

            </Grid>

            <Grid Grid.Column="1">

                <StackLayout
                    Grid.Column="0"
                    Margin="12,0,0,0"
                    VerticalOptions="Center"
                    HorizontalOptions="Start">

                    <StackLayout 
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Orientation="Horizontal">
                        <Image 
                            Source="eyeGray.png"
                            Opacity="0.5"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            HeightRequest="12"
                            WidthRequest="12"/>
                        <Label 
                            TextColor="{x:StaticResource greyTextColor}"
                            Opacity="0.5"
                            FontSize="12"
                            FontFamily="FontTextLight"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Text="чтение"/>
                    </StackLayout>


                    <Label 
                        x:Name="titleLabel"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="13"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text="Cнятие стресса и медитация"/>
                    <Label 
                        TextColor="{x:StaticResource greyTextColor}"
                        Opacity="0.5"
                        FontSize="12"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Start">
                        <Label.FormattedText>
                            <FormattedString>
                                <FormattedString.Spans>
                                    <Span x:Name="minutesLabel"/>
                                    <Span Text=" "/>
                                    <Span Text="мин"/>
                                </FormattedString.Spans>
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                </StackLayout>

            </Grid>

        </Grid>
  </ContentView.Content>
</ContentView>