﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             BackgroundColor="White"
             Background="White"
             x:Class="Triggero.MauiMobileApp.Views.Pages.MoodTracker.TrackerDay">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="185"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Image
                Aspect="Fill"
                Source="trackerBgGradient.png"
                Grid.RowSpan="2"/>

            <Grid Grid.Row="0">

                <ImageButton 
                    Command="{Binding Source={x:Reference this},Path=Close}"
                    CornerRadius="0"
                    WidthRequest="8"
                    HeightRequest="16"
                    Source="arrowBack.png"
                    Margin="25,0,0,20"
                    HorizontalOptions="Start"
                    VerticalOptions="Center"
                    BackgroundColor="Transparent"/>


                <Label 
                    x:Name="dateLabel"
                    TextColor="{x:StaticResource greyTextColor}"
                    FontSize="22"
                    FontAttributes="Bold"
                    Margin="20,0,0,30"
                    VerticalOptions="End"
                    HorizontalOptions="Start"
                    Text=""/>
            </Grid>

            <Grid Grid.Row="1">
                <ScrollView Margin="20,0,20,20">
                    <StackLayout 
                        Spacing="12"
                        x:Name="layout">
                        
                    </StackLayout>
                </ScrollView>
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>