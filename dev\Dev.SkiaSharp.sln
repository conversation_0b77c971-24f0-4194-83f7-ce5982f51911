﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Addons", "Addons", "{5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DrawnUi.Maui.Camera", "Maui\Addons\DrawnUi.Maui.Camera\DrawnUi.Maui.Camera.csproj", "{DD2D491D-7046-41D2-A00E-FE65CBADE85E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DrawnUi.Maui.Game", "<PERSON>ui\Addons\DrawnUi.Maui.Game\DrawnUi.Maui.Game.csproj", "{793E382E-815C-42A2-B045-44FF930BED07}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "DrawnUi.MauiGraphics", "Maui\Addons\DrawnUi.MauiGraphics\DrawnUi.MauiGraphics.csproj", "{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{8ED244C1-6F60-4954-BC7F-98D827EA31CC}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "SomeBenchmarks", "Tests\SomeBenchmarks\SomeBenchmarks.csproj", "{441DFECC-C694-4B72-BA21-CE40E810A575}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "UnitTests", "Tests\UnitTests\UnitTests.csproj", "{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{089100B1-113F-4E66-888A-E83F3999EAFD}"
	ProjectSection(SolutionItems) = preProject
		Maui\Directory.Build.props = Maui\Directory.Build.props
		Directory.Build.props = Directory.Build.props
		Directory.Build.targets = Directory.Build.targets
		..\nugets\github_uploadnugets.bat = ..\nugets\github_uploadnugets.bat
		..\nugets\nuget_uploadnugets.bat = ..\nugets\nuget_uploadnugets.bat
		..\README.md = ..\README.md
	EndProjectSection
EndProject
Project("{D954291E-2A0B-460D-934E-DC6B0785DB48}") = "DrawnUi.Shared", "Shared\DrawnUi.Shared.shproj", "{*************-48DD-BDB3-98EDECBB1107}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrawnUi.Maui.MapsUi", "Maui\Addons\DrawnUi.Maui.MapsUi\DrawnUi.Maui.MapsUi.csproj", "{FADF6785-E873-6CD3-764C-E3A98456EAF8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sandbox", "Maui\Samples\Sandbox\Sandbox.csproj", "{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Samples", "Samples", "{5D20AA90-6969-D8BD-9DCD-8634F4692FDA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "AppoMobi.Maui.DrawnUi.Demo", "..\..\AppoMobi.Maui.DrawnUi.Demo\src\AllDrawn\AppoMobi.Maui.DrawnUi.Demo.csproj", "{B9726DC8-5E20-BF93-5EED-AC166CD612BF}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Docs", "Docs", "{02EA681E-C7D8-13C7-8484-4AC65E1B71E8}"
	ProjectSection(SolutionItems) = preProject
		..\docs\articles\first-app.md = ..\docs\articles\first-app.md
		..\docs\articles\getting-started.md = ..\docs\articles\getting-started.md
		..\docs\articles\index.md = ..\docs\articles\index.md
		..\docs\articles\tutorials.md = ..\docs\articles\tutorials.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "advanced", "advanced", "{FE2FAC39-CCC4-4F0C-9554-73700F12B8ED}"
	ProjectSection(SolutionItems) = preProject
		..\docs\articles\advanced\game-ui.md = ..\docs\articles\advanced\game-ui.md
		..\docs\articles\advanced\gestures.md = ..\docs\articles\advanced\gestures.md
		..\docs\articles\advanced\gradients.md = ..\docs\articles\advanced\gradients.md
		..\docs\articles\advanced\index.md = ..\docs\articles\advanced\index.md
		..\docs\articles\advanced\layout-system.md = ..\docs\articles\advanced\layout-system.md
		..\docs\articles\advanced\platform-styling.md = ..\docs\articles\advanced\platform-styling.md
		..\docs\articles\advanced\skiascroll.md = ..\docs\articles\advanced\skiascroll.md
	EndProjectSection
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "SkiaSharp", "SkiaSharp", "{C45FC449-9882-4CAC-935B-FFF85E9B98E0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SkiaSharp.Views.WinUI", "..\..\SkiaSharpFork\source\SkiaSharp.Views\SkiaSharp.Views.WinUI\SkiaSharp.Views.WinUI.csproj", "{200057C5-E358-FB5C-C98C-2A94D32C8BBA}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SkiaSharp.Views.Maui.Controls", "..\..\SkiaSharpFork\source\SkiaSharp.Views.Maui\SkiaSharp.Views.Maui.Controls\SkiaSharp.Views.Maui.Controls.csproj", "{1D8D6D5C-15A2-6A6D-0E2C-32A371290A4A}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SkiaSharp.Views.Maui.Core", "..\..\SkiaSharpFork\source\SkiaSharp.Views.Maui\SkiaSharp.Views.Maui.Core\SkiaSharp.Views.Maui.Core.csproj", "{18528C9A-7A30-7109-A6F1-C3DCC9CEF18F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SkiaSharp", "..\..\SkiaSharpFork\binding\SkiaSharp\SkiaSharp.csproj", "{75CEE84D-B115-4BDD-4E81-484E9BAC62C4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SkiaSharp.NativeAssets.WinUI", "..\..\SkiaSharpFork\binding\SkiaSharp.NativeAssets.WinUI\SkiaSharp.NativeAssets.WinUI.csproj", "{9C3D9423-CA6A-29E7-D3BB-912047B49210}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SkiaSharp.Skottie", "..\..\SkiaSharpFork\binding\SkiaSharp.Skottie\SkiaSharp.Skottie.csproj", "{DAC88F04-8BBC-4653-623A-43670B951AC2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SkiaSharp.SceneGraph", "..\..\SkiaSharpFork\binding\SkiaSharp.SceneGraph\SkiaSharp.SceneGraph.csproj", "{EE274246-D61E-9065-766E-05301A41ECD0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SkiaSharp.Resources", "..\..\SkiaSharpFork\binding\SkiaSharp.Resources\SkiaSharp.Resources.csproj", "{B22E4239-C394-A009-CF7D-37C41C2029F1}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SkiaSharp.Views", "..\..\SkiaSharpFork\source\SkiaSharp.Views\SkiaSharp.Views\SkiaSharp.Views.csproj", "{522F20CC-F666-8D73-97A6-B407A0295F26}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "DrawnUi.Maui", "Maui\DrawnUi\DrawnUi.Maui.csproj", "{93E119B1-4378-87DF-2DD2-A818D1E6C2A2}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SkiaSharp.NativeAssets.Win32", "..\..\SkiaSharpFork\binding\SkiaSharp.NativeAssets.Win32\SkiaSharp.NativeAssets.Win32.csproj", "{DF84795B-23BF-4D93-738A-A484184F5925}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{DD2D491D-7046-41D2-A00E-FE65CBADE85E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DD2D491D-7046-41D2-A00E-FE65CBADE85E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DD2D491D-7046-41D2-A00E-FE65CBADE85E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DD2D491D-7046-41D2-A00E-FE65CBADE85E}.Release|Any CPU.Build.0 = Release|Any CPU
		{793E382E-815C-42A2-B045-44FF930BED07}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{793E382E-815C-42A2-B045-44FF930BED07}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{793E382E-815C-42A2-B045-44FF930BED07}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{793E382E-815C-42A2-B045-44FF930BED07}.Release|Any CPU.Build.0 = Release|Any CPU
		{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7}.Release|Any CPU.Build.0 = Release|Any CPU
		{441DFECC-C694-4B72-BA21-CE40E810A575}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{441DFECC-C694-4B72-BA21-CE40E810A575}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{441DFECC-C694-4B72-BA21-CE40E810A575}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{441DFECC-C694-4B72-BA21-CE40E810A575}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B}.Release|Any CPU.Build.0 = Release|Any CPU
		{FADF6785-E873-6CD3-764C-E3A98456EAF8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FADF6785-E873-6CD3-764C-E3A98456EAF8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FADF6785-E873-6CD3-764C-E3A98456EAF8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FADF6785-E873-6CD3-764C-E3A98456EAF8}.Release|Any CPU.Build.0 = Release|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Release|Any CPU.Build.0 = Release|Any CPU
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{B9726DC8-5E20-BF93-5EED-AC166CD612BF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B9726DC8-5E20-BF93-5EED-AC166CD612BF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B9726DC8-5E20-BF93-5EED-AC166CD612BF}.Debug|Any CPU.Deploy.0 = Debug|Any CPU
		{B9726DC8-5E20-BF93-5EED-AC166CD612BF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B9726DC8-5E20-BF93-5EED-AC166CD612BF}.Release|Any CPU.Build.0 = Release|Any CPU
		{B9726DC8-5E20-BF93-5EED-AC166CD612BF}.Release|Any CPU.Deploy.0 = Release|Any CPU
		{200057C5-E358-FB5C-C98C-2A94D32C8BBA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{200057C5-E358-FB5C-C98C-2A94D32C8BBA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{200057C5-E358-FB5C-C98C-2A94D32C8BBA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{200057C5-E358-FB5C-C98C-2A94D32C8BBA}.Release|Any CPU.Build.0 = Release|Any CPU
		{1D8D6D5C-15A2-6A6D-0E2C-32A371290A4A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1D8D6D5C-15A2-6A6D-0E2C-32A371290A4A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1D8D6D5C-15A2-6A6D-0E2C-32A371290A4A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1D8D6D5C-15A2-6A6D-0E2C-32A371290A4A}.Release|Any CPU.Build.0 = Release|Any CPU
		{18528C9A-7A30-7109-A6F1-C3DCC9CEF18F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{18528C9A-7A30-7109-A6F1-C3DCC9CEF18F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{18528C9A-7A30-7109-A6F1-C3DCC9CEF18F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{18528C9A-7A30-7109-A6F1-C3DCC9CEF18F}.Release|Any CPU.Build.0 = Release|Any CPU
		{75CEE84D-B115-4BDD-4E81-484E9BAC62C4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{75CEE84D-B115-4BDD-4E81-484E9BAC62C4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{75CEE84D-B115-4BDD-4E81-484E9BAC62C4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{75CEE84D-B115-4BDD-4E81-484E9BAC62C4}.Release|Any CPU.Build.0 = Release|Any CPU
		{9C3D9423-CA6A-29E7-D3BB-912047B49210}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{9C3D9423-CA6A-29E7-D3BB-912047B49210}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{9C3D9423-CA6A-29E7-D3BB-912047B49210}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{9C3D9423-CA6A-29E7-D3BB-912047B49210}.Release|Any CPU.Build.0 = Release|Any CPU
		{DAC88F04-8BBC-4653-623A-43670B951AC2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DAC88F04-8BBC-4653-623A-43670B951AC2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DAC88F04-8BBC-4653-623A-43670B951AC2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DAC88F04-8BBC-4653-623A-43670B951AC2}.Release|Any CPU.Build.0 = Release|Any CPU
		{EE274246-D61E-9065-766E-05301A41ECD0}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EE274246-D61E-9065-766E-05301A41ECD0}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EE274246-D61E-9065-766E-05301A41ECD0}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EE274246-D61E-9065-766E-05301A41ECD0}.Release|Any CPU.Build.0 = Release|Any CPU
		{B22E4239-C394-A009-CF7D-37C41C2029F1}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B22E4239-C394-A009-CF7D-37C41C2029F1}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B22E4239-C394-A009-CF7D-37C41C2029F1}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B22E4239-C394-A009-CF7D-37C41C2029F1}.Release|Any CPU.Build.0 = Release|Any CPU
		{522F20CC-F666-8D73-97A6-B407A0295F26}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{522F20CC-F666-8D73-97A6-B407A0295F26}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{522F20CC-F666-8D73-97A6-B407A0295F26}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{522F20CC-F666-8D73-97A6-B407A0295F26}.Release|Any CPU.Build.0 = Release|Any CPU
		{93E119B1-4378-87DF-2DD2-A818D1E6C2A2}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{93E119B1-4378-87DF-2DD2-A818D1E6C2A2}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{93E119B1-4378-87DF-2DD2-A818D1E6C2A2}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{93E119B1-4378-87DF-2DD2-A818D1E6C2A2}.Release|Any CPU.Build.0 = Release|Any CPU
		{DF84795B-23BF-4D93-738A-A484184F5925}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{DF84795B-23BF-4D93-738A-A484184F5925}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{DF84795B-23BF-4D93-738A-A484184F5925}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{DF84795B-23BF-4D93-738A-A484184F5925}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{DD2D491D-7046-41D2-A00E-FE65CBADE85E} = {5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}
		{793E382E-815C-42A2-B045-44FF930BED07} = {5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}
		{E1A7A5B7-8B7A-43CC-888B-4CF97783AFB7} = {5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}
		{441DFECC-C694-4B72-BA21-CE40E810A575} = {8ED244C1-6F60-4954-BC7F-98D827EA31CC}
		{1BECAEC0-5BCC-49FE-AB0B-0677D78C3D0B} = {8ED244C1-6F60-4954-BC7F-98D827EA31CC}
		{FADF6785-E873-6CD3-764C-E3A98456EAF8} = {5B1CDC4F-5ED6-4662-8EC6-3DE3FF0B05BE}
		{809FE1DE-DCE9-2383-C3DD-1B21F336EB34} = {5D20AA90-6969-D8BD-9DCD-8634F4692FDA}
		{B9726DC8-5E20-BF93-5EED-AC166CD612BF} = {5D20AA90-6969-D8BD-9DCD-8634F4692FDA}
		{FE2FAC39-CCC4-4F0C-9554-73700F12B8ED} = {02EA681E-C7D8-13C7-8484-4AC65E1B71E8}
		{200057C5-E358-FB5C-C98C-2A94D32C8BBA} = {C45FC449-9882-4CAC-935B-FFF85E9B98E0}
		{1D8D6D5C-15A2-6A6D-0E2C-32A371290A4A} = {C45FC449-9882-4CAC-935B-FFF85E9B98E0}
		{18528C9A-7A30-7109-A6F1-C3DCC9CEF18F} = {C45FC449-9882-4CAC-935B-FFF85E9B98E0}
		{75CEE84D-B115-4BDD-4E81-484E9BAC62C4} = {C45FC449-9882-4CAC-935B-FFF85E9B98E0}
		{9C3D9423-CA6A-29E7-D3BB-912047B49210} = {C45FC449-9882-4CAC-935B-FFF85E9B98E0}
		{DAC88F04-8BBC-4653-623A-43670B951AC2} = {C45FC449-9882-4CAC-935B-FFF85E9B98E0}
		{EE274246-D61E-9065-766E-05301A41ECD0} = {C45FC449-9882-4CAC-935B-FFF85E9B98E0}
		{B22E4239-C394-A009-CF7D-37C41C2029F1} = {C45FC449-9882-4CAC-935B-FFF85E9B98E0}
		{522F20CC-F666-8D73-97A6-B407A0295F26} = {C45FC449-9882-4CAC-935B-FFF85E9B98E0}
		{DF84795B-23BF-4D93-738A-A484184F5925} = {C45FC449-9882-4CAC-935B-FFF85E9B98E0}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {329E3D0C-A3F7-4A3E-B61C-6B2D1BD7F708}
	EndGlobalSection
	GlobalSection(SharedMSBuildProjectFiles) = preSolution
		Shared\Shared.projitems*{*************-48dd-bdb3-98edecbb1107}*SharedItemsImports = 13
		Shared\Shared.projitems*{93e119b1-4378-87df-2dd2-a818d1e6c2a2}*SharedItemsImports = 5
	EndGlobalSection
EndGlobal
