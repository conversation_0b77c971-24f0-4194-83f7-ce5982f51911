### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ISkiaSharpView
  commentId: T:DrawnUi.Draw.ISkiaSharpView
  id: ISkiaSharpView
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ISkiaSharpView.CanvasSize
  - DrawnUi.Draw.ISkiaSharpView.SignalFrame(System.Int64)
  - DrawnUi.Draw.ISkiaSharpView.Update(System.Int64)
  langs:
  - csharp
  - vb
  name: ISkiaSharpView
  nameWithType: ISkiaSharpView
  fullName: DrawnUi.Draw.ISkiaSharpView
  type: Interface
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/ISkiaSharpView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ISkiaSharpView
    path: ../src/Shared/Draw/Internals/Interfaces/ISkiaSharpView.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public interface ISkiaSharpView
    content.vb: Public Interface ISkiaSharpView
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ISkiaSharpView.Update(System.Int64)
  commentId: M:DrawnUi.Draw.ISkiaSharpView.Update(System.Int64)
  id: Update(System.Int64)
  parent: DrawnUi.Draw.ISkiaSharpView
  langs:
  - csharp
  - vb
  name: Update(long)
  nameWithType: ISkiaSharpView.Update(long)
  fullName: DrawnUi.Draw.ISkiaSharpView.Update(long)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/ISkiaSharpView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Update
    path: ../src/Shared/Draw/Internals/Interfaces/ISkiaSharpView.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Safe InvalidateSurface() call. If nanos not specified will generate ittself
  example: []
  syntax:
    content: void Update(long nanos = 0)
    parameters:
    - id: nanos
      type: System.Int64
    content.vb: Sub Update(nanos As Long = 0)
  overload: DrawnUi.Draw.ISkiaSharpView.Update*
  nameWithType.vb: ISkiaSharpView.Update(Long)
  fullName.vb: DrawnUi.Draw.ISkiaSharpView.Update(Long)
  name.vb: Update(Long)
- uid: DrawnUi.Draw.ISkiaSharpView.SignalFrame(System.Int64)
  commentId: M:DrawnUi.Draw.ISkiaSharpView.SignalFrame(System.Int64)
  id: SignalFrame(System.Int64)
  parent: DrawnUi.Draw.ISkiaSharpView
  langs:
  - csharp
  - vb
  name: SignalFrame(long)
  nameWithType: ISkiaSharpView.SignalFrame(long)
  fullName: DrawnUi.Draw.ISkiaSharpView.SignalFrame(long)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/ISkiaSharpView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SignalFrame
    path: ../src/Shared/Draw/Internals/Interfaces/ISkiaSharpView.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: void SignalFrame(long nanoseconds)
    parameters:
    - id: nanoseconds
      type: System.Int64
    content.vb: Sub SignalFrame(nanoseconds As Long)
  overload: DrawnUi.Draw.ISkiaSharpView.SignalFrame*
  nameWithType.vb: ISkiaSharpView.SignalFrame(Long)
  fullName.vb: DrawnUi.Draw.ISkiaSharpView.SignalFrame(Long)
  name.vb: SignalFrame(Long)
- uid: DrawnUi.Draw.ISkiaSharpView.CanvasSize
  commentId: P:DrawnUi.Draw.ISkiaSharpView.CanvasSize
  id: CanvasSize
  parent: DrawnUi.Draw.ISkiaSharpView
  langs:
  - csharp
  - vb
  name: CanvasSize
  nameWithType: ISkiaSharpView.CanvasSize
  fullName: DrawnUi.Draw.ISkiaSharpView.CanvasSize
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Interfaces/ISkiaSharpView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CanvasSize
    path: ../src/Shared/Draw/Internals/Interfaces/ISkiaSharpView.cs
    startLine: 11
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: SKSize CanvasSize { get; }
    parameters: []
    return:
      type: SkiaSharp.SKSize
    content.vb: ReadOnly Property CanvasSize As SKSize
  overload: DrawnUi.Draw.ISkiaSharpView.CanvasSize*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ISkiaSharpView.Update*
  commentId: Overload:DrawnUi.Draw.ISkiaSharpView.Update
  href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_Update_System_Int64_
  name: Update
  nameWithType: ISkiaSharpView.Update
  fullName: DrawnUi.Draw.ISkiaSharpView.Update
- uid: System.Int64
  commentId: T:System.Int64
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int64
  name: long
  nameWithType: long
  fullName: long
  nameWithType.vb: Long
  fullName.vb: Long
  name.vb: Long
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.ISkiaSharpView.SignalFrame*
  commentId: Overload:DrawnUi.Draw.ISkiaSharpView.SignalFrame
  href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_SignalFrame_System_Int64_
  name: SignalFrame
  nameWithType: ISkiaSharpView.SignalFrame
  fullName: DrawnUi.Draw.ISkiaSharpView.SignalFrame
- uid: DrawnUi.Draw.ISkiaSharpView.CanvasSize*
  commentId: Overload:DrawnUi.Draw.ISkiaSharpView.CanvasSize
  href: DrawnUi.Draw.ISkiaSharpView.html#DrawnUi_Draw_ISkiaSharpView_CanvasSize
  name: CanvasSize
  nameWithType: ISkiaSharpView.CanvasSize
  fullName: DrawnUi.Draw.ISkiaSharpView.CanvasSize
- uid: SkiaSharp.SKSize
  commentId: T:SkiaSharp.SKSize
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.sksize
  name: SKSize
  nameWithType: SKSize
  fullName: SkiaSharp.SKSize
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
