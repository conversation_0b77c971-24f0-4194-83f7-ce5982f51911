using Triggero.Domain.Models;
using CommunityToolkit.Maui.Views;
using CommunityToolkit.Maui;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using CommunityToolkit.Maui.Extensions;
using Triggero.Controls;
using Triggero.Controls.Cards.Sliders;
using Triggero.MauiMobileApp.Abstractions;
using Triggero.MauiMobileApp.Views.Pages.Auth;
using Triggero.MauiMobileApp.Views.Pages.Legal;
using Triggero.MauiMobileApp.Views.Popups;
using Triggero.Models.Enums;
using Triggero.Models.Plans;
using Triggero.Models.Sliders;

using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Helpers;

namespace Triggero.MauiMobileApp.Views.Pages.Subscriptions
{

    //STPage = StartTrialPage

    public partial class SelectTrialPage : ContentPage
    {
        public SelectTrialPage()
        {
            InitializeComponent();
            NavigationPage.SetHasNavigationBar(this, false);


            //Items.Add(new TrialSliderItem
            //{
            //    Text = "",
            //    ImgPath = ""
            //});
            Items.Add(new TrialSliderItem
            {
                Text = App.This.Interface.Subscriptions.TrialPage.SliderTesting,
                ImgPath = "knowYourself.png"
            });
            Items.Add(new TrialSliderItem
            {
                Text = App.This.Interface.Subscriptions.TrialPage.SliderMoodDiary,
                ImgPath = "moodDiary.png",
            });
            Items.Add(new TrialSliderItem
            {
                Text = App.This.Interface.Subscriptions.TrialPage.SliderLibrary,
                ImgPath = "selfRealise.png",
            });
            Items.Add(new TrialSliderItem
            {
                Text = App.This.Interface.Subscriptions.TrialPage.SliderVirtualAssistant,
                ImgPath = "virtualAssistant.png",
            });


            dotsView.DefaultColor = Color.FromHex("#CEE3F4");
            dotsView.DefaultCornerRadius = 2;
            dotsView.DefaultHeightRequest = 4;
            dotsView.DefaultWidthRequest = 9;

            dotsView.SelectedColor = Color.FromHex("#4D4D4D");
            dotsView.SelectedCornerRadius = 2;
            dotsView.SelectedHeightRequest = 4;
            dotsView.SelectedWidthRequest = 28;

            dotsView.SetDots(Items.Count, 0);

            RenderSlider();
            Load();
        }

        private void RenderSlider()
        {
            sliderLayout.Children.Clear();

            int counter = 0;
            foreach (var item in Items)
            {
                var card = new TrialSliderItemCard
                {
                    TrialSliderItem = item,
                    HeightRequest = 205,
                    WidthRequest = 190
                };
                sliderLayout.Children.Add(card);

                if (counter == 0)
                {
                    card.Margin = new Thickness(120, 0, 0, 0);
                }
                counter++;
            }

        }

        private async Task Load()
        {
            var plans = await ApplicationState.Data.GetPlans();
            var monthPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.Month);
            var threeMonthsPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.ThreeMonths);
            var yearPlan = plans.FirstOrDefault(o => o.BuiltInPlanType == BuiltInPlanType.Year);

            monthPlanSlot.Children.Add(new PlanCard(monthPlan, true) { IsSelected = true });
            threeMonthsPlanSlot.Children.Add(new PlanCard(threeMonthsPlan, true));
            yearPlanSlot.Children.Add(new PlanCard(yearPlan, true));


            appstoreStackLayout.IsVisible = false;


            //if (!await TriggeroMobileAPI.Common.CheckHui())
            //{
            //    appstoreStackLayout.IsVisible = false;
            //}
            //else if(Device.RuntimePlatform == Device.iOS)
            //{
            //    appstoreStackLayout.IsVisible = true;
            //}


        }




        #region Каруселька
        private ObservableCollection<TrialSliderItem> items = new ObservableCollection<TrialSliderItem>();
        public ObservableCollection<TrialSliderItem> Items
        {
            get { return items; }
            set { items = value; OnPropertyChanged(nameof(Items)); }
        }

        int index = 0;
        private void onSliderScrolled(object sender, ScrolledEventArgs e)
        {
            var percent = sliderScrollView.ScrollX / sliderLayout.Width * (double)100;

            var item = (double)Items.Count / (double)100 * percent;
            var itemIndex = (int)Math.Round(item, 0);

            if (index != itemIndex)
            {
                index = itemIndex;
            }
            dotsView.SetIndex(itemIndex);
        }
        #endregion


        private RelayCommand goToAppstorePage;
        public RelayCommand GoToAppstorePage
        {
            get => goToAppstorePage ??= new RelayCommand(async obj =>
            {
                //App.OpenPage(new StartTrialPageAppstore());
            });
        }

        private RelayCommand goToLoginPage;
        public RelayCommand GoToLoginPage
        {
            get => goToLoginPage ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new LoginStartPage());
            });
        }

        private RelayCommand startTrial;
        public RelayCommand StartTrial
        {
            get => startTrial ??= new RelayCommand(async obj =>
            {

                try
                {
                    Plan plan = null;
                    if ((monthPlanSlot.Children[0] as PlanCard).IsSelected)
                    {
                        plan = (monthPlanSlot.Children[0] as PlanCard).Plan;
                    }
                    else if ((threeMonthsPlanSlot.Children[0] as PlanCard).IsSelected)
                    {
                        plan = (threeMonthsPlanSlot.Children[0] as PlanCard).Plan;
                    }
                    else if ((yearPlanSlot.Children[0] as PlanCard).IsSelected)
                    {
                        plan = (yearPlanSlot.Children[0] as PlanCard).Plan;
                    }

                    if (Device.RuntimePlatform == Device.Android && Constants.RequirePrepayment) //use UKASSA
                    {
                        // TODO: Replace with actual API endpoint URL
                        var createdPayment = await MobileApiRequestHelper.ExecuteRequestReceiveModelAsync<CreatedPaymentModel>(
                            $"https://your-api-host/api/Payments/PaySubscription?userID={AuthHelper.UserId}",
                            HttpMethod.Post,
                            new SubscriptionPaymentSettings
                            {
                                Duration = plan.BuiltInPlanType,
                                SubType = SubscriptionType.Trial,
                                IsBindingPayment = true,
                            });

                        YouKassaPopup paymentPopup = new YouKassaPopup(createdPayment);
                        paymentPopup.PaymentProceed += PaymentPopup_PaymentProceed;

                        await this.ShowPopupAsync(paymentPopup);
                    }
                    else
                    {
                        // TODO: Replace with actual API endpoint URL
                        await MobileApiRequestHelper.ExecuteRequestAsync(
                            $"https://your-api-host/api/UserSubscription/StartTrial?userId={AuthHelper.UserId}",
                            HttpMethod.Put);

                        await StartOk();
                    }
                }
                catch (Exception e)
                {
                    Super.Log(e);
                    PlatformUi.Instance.ShowAlert("Ошибка");
                }

            });
        }

        async Task StartOk()
        {
            var user = await AuthHelper.GetUser(true);

            if (user != null && user.UserSubscription.SubscriptionType != SubscriptionType.None)
            {
                App.SetMainPage(new TrialActivatedPage());
            }
            else
            {
                PlatformUi.Instance.ShowAlert("Не удалось активировать пробный период");
            }

            return;

            //AuthHelper.User.UserSubscription.TrialActivated = true;
            //ApplicationState.ConfigData.User.UserSubscription.TrialActivated = true;             
            //ApplicationState.ConfigData.SaveChangesToMemory();

            //GlobalEvents.OnUserPropertyChanged(ApplicationState.ConfigData.User);


            ApplicationState.ConfigData.UserId = user.Id;
            ApplicationState.ConfigData.User = user;
            ApplicationState.ConfigData.SaveChangesToMemory();

            GlobalEvents.OnUserPropertyChanged(user);

            await App.Current.MainPage.Navigation.PopToRootAsync();
            App.OpenPage(new TrialActivatedPage());
        }

        private async void PaymentPopup_PaymentProceed(object sender, EventArgs e)
        {
            // TODO: Replace with actual API endpoint URL
            var response = await MobileApiRequestHelper.ExecuteRequestAsync(
                $"https://your-api-host/api/UserSubscription/StartTrial?userId={AuthHelper.UserId}",
                HttpMethod.Put);

            bool success = response.IsSuccessful;
            if (success)
            {
                await StartOk();
            }
            else
            {

            }
        }


        #region Legal
        private RelayCommand goToEULA;
        public RelayCommand GoToEULA
        {
            get => goToEULA ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new EULAPage());
            });
        }
        private RelayCommand goToTerms;
        public RelayCommand GoToTerms
        {
            get => goToTerms ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new TermsPage());
            });
        }
        private RelayCommand goToPrivacy;
        public RelayCommand GoToPrivacy
        {
            get => goToPrivacy ??= new RelayCommand(async obj =>
            {
                App.OpenPage(new PrivacyPage());
            });
        }

        #endregion


    }
}