### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaControl.ParentMeasureRequest
  commentId: T:DrawnUi.Draw.SkiaControl.ParentMeasureRequest
  id: SkiaControl.ParentMeasureRequest
  parent: DrawnU<PERSON>.Draw
  children:
  - DrawnUi.Draw.SkiaControl.ParentMeasureRequest.HeightRequest
  - DrawnUi.Draw.SkiaControl.ParentMeasureRequest.Parent
  - DrawnUi.Draw.SkiaControl.ParentMeasureRequest.WidthRequest
  langs:
  - csharp
  - vb
  name: SkiaControl.ParentMeasureRequest
  nameWithType: SkiaControl.ParentMeasureRequest
  fullName: DrawnUi.Draw.SkiaControl.ParentMeasureRequest
  type: Struct
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ParentMeasureRequest
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 4342
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public struct SkiaControl.ParentMeasureRequest
    content.vb: Public Structure SkiaControl.ParentMeasureRequest
  inheritedMembers:
  - System.ValueType.Equals(System.Object)
  - System.ValueType.GetHashCode
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.Parent
  commentId: P:DrawnUi.Draw.SkiaControl.ParentMeasureRequest.Parent
  id: Parent
  parent: DrawnUi.Draw.SkiaControl.ParentMeasureRequest
  langs:
  - csharp
  - vb
  name: Parent
  nameWithType: SkiaControl.ParentMeasureRequest.Parent
  fullName: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.Parent
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Parent
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 4344
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public IDrawnBase Parent { readonly get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.IDrawnBase
    content.vb: Public Property Parent As IDrawnBase
  overload: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.Parent*
- uid: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.WidthRequest
  commentId: P:DrawnUi.Draw.SkiaControl.ParentMeasureRequest.WidthRequest
  id: WidthRequest
  parent: DrawnUi.Draw.SkiaControl.ParentMeasureRequest
  langs:
  - csharp
  - vb
  name: WidthRequest
  nameWithType: SkiaControl.ParentMeasureRequest.WidthRequest
  fullName: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.WidthRequest
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WidthRequest
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 4345
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float WidthRequest { readonly get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property WidthRequest As Single
  overload: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.WidthRequest*
- uid: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.HeightRequest
  commentId: P:DrawnUi.Draw.SkiaControl.ParentMeasureRequest.HeightRequest
  id: HeightRequest
  parent: DrawnUi.Draw.SkiaControl.ParentMeasureRequest
  langs:
  - csharp
  - vb
  name: HeightRequest
  nameWithType: SkiaControl.ParentMeasureRequest.HeightRequest
  fullName: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.HeightRequest
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Base/SkiaControl.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HeightRequest
    path: ../src/Shared/Draw/Base/SkiaControl.Shared.cs
    startLine: 4346
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float HeightRequest { readonly get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property HeightRequest As Single
  overload: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.HeightRequest*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.Parent*
  commentId: Overload:DrawnUi.Draw.SkiaControl.ParentMeasureRequest.Parent
  href: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.html#DrawnUi_Draw_SkiaControl_ParentMeasureRequest_Parent
  name: Parent
  nameWithType: SkiaControl.ParentMeasureRequest.Parent
  fullName: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.Parent
- uid: DrawnUi.Draw.IDrawnBase
  commentId: T:DrawnUi.Draw.IDrawnBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnBase.html
  name: IDrawnBase
  nameWithType: IDrawnBase
  fullName: DrawnUi.Draw.IDrawnBase
- uid: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.WidthRequest*
  commentId: Overload:DrawnUi.Draw.SkiaControl.ParentMeasureRequest.WidthRequest
  href: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.html#DrawnUi_Draw_SkiaControl_ParentMeasureRequest_WidthRequest
  name: WidthRequest
  nameWithType: SkiaControl.ParentMeasureRequest.WidthRequest
  fullName: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.WidthRequest
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.HeightRequest*
  commentId: Overload:DrawnUi.Draw.SkiaControl.ParentMeasureRequest.HeightRequest
  href: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.html#DrawnUi_Draw_SkiaControl_ParentMeasureRequest_HeightRequest
  name: HeightRequest
  nameWithType: SkiaControl.ParentMeasureRequest.HeightRequest
  fullName: DrawnUi.Draw.SkiaControl.ParentMeasureRequest.HeightRequest
