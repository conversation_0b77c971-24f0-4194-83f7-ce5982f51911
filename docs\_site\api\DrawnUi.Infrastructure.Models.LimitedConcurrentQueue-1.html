<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class LimitedConcurrentQueue&lt;T&gt; | DrawnUI for .NET MAUI </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class LimitedConcurrentQueue&lt;T&gt; | DrawnUI for .NET MAUI ">
      
      <meta name="description" content="DrawnUI for .NET MAUI - Rendering engine built on SkiaSharp. Create pixel-perfect cross-platform apps for iOS, Android, Windows, MacCatalyst with advanced animations, gestures, and visual effects.">
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/drawnui/new/master/apiSpec/new?filename=DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1.md&amp;value=---%0Auid%3A%20DrawnUi.Infrastructure.Models.LimitedConcurrentQueue%601%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/drawnuint38.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1">



  <h1 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1" class="text-break">
Class LimitedConcurrentQueue&lt;T&gt;  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L5"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Infrastructure.html">Infrastructure</a>.<a class="xref" href="DrawnUi.Infrastructure.Models.html">Models</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class LimitedConcurrentQueue&lt;T&gt;</code></pre>
  </div>



  <h4 class="section">Type Parameters</h4>
  <dl class="parameters">
    <dt><code>T</code></dt>
    <dd></dd>
  </dl>

  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">LimitedConcurrentQueue&lt;T&gt;</span></div>
    </dd>
  </dl>



  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1__ctor_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.#ctor*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1__ctor" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.#ctor">
  LimitedConcurrentQueue()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L23"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LimitedConcurrentQueue()</code></pre>
  </div>













  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1__ctor_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.#ctor*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1__ctor_System_Int32_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.#ctor(System.Int32)">
  LimitedConcurrentQueue(int)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L32"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public LimitedConcurrentQueue(int max)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>max</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>












  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Count_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Count*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Count" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Count">
  Count
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L11"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public int Count { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.int32">int</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_IsLocked_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.IsLocked*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_IsLocked" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.IsLocked">
  IsLocked
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L50"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public bool IsLocked { get; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.boolean">bool</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Clear_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Clear*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Clear" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Clear">
  Clear()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L75"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Clear()</code></pre>
  </div>













  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Lock_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Lock*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Lock" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Lock">
  Lock()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L58"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Lock()</code></pre>
  </div>













  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_OnAutoRemovingItem_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.OnAutoRemovingItem*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_OnAutoRemovingItem__0_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.OnAutoRemovingItem(`0)">
  OnAutoRemovingItem(T)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L27"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected virtual void OnAutoRemovingItem(T item)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>item</code> <span class="xref">T</span></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Pop_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Pop*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Pop" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Pop">
  Pop()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L68"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public T Pop()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><span class="xref">T</span></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Push_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Push*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Push__0_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Push(`0)">
  Push(T)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L37"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Push(T item)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>item</code> <span class="xref">T</span></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_ToArray_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.ToArray*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_ToArray" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.ToArray">
  ToArray()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L13"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public T[] ToArray()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt>T[]</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_ToList_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.ToList*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_ToList" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.ToList">
  ToList()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L18"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public List&lt;T&gt; ToList()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1">List</a>&lt;T&gt;</dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Unlock_" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Unlock*"></a>

  <h3 id="DrawnUi_Infrastructure_Models_LimitedConcurrentQueue_1_Unlock" data-uid="DrawnUi.Infrastructure.Models.LimitedConcurrentQueue`1.Unlock">
  Unlock()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L63"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Unlock()</code></pre>
  </div>














</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Models/LimitedConcurrentQueue.cs/#L5" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          Made by <a href="https://taublast.github.io/about/">Nick Kovalsky aka AppoMobi (@taublast)</a>
        </div>
      </div>
    </footer>
  </body>
</html>
