### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  commentId: T:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  id: SkiaLayout.BackgroundMeasurementStartingPosition
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartX
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartY
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastCol
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastRow
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LayoutType
  langs:
  - csharp
  - vb
  name: SkiaLayout.BackgroundMeasurementStartingPosition
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BackgroundMeasurementStartingPosition
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 110
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Stores the starting position data when background measurement begins

    Used to detect and compensate for position changes due to visibility changes
  example: []
  syntax:
    content: public class SkiaLayout.BackgroundMeasurementStartingPosition
    content.vb: Public Class SkiaLayout.BackgroundMeasurementStartingPosition
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastRow
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastRow
  id: LastRow
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  langs:
  - csharp
  - vb
  name: LastRow
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition.LastRow
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastRow
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LastRow
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 112
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int LastRow { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property LastRow As Integer
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastRow*
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastCol
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastCol
  id: LastCol
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  langs:
  - csharp
  - vb
  name: LastCol
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition.LastCol
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastCol
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LastCol
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 113
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int LastCol { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property LastCol As Integer
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastCol*
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartX
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartX
  id: ExpectedStartX
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  langs:
  - csharp
  - vb
  name: ExpectedStartX
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartX
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartX
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ExpectedStartX
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 114
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float ExpectedStartX { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property ExpectedStartX As Single
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartX*
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartY
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartY
  id: ExpectedStartY
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  langs:
  - csharp
  - vb
  name: ExpectedStartY
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartY
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartY
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ExpectedStartY
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 115
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float ExpectedStartY { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property ExpectedStartY As Single
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartY*
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LayoutType
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LayoutType
  id: LayoutType
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  langs:
  - csharp
  - vb
  name: LayoutType
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition.LayoutType
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LayoutType
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LayoutType
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 116
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public LayoutType LayoutType { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.LayoutType
    content.vb: Public Property LayoutType As LayoutType
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LayoutType*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastRow*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastRow
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementStartingPosition_LastRow
  name: LastRow
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition.LastRow
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastRow
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastCol*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastCol
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementStartingPosition_LastCol
  name: LastCol
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition.LastCol
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LastCol
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartX*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartX
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementStartingPosition_ExpectedStartX
  name: ExpectedStartX
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartX
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartX
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartY*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartY
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementStartingPosition_ExpectedStartY
  name: ExpectedStartY
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartY
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.ExpectedStartY
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LayoutType*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LayoutType
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementStartingPosition_LayoutType
  name: LayoutType
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition.LayoutType
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.LayoutType
- uid: DrawnUi.Draw.LayoutType
  commentId: T:DrawnUi.Draw.LayoutType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LayoutType.html
  name: LayoutType
  nameWithType: LayoutType
  fullName: DrawnUi.Draw.LayoutType
