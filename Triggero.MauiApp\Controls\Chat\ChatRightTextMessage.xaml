﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Chat.ChatRightTextMessage">
  <ContentView.Content>
      <Frame
          CornerRadius="12"
          BackgroundColor="{x:StaticResource blueColor}"
          HasShadow="False"
          Padding="0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="60"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Row="0">
                    <Label 
                        TextColor="White"
                        FontSize="17"
                        Margin="20,8,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Text="Привет, Кристина!"/>
                </Grid>

                <Grid Grid.Row="1">
                    <StackLayout
                        Spacing="0"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        Orientation="Horizontal">
                        <Label 
                            TextColor="White"
                            Opacity="0.5"
                            FontSize="12"
                            Margin="0,12,0,0"
                            VerticalOptions="Start"
                            HorizontalOptions="Start"
                            Text="8:38"/>
                        <Image
                            Source="chatReadMsgSign.png"  
                            Margin="7,12,0,0"
                            HorizontalOptions="Start"
                            VerticalOptions="Start"
                            WidthRequest="12"
                            HeightRequest="8"/>

                    </StackLayout>
                   
                </Grid>

            </Grid>
      </Frame>
  </ContentView.Content>
</ContentView>