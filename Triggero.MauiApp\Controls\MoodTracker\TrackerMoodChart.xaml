﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:charts="clr-namespace:Syncfusion.Maui.Charts;assembly=Syncfusion.Maui.Charts" 
             
             x:Name="this"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Class="Triggero.Controls.MoodTracker.TrackerMoodChart">
    <ContentView.Content>

        <StackLayout>

            <Label
                            Margin="0,10,0,0"
                            TextColor="{x:StaticResource greyTextColor}"
                            HorizontalOptions="Start"
                            FontSize="17"
                            FontAttributes="Bold"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerMainPage.MoodChart}"/>
            <Label
                            TextColor="{x:StaticResource ColorTextGray}"

                            HorizontalOptions="Start"
                            FontSize="12"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerMainPage.MoodChartDescription}"/>


            <Grid
                            Margin="0,30,0,0"
                            HorizontalOptions="FillAndExpand"
                            VerticalOptions="FillAndExpand">
                <Grid.RowDefinitions>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="65"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0"
                                  ColumnSpacing="0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="45"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="44"/>
                            <RowDefinition Height="44"/>
                            <RowDefinition Height="44"/>
                            <RowDefinition Height="44"/>
                            <RowDefinition Height="44"/>
                            <RowDefinition Height="44"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0">

                            <BoxView 
                                            BackgroundColor="#CEE3F4"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="Start"
                                            HeightRequest="1"/>

                            <Image 
                                            Source="dayMood1.png"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            HeightRequest="36"
                                            WidthRequest="36"/>

                            <BoxView 
                                            BackgroundColor="#CEE3F4"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="End"
                                            HeightRequest="1"/>
                        </Grid>

                        <Grid Grid.Row="1">
                            <Image 
                                            Source="dayMood2.png"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            HeightRequest="36"
                                            WidthRequest="36"/>
                            <BoxView 
                                            BackgroundColor="#CEE3F4"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="End"
                                            HeightRequest="1"/>
                        </Grid>

                        <Grid Grid.Row="2">
                            <Image 
                                            Source="dayMood3.png"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            HeightRequest="36"
                                            WidthRequest="36"/>
                            <BoxView 
                                            BackgroundColor="#CEE3F4"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="End"
                                            HeightRequest="1"/>
                        </Grid>

                        <Grid Grid.Row="3">
                            <Image 
                                            Source="dayMood4.png"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            HeightRequest="36"
                                            WidthRequest="36"/>
                            <BoxView 
                                            BackgroundColor="#CEE3F4"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="End"
                                            HeightRequest="1"/>
                        </Grid>

                        <Grid Grid.Row="4">
                            <Image 
                                            Source="dayMood5.png"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            HeightRequest="36"
                                            WidthRequest="36"/>
                            <BoxView 
                                            BackgroundColor="#CEE3F4"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="End"
                                            HeightRequest="1"/>
                        </Grid>

                        <Grid Grid.Row="5">
                            <Image 
                                            Source="dayMood6.png"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Center"
                                            HeightRequest="36"
                                            WidthRequest="36"/>
                            <BoxView 
                                            BackgroundColor="#CEE3F4"
                                            HorizontalOptions="Fill"
                                            VerticalOptions="End"
                                            HeightRequest="1"/>
                        </Grid>

                    </Grid>



                    <Grid Grid.Column="1">

                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="44"/>
                                <RowDefinition Height="44"/>
                                <RowDefinition Height="44"/>
                                <RowDefinition Height="44"/>
                                <RowDefinition Height="44"/>
                                <RowDefinition Height="44"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0">

                                <BoxView 
                                                BackgroundColor="#CEE3F4"
                                                HorizontalOptions="Fill"
                                                VerticalOptions="Start"
                                                HeightRequest="1"/>

                                <BoxView 
                                                BackgroundColor="#CEE3F4"
                                                HorizontalOptions="Fill"
                                                VerticalOptions="End"
                                                HeightRequest="1"/>

                            </Grid>

                            <Grid Grid.Row="1">
                                <BoxView 
                                                BackgroundColor="#CEE3F4"
                                                HorizontalOptions="Fill"
                                                VerticalOptions="End"
                                                HeightRequest="1"/>
                            </Grid>

                            <Grid Grid.Row="2">
                                <BoxView 
                                                BackgroundColor="#CEE3F4"
                                                HorizontalOptions="Fill"
                                                VerticalOptions="End"
                                                HeightRequest="1"/>
                            </Grid>

                            <Grid Grid.Row="3">
                                <BoxView 
                                                BackgroundColor="#CEE3F4"
                                                HorizontalOptions="Fill"
                                                VerticalOptions="End"
                                                HeightRequest="1"/>
                            </Grid>

                            <Grid Grid.Row="4">
                                <BoxView 
                                                BackgroundColor="#CEE3F4"
                                                HorizontalOptions="Fill"
                                                VerticalOptions="End"
                                                HeightRequest="1"/>
                            </Grid>

                            <Grid Grid.Row="5">
                                <BoxView 
                                                BackgroundColor="#CEE3F4"
                                                HorizontalOptions="Fill"
                                                VerticalOptions="End"
                                                HeightRequest="1"/>
                            </Grid>

                        </Grid>


                        <charts:SfCartesianChart 
                                        x:Name="chart"
                                        Margin="10,0,0,0"
                                        BackgroundColor="Transparent"
                                        HorizontalOptions="FillAndExpand"
                                        VerticalOptions="FillAndExpand">

                            <charts:SfCartesianChart.XAxes>
                                <charts:DateTimeAxis
                                                x:Name="dateAxis"
                                                Interval="1"
                                                ShowMajorGridLines="False" 
                                                IntervalType="Days"
                                                IsVisible="True">
                                    <charts:DateTimeAxis.LabelStyle>
                                        <charts:ChartAxisLabelStyle
                                                        FontSize="10"
                                                        LabelFormat="dd"
                                                        TextColor="#363B4099"/>
                                    </charts:DateTimeAxis.LabelStyle>
                                    <charts:DateTimeAxis.AxisLineStyle>
                                        <charts:ChartLineStyle 
                                                        StrokeWidth="0"
                                                        Stroke="Transparent"/>
                                    </charts:DateTimeAxis.AxisLineStyle>
                                </charts:DateTimeAxis>
                            </charts:SfCartesianChart.XAxes>

                            <charts:SfCartesianChart.YAxes>
                                <charts:NumericalAxis 
                                                Minimum="-0.5" 
                                                Maximum="5.30"
                                                ShowMajorGridLines="False" 
                                                IsVisible="False"
                                                Interval="1"/>
                            </charts:SfCartesianChart.YAxes>

                            <charts:SplineSeries
                                            Fill="#4D4D4D50"
                                            Type="Monotonic"
                                            ItemsSource="{Binding Source={x:Reference this},Path=Data,Mode=TwoWay}"
                                            XBindingPath="Date" 
                                            YBindingPath="Mood"/>

                            <!-- Mood level markers from 0 to 5 -->
                            <charts:LineSeries
                                            Fill="Transparent"
                                            ItemsSource="{Binding Source={x:Reference this},Path=Data5,Mode=TwoWay}"
                                            XBindingPath="Date" 
                                            YBindingPath="Mood"
                                            ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle 
                                                            Background="#FFCD62"/>
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                            <charts:LineSeries
                                            Fill="Transparent"
                                            ItemsSource="{Binding Source={x:Reference this},Path=Data4,Mode=TwoWay}"
                                            XBindingPath="Date" 
                                            YBindingPath="Mood"
                                            ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle 
                                                            Background="#F3D7A8"/>
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                            <charts:LineSeries
                                            Fill="Transparent"
                                            ItemsSource="{Binding Source={x:Reference this},Path=Data3,Mode=TwoWay}"
                                            XBindingPath="Date" 
                                            YBindingPath="Mood"
                                            ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle 
                                                            Background="#CFE4F7"/>
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                            <charts:LineSeries
                                            Fill="Transparent"
                                            ItemsSource="{Binding Source={x:Reference this},Path=Data2,Mode=TwoWay}"
                                            XBindingPath="Date" 
                                            YBindingPath="Mood"
                                            ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle 
                                                            Background="#AEDCEE"/>
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                            <charts:LineSeries
                                            Fill="Transparent"
                                            ItemsSource="{Binding Source={x:Reference this},Path=Data1,Mode=TwoWay}"
                                            XBindingPath="Date" 
                                            YBindingPath="Mood"
                                            ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle 
                                                            Background="#7AD1E2"/>
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                            <charts:LineSeries
                                            Fill="Transparent"
                                            ItemsSource="{Binding Source={x:Reference this},Path=Data0,Mode=TwoWay}"
                                            XBindingPath="Date" 
                                            YBindingPath="Mood"
                                            ShowDataLabels="False">
                                <charts:LineSeries.DataLabelSettings>
                                    <charts:CartesianDataLabelSettings>
                                        <charts:CartesianDataLabelSettings.LabelStyle>
                                            <charts:ChartDataLabelStyle 
                                                            Background="#02C0CD"/>
                                        </charts:CartesianDataLabelSettings.LabelStyle>
                                    </charts:CartesianDataLabelSettings>
                                </charts:LineSeries.DataLabelSettings>
                            </charts:LineSeries>

                        </charts:SfCartesianChart>

                    </Grid>

                </Grid>

                <Grid Grid.Row="1"
                                Margin="7,0,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">
                        <StackLayout 
                                        HorizontalOptions="Start"
                                        VerticalOptions="Center"
                                        Spacing="10">

                            <StackLayout 
                                            Spacing="8"
                                            Orientation="Horizontal">
                                <Frame 
                                                BackgroundColor="#FFCD62"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                HeightRequest="12"
                                                WidthRequest="12"
                                                CornerRadius="6"
                                                HasShadow="False"
                                                Padding="0"/>
                                <Label 
                                                Margin="0,-1,0,0"
                                                Opacity="0.6"
                                                VerticalOptions="Center"
                                                TextColor="#363B40"
                                                FontSize="10"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood5}"/>
                            </StackLayout>

                            <StackLayout 
                                            Spacing="8"
                                            Orientation="Horizontal">
                                <Frame 
                                                BackgroundColor="#F3D7A8"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                HeightRequest="12"
                                                WidthRequest="12"
                                                CornerRadius="6"
                                                HasShadow="False"
                                                Padding="0"/>
                                <Label 
                                                Margin="0,-1,0,0"
                                                Opacity="0.6"
                                                VerticalOptions="Center"
                                                TextColor="#363B40"
                                                FontSize="10"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood4}"/>
                            </StackLayout>

                            <StackLayout 
                                            Spacing="8"
                                            Orientation="Horizontal">
                                <Frame 
                                                BackgroundColor="#CFE4F7"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                HeightRequest="12"
                                                WidthRequest="12"
                                                CornerRadius="6"
                                                HasShadow="False"
                                                Padding="0"/>
                                <Label 
                                                Margin="0,-1,0,0"
                                                Opacity="0.6"
                                                VerticalOptions="Center"
                                                TextColor="#363B40"
                                                FontSize="10"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood3}"/>
                            </StackLayout>

                        </StackLayout>
                    </Grid>

                    <Grid Grid.Column="1">
                        <StackLayout 
                                        HorizontalOptions="Start"
                                        VerticalOptions="Center"
                                        Spacing="10">

                            <StackLayout 
                                            Spacing="8"
                                            Orientation="Horizontal">
                                <Frame 
                                                BackgroundColor="#AEDCEE"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                HeightRequest="12"
                                                WidthRequest="12"
                                                CornerRadius="6"
                                                HasShadow="False"
                                                Padding="0"/>
                                <Label 
                                                Margin="0,-1,0,0"
                                                Opacity="0.6"
                                                VerticalOptions="Center"
                                                TextColor="#363B40"
                                                FontSize="10"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood2}"/>
                            </StackLayout>

                            <StackLayout 
                                            Spacing="8"
                                            Orientation="Horizontal">
                                <Frame 
                                                BackgroundColor="#7AD1E2"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                HeightRequest="12"
                                                WidthRequest="12"
                                                CornerRadius="6"
                                                HasShadow="False"
                                                Padding="0"/>
                                <Label 
                                                Margin="0,-1,0,0"
                                                Opacity="0.6"
                                                VerticalOptions="Center"
                                                TextColor="#363B40"
                                                FontSize="10"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood1}"/>
                            </StackLayout>

                            <StackLayout 
                                            Spacing="8"
                                            Orientation="Horizontal">
                                <Frame 
                                                BackgroundColor="#02C0CD"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                HeightRequest="12"
                                                WidthRequest="12"
                                                CornerRadius="6"
                                                HasShadow="False"
                                                Padding="0"/>
                                <Label 
                                                Margin="0,-1,0,0"
                                                Opacity="0.6"
                                                VerticalOptions="Center"
                                                TextColor="#363B40"
                                                FontSize="10"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood0}"/>
                            </StackLayout>

                        </StackLayout>
                    </Grid>

                </Grid>

            </Grid>

        </StackLayout>

    </ContentView.Content>
</ContentView>