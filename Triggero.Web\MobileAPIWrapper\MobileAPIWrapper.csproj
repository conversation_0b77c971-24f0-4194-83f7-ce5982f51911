﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.0</TargetFramework>
	<LangVersion>latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="JsonKnownTypes" Version="0.6.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <!--<PackageReference Include="RestSharp" Version="111.2.0" />-->
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Triggero.Models\Triggero.Models.csproj" />
    <ProjectReference Include="..\TriggeroWeb.Models\Triggero.Domain.csproj" />
  </ItemGroup>

</Project>
