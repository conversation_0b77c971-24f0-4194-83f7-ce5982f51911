﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Cards.Tracker.TrackerInfluenceGroup">
  <ContentView.Content>
      <StackLayout>
          <Label
              TextColor="{x:StaticResource greyTextColor}"
              HorizontalOptions="Start"
              FontSize="14"
              x:Name="infuenceGroupLabel" />
            <FlexLayout
                Margin="-10,0,20,7"
                Direction="Row"
                Wrap="Wrap"
                x:Name="itemsLayout">
                
            </FlexLayout>
        </StackLayout>
  </ContentView.Content>
</ContentView>