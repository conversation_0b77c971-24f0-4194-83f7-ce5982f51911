using System.Diagnostics;
using System.Net;
using System.Net.Http;
using System.Text;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;

namespace MobileAPIWrapper.Helpers
{
    public enum Method
    {
        Get,
        Post,
        Put,
        Delete,
        Patch
    }

    public static class MobileRequestHelper
    {
        private static readonly HttpClient _httpClient;
        private static string _token;
        private static IEnumerable<KeyValuePair<string, string>> _headers;

        public static EventHandler OnUnauthorized;

        private static readonly JsonSerializerSettings JsonSettings = new JsonSerializerSettings
        {
            ContractResolver = new CamelCasePropertyNamesContractResolver(),
            NullValueHandling = NullValueHandling.Ignore,
            DateFormatHandling = DateFormatHandling.IsoDateFormat
        };

        static MobileRequestHelper()
        {
            var handler = new HttpClientHandler();
            handler.ServerCertificateCustomValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
            _httpClient = new HttpClient(handler);
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        public static void SetBearerToken(string token)
        {
            _token = token;
        }

        public static void AddDefaultHeaders(IEnumerable<KeyValuePair<string, string>> headers)
        {
            _headers = headers;
        }

        public static void ClearDefaultHeaders()
        {
            _headers = null;
        }

        public class MobileResponse
        {
            public string Content { get; set; }
            public HttpStatusCode StatusCode { get; set; }
            public string ErrorMessage { get; set; }
            public Exception ErrorException { get; set; }
            public bool IsSuccessful => (int)StatusCode >= 200 && (int)StatusCode <= 299;
        }

        private static HttpMethod ConvertHttpMethod(Method method)
        {
            return method switch
            {
                Method.Get => HttpMethod.Get,
                Method.Post => HttpMethod.Post,
                Method.Put => HttpMethod.Put,
                Method.Delete => HttpMethod.Delete,
                Method.Patch => new HttpMethod("PATCH"),
                _ => HttpMethod.Get
            };
        }

        public static async Task<MobileResponse> ExecuteRequestAsync(string url, Method method, object body = null)
        {
            try
            {
                Debug.WriteLine($"[MobileRequestHelper] {url}");

                using var request = new HttpRequestMessage(ConvertHttpMethod(method), url);

                if (!string.IsNullOrEmpty(_token))
                {
                    Debug.WriteLine($"[MobileRequestHelper] Authorization Bearer {_token}");
                    request.Headers.Add("Authorization", "Bearer " + _token);
                }

                if (_headers != null)
                {
                    foreach (var header in _headers)
                    {
                        request.Headers.Add(header.Key, header.Value);
                    }
                }

                if (body != null)
                {
                    Debug.WriteLine("[MobileRequestHelper] Sending request with body");
                    var json = JsonConvert.SerializeObject(body, JsonSettings);
                    Debug.WriteLine(json);
                    request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                }
                else if (method == Method.Post || method == Method.Put || method == Method.Patch)
                {
                    request.Content = new StringContent("", Encoding.UTF8, "application/json");
                }

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                var mobileResponse = new MobileResponse
                {
                    Content = content,
                    StatusCode = response.StatusCode
                };

                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    OnUnauthorized?.Invoke(null, null);
                }

                return mobileResponse;
            }
            catch (Exception e)
            {
                Debug.WriteLine($"[MobileRequestHelper] Error: {e.Message}");
                return new MobileResponse
                {
                    ErrorMessage = e.Message,
                    ErrorException = e,
                    StatusCode = HttpStatusCode.BadRequest
                };
            }
        }

        public static async Task<T> ExecuteRequestReceiveModelAsync<T>(string url, Method method, object dto = null)
        {
            try
            {
                var response = await ExecuteRequestAsync(url, method, dto);

                if (!response.IsSuccessful)
                {
                    Debug.WriteLine($"[MobileRequestHelper] Request failed with status: {response.StatusCode}");
                    return default;
                }

                return JsonConvert.DeserializeObject<T>(response.Content, JsonSettings);
            }
            catch (Exception e)
            {
                Debug.WriteLine($"[MobileRequestHelper] Deserialization error: {e}");
            }

            return default;
        }

        public static void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
