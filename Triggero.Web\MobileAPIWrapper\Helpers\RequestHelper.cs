﻿using Newtonsoft.Json;
using RestSharp;
using System.Diagnostics;
using System.Net;
using System.Text;


namespace Odintcovo.API.Helpers
{
    public static class RequestHelper
    {

        public static void SetBearerToken(string token)
        {
            _token = token;
        }

        public static void AddDefaultHeaders(IEnumerable<KeyValuePair<string, string>> headers)
        {
            _headers = headers;
        }

        public static void ClearDefaultHeaders()
        {
            _headers = null;
        }

        public static EventHandler OnUnauthorized;

        private static string _token;
        private static IEnumerable<KeyValuePair<string, string>> _headers;

        public static async Task<RestResponse> ExecuteRequestAsync(
            string url,
            Method method,
            object body = null)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"{url}");

                var options = new RestClientOptions(url)
                {
                    RemoteCertificateValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true
                };
                RestClient client = new RestClient(options);

                var request = new RestRequest(url, method);
                request.AddHeader("Accept", "application/json");
                request.AddHeader("Content-Type", "application/json");

                if (!string.IsNullOrEmpty(_token))
                {
                    Debug.WriteLine($"Authorization Bearer {_token}");
                    request.AddHeader("Authorization", "Bearer " + _token);
                }

                if (_headers != null)
                {
                    foreach (var header in _headers)
                    {
                        request.AddHeader(header.Key, header.Value);
                    }
                }

                if (body is not null)
                {
                    Debug.WriteLine("Sending request with body");
                    Debug.WriteLine(JsonConvert.SerializeObject(body));

                    request.AddJsonBody(body);
                }

                CancellationToken cancellationToken = new CancellationToken();

                var resp = await client.ExecuteAsync(request, cancellationToken);

                if (resp.StatusCode == HttpStatusCode.Unauthorized)
                {
                    OnUnauthorized?.Invoke(null, null);
                }

                if (resp.ErrorException != null)
                {
                    Console.WriteLine(resp.ErrorException);
                    throw resp.ErrorException;
                }

                return resp;
            }
            catch (Exception e)
            {
                return new RestResponse()
                {
                    ErrorMessage = e.Message,
                    StatusCode = HttpStatusCode.BadRequest
                };
            }

        }

        public static async Task<T> ExecuteRequestReceiveModelAsync<T>(string url, Method method,
            object dto = null)
        {
            try
            {
                var response = await RequestHelper.ExecuteRequestAsync(url, method, dto);

                return JsonConvert.DeserializeObject<T>(response.Content);
            }
            catch (Exception e)
            {
                Debug.WriteLine(e);
            }

            return default;
        }
    }

    /// <summary>
    /// Lightweight mobile HTTP client helper using HttpClient instead of RestSharp
    /// Designed for mobile applications to reduce dependencies and improve performance
    /// </summary>
    public static class MobileRequestHelper
    {
        private static readonly HttpClient _httpClient;
        private static string _token;
        private static IEnumerable<KeyValuePair<string, string>> _headers;

        public static EventHandler OnUnauthorized;

        static MobileRequestHelper()
        {
            var handler = new HttpClientHandler();

            // For development - bypass SSL certificate validation
            // TODO: Remove this in production or make it configurable
            handler.ServerCertificateCustomValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;

            _httpClient = new HttpClient(handler);
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        public static void SetBearerToken(string token)
        {
            _token = token;
        }

        public static void AddDefaultHeaders(IEnumerable<KeyValuePair<string, string>> headers)
        {
            _headers = headers;
        }

        public static void ClearDefaultHeaders()
        {
            _headers = null;
        }

        /// <summary>
        /// Response object that mimics RestSharp.RestResponse for compatibility
        /// </summary>
        public class MobileResponse
        {
            public string Content { get; set; }
            public HttpStatusCode StatusCode { get; set; }
            public string ErrorMessage { get; set; }
            public Exception ErrorException { get; set; }
            public bool IsSuccessful => (int)StatusCode >= 200 && (int)StatusCode <= 299;
        }

        /// <summary>
        /// HTTP method enum for compatibility with RestSharp Method enum
        /// </summary>
        public enum HttpMethod
        {
            Get,
            Post,
            Put,
            Delete,
            Patch
        }

        private static System.Net.Http.HttpMethod ConvertHttpMethod(HttpMethod method)
        {
            return method switch
            {
                HttpMethod.Get => System.Net.Http.HttpMethod.Get,
                HttpMethod.Post => System.Net.Http.HttpMethod.Post,
                HttpMethod.Put => System.Net.Http.HttpMethod.Put,
                HttpMethod.Delete => System.Net.Http.HttpMethod.Delete,
                HttpMethod.Patch => new System.Net.Http.HttpMethod("PATCH"),
                _ => System.Net.Http.HttpMethod.Get
            };
        }

        public static async Task<MobileResponse> ExecuteRequestAsync(
            string url,
            HttpMethod method,
            object body = null)
        {
            try
            {
                Debug.WriteLine($"[MobileRequestHelper] {url}");

                using var request = new HttpRequestMessage(ConvertHttpMethod(method), url);

                // Add content type header
                request.Headers.Add("Content-Type", "application/json");

                // Add authorization header if token is set
                if (!string.IsNullOrEmpty(_token))
                {
                    Debug.WriteLine($"[MobileRequestHelper] Authorization Bearer {_token}");
                    request.Headers.Add("Authorization", "Bearer " + _token);
                }

                // Add default headers
                if (_headers != null)
                {
                    foreach (var header in _headers)
                    {
                        request.Headers.Add(header.Key, header.Value);
                    }
                }

                // Add JSON body if provided
                if (body != null)
                {
                    Debug.WriteLine("[MobileRequestHelper] Sending request with body");
                    var json = JsonConvert.SerializeObject(body);
                    Debug.WriteLine(json);

                    request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                }

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                var mobileResponse = new MobileResponse
                {
                    Content = content,
                    StatusCode = response.StatusCode
                };

                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    OnUnauthorized?.Invoke(null, null);
                }

                return mobileResponse;
            }
            catch (Exception e)
            {
                Debug.WriteLine($"[MobileRequestHelper] Error: {e.Message}");
                return new MobileResponse
                {
                    ErrorMessage = e.Message,
                    ErrorException = e,
                    StatusCode = HttpStatusCode.BadRequest
                };
            }
        }

        public static async Task<T> ExecuteRequestReceiveModelAsync<T>(string url, HttpMethod method, object dto = null)
        {
            try
            {
                var response = await ExecuteRequestAsync(url, method, dto);

                if (!response.IsSuccessful)
                {
                    Debug.WriteLine($"[MobileRequestHelper] Request failed with status: {response.StatusCode}");
                    return default;
                }

                return JsonConvert.DeserializeObject<T>(response.Content);
            }
            catch (Exception e)
            {
                Debug.WriteLine($"[MobileRequestHelper] Deserialization error: {e}");
            }

            return default;
        }

        /// <summary>
        /// Dispose resources when application shuts down
        /// </summary>
        public static void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}


