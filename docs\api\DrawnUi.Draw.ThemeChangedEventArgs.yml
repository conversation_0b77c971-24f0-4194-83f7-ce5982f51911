### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ThemeChangedEventArgs
  commentId: T:DrawnUi.Draw.ThemeChangedEventArgs
  id: ThemeChangedEventArgs
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ThemeChangedEventArgs.#ctor(Microsoft.Maui.ApplicationModel.AppTheme,Microsoft.Maui.ApplicationModel.AppTheme)
  - DrawnUi.Draw.ThemeChangedEventArgs.NewTheme
  - DrawnUi.Draw.ThemeChangedEventArgs.OldTheme
  langs:
  - csharp
  - vb
  name: ThemeChangedEventArgs
  nameWithType: ThemeChangedEventArgs
  fullName: DrawnUi.Draw.ThemeChangedEventArgs
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ThemeChangedEventArgs
    path: ../src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
    startLine: 18
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Theme changed event arguments
  example: []
  syntax:
    content: 'public class ThemeChangedEventArgs : EventArgs'
    content.vb: Public Class ThemeChangedEventArgs Inherits EventArgs
  inheritance:
  - System.Object
  - System.EventArgs
  inheritedMembers:
  - System.EventArgs.Empty
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ThemeChangedEventArgs.NewTheme
  commentId: P:DrawnUi.Draw.ThemeChangedEventArgs.NewTheme
  id: NewTheme
  parent: DrawnUi.Draw.ThemeChangedEventArgs
  langs:
  - csharp
  - vb
  name: NewTheme
  nameWithType: ThemeChangedEventArgs.NewTheme
  fullName: DrawnUi.Draw.ThemeChangedEventArgs.NewTheme
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NewTheme
    path: ../src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public AppTheme NewTheme { get; }
    parameters: []
    return:
      type: Microsoft.Maui.ApplicationModel.AppTheme
    content.vb: Public ReadOnly Property NewTheme As AppTheme
  overload: DrawnUi.Draw.ThemeChangedEventArgs.NewTheme*
- uid: DrawnUi.Draw.ThemeChangedEventArgs.OldTheme
  commentId: P:DrawnUi.Draw.ThemeChangedEventArgs.OldTheme
  id: OldTheme
  parent: DrawnUi.Draw.ThemeChangedEventArgs
  langs:
  - csharp
  - vb
  name: OldTheme
  nameWithType: ThemeChangedEventArgs.OldTheme
  fullName: DrawnUi.Draw.ThemeChangedEventArgs.OldTheme
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OldTheme
    path: ../src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
    startLine: 21
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public AppTheme OldTheme { get; }
    parameters: []
    return:
      type: Microsoft.Maui.ApplicationModel.AppTheme
    content.vb: Public ReadOnly Property OldTheme As AppTheme
  overload: DrawnUi.Draw.ThemeChangedEventArgs.OldTheme*
- uid: DrawnUi.Draw.ThemeChangedEventArgs.#ctor(Microsoft.Maui.ApplicationModel.AppTheme,Microsoft.Maui.ApplicationModel.AppTheme)
  commentId: M:DrawnUi.Draw.ThemeChangedEventArgs.#ctor(Microsoft.Maui.ApplicationModel.AppTheme,Microsoft.Maui.ApplicationModel.AppTheme)
  id: '#ctor(Microsoft.Maui.ApplicationModel.AppTheme,Microsoft.Maui.ApplicationModel.AppTheme)'
  parent: DrawnUi.Draw.ThemeChangedEventArgs
  langs:
  - csharp
  - vb
  name: ThemeChangedEventArgs(AppTheme, AppTheme)
  nameWithType: ThemeChangedEventArgs.ThemeChangedEventArgs(AppTheme, AppTheme)
  fullName: DrawnUi.Draw.ThemeChangedEventArgs.ThemeChangedEventArgs(Microsoft.Maui.ApplicationModel.AppTheme, Microsoft.Maui.ApplicationModel.AppTheme)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
    startLine: 23
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ThemeChangedEventArgs(AppTheme oldTheme, AppTheme newTheme)
    parameters:
    - id: oldTheme
      type: Microsoft.Maui.ApplicationModel.AppTheme
    - id: newTheme
      type: Microsoft.Maui.ApplicationModel.AppTheme
    content.vb: Public Sub New(oldTheme As AppTheme, newTheme As AppTheme)
  overload: DrawnUi.Draw.ThemeChangedEventArgs.#ctor*
  nameWithType.vb: ThemeChangedEventArgs.New(AppTheme, AppTheme)
  fullName.vb: DrawnUi.Draw.ThemeChangedEventArgs.New(Microsoft.Maui.ApplicationModel.AppTheme, Microsoft.Maui.ApplicationModel.AppTheme)
  name.vb: New(AppTheme, AppTheme)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.EventArgs
  commentId: T:System.EventArgs
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventargs
  name: EventArgs
  nameWithType: EventArgs
  fullName: System.EventArgs
- uid: System.EventArgs.Empty
  commentId: F:System.EventArgs.Empty
  parent: System.EventArgs
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventargs.empty
  name: Empty
  nameWithType: EventArgs.Empty
  fullName: System.EventArgs.Empty
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ThemeChangedEventArgs.NewTheme*
  commentId: Overload:DrawnUi.Draw.ThemeChangedEventArgs.NewTheme
  href: DrawnUi.Draw.ThemeChangedEventArgs.html#DrawnUi_Draw_ThemeChangedEventArgs_NewTheme
  name: NewTheme
  nameWithType: ThemeChangedEventArgs.NewTheme
  fullName: DrawnUi.Draw.ThemeChangedEventArgs.NewTheme
- uid: Microsoft.Maui.ApplicationModel.AppTheme
  commentId: T:Microsoft.Maui.ApplicationModel.AppTheme
  parent: Microsoft.Maui.ApplicationModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.applicationmodel.apptheme
  name: AppTheme
  nameWithType: AppTheme
  fullName: Microsoft.Maui.ApplicationModel.AppTheme
- uid: Microsoft.Maui.ApplicationModel
  commentId: N:Microsoft.Maui.ApplicationModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.ApplicationModel
  nameWithType: Microsoft.Maui.ApplicationModel
  fullName: Microsoft.Maui.ApplicationModel
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.ApplicationModel
    name: ApplicationModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.applicationmodel
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.ApplicationModel
    name: ApplicationModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.applicationmodel
- uid: DrawnUi.Draw.ThemeChangedEventArgs.OldTheme*
  commentId: Overload:DrawnUi.Draw.ThemeChangedEventArgs.OldTheme
  href: DrawnUi.Draw.ThemeChangedEventArgs.html#DrawnUi_Draw_ThemeChangedEventArgs_OldTheme
  name: OldTheme
  nameWithType: ThemeChangedEventArgs.OldTheme
  fullName: DrawnUi.Draw.ThemeChangedEventArgs.OldTheme
- uid: DrawnUi.Draw.ThemeChangedEventArgs.#ctor*
  commentId: Overload:DrawnUi.Draw.ThemeChangedEventArgs.#ctor
  href: DrawnUi.Draw.ThemeChangedEventArgs.html#DrawnUi_Draw_ThemeChangedEventArgs__ctor_Microsoft_Maui_ApplicationModel_AppTheme_Microsoft_Maui_ApplicationModel_AppTheme_
  name: ThemeChangedEventArgs
  nameWithType: ThemeChangedEventArgs.ThemeChangedEventArgs
  fullName: DrawnUi.Draw.ThemeChangedEventArgs.ThemeChangedEventArgs
  nameWithType.vb: ThemeChangedEventArgs.New
  fullName.vb: DrawnUi.Draw.ThemeChangedEventArgs.New
  name.vb: New
