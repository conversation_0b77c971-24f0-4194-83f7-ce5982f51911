﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Library.PracticePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:sliders="http://schemas.syncfusion.com/maui"

    x:Name="this"
    NavigationPage.HasNavigationBar="False"
    BackgroundColor="White">
    <ContentPage.Content>

        <Grid
            x:Name="mainStackLayout"
            HorizontalOptions="FillAndExpand"
            Opacity="1"
            VerticalOptions="FillAndExpand">

            <!--<extensions:MediaElement
                IsVisible="False"
                x:Name="player"/>-->

            <draw:Canvas
                VerticalOptions="Fill"
                HorizontalOptions="Fill">
                <draw:SkiaImage
                    x:Name="img"
                    Aspect="AspectCover"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill" />
            </draw:Canvas>

            <BoxView 
                BackgroundColor="#22000000"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"/>

            <Grid
                Margin="8,50,0,0"
                HeightRequest="55"
                HorizontalOptions="Start"
                VerticalOptions="Start"
                WidthRequest="55">
                <Grid.GestureRecognizers>
                    <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=Close}" />
                </Grid.GestureRecognizers>

                <ImageButton
                    BackgroundColor="Transparent"
                    Command="{Binding Source={x:Reference this}, Path=Close}"
                    CornerRadius="0"
                    HeightRequest="21"
                    HorizontalOptions="Center"
                    Source="closeWhite.png"
                    VerticalOptions="Center"
                    WidthRequest="21" />
            </Grid>

            <Grid
                Margin="0,50,8,0"
                HeightRequest="55"
                HorizontalOptions="End"
                VerticalOptions="Start"
                WidthRequest="55">
                <Grid.GestureRecognizers>
                    <TapGestureRecognizer Tapped="toggleFavorite" />
                </Grid.GestureRecognizers>
                <RadioButton
                    x:Name="favoriteRb"
                    CornerRadius="0"
                    HeightRequest="27"
                    HorizontalOptions="Center"
                    InputTransparent="True"
                    Style="{x:StaticResource favorite_hearted_white_rb}"
                    VerticalOptions="Center"
                    WidthRequest="27" />
            </Grid>

            <StackLayout
                Margin="20,0,20,90"
                VerticalOptions="End">

                <Label
                    x:Name="titleLabel"
                    Margin="10,0,10,0"
                    FontAttributes="Bold"
                    FontFamily="Open Sans"
                    FontSize="{x:OnPlatform Android=22,
                                          iOS=22}"
                    HorizontalOptions="Fill"
                    HorizontalTextAlignment="Center"
                    Text="Снятие стресса &quot;Медитация&quot;"
                    TextColor="White" />

                <Label
                    x:Name="descLabel"
                    Margin="60,0,60,30"
                    FontSize="{x:OnPlatform Android=14,
                                          iOS=17}"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    Text="По методике Д.Боула"
                    TextColor="#A7C2DC"
                    TextType="Html" />

                <Grid HeightRequest="46">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="1*" />
                        <RowDefinition Height="1*" />
                    </Grid.RowDefinitions>

                    <sliders:SfSlider
                        x:Name="slider"
                        Grid.Row="0"
                        StepSize="0.2"
                        ValueChangeStart="onSliderTouchDown"
                        ValueChangeEnd="onSliderTouchUp"
                        ValueChanging="onSliderValueChanging"
                        Value="0">

                        <sliders:SfSlider.TrackStyle>
                            <sliders:SliderTrackStyle
                                InactiveFill="#363B40"
                                ActiveFill="White"
                                InactiveSize="4"
                                ActiveSize="4"/>
                        </sliders:SfSlider.TrackStyle>

                        <sliders:SfSlider.ThumbStyle>
                            <sliders:SliderThumbStyle
                                Fill="White"
                                Stroke="White"/>
                        </sliders:SfSlider.ThumbStyle>

                        <sliders:SfSlider.MajorTickStyle>
                            <sliders:SliderTickStyle
                                ActiveFill="Transparent"
                                InactiveFill="Transparent"/>
                        </sliders:SfSlider.MajorTickStyle>

                    </sliders:SfSlider>

                    <Grid Grid.Row="1">
                        <Label
                            x:Name="currentProgress"
                            Margin="10,0,0,0"
                            FontSize="14"
                            HorizontalOptions="Start"
                            Text="0:00"
                            TextColor="#ffffff"
                            VerticalOptions="Center" />


                        <Label
                            x:Name="totalTime"
                            Margin="0,0,10,0"
                            FontSize="14"
                            HorizontalOptions="End"
                            Text="0:00"
                            TextColor="#ffffff"
                            VerticalOptions="Center" />

                    </Grid>

                </Grid>


                <StackLayout
                    Margin="0,20,0,0"
                    HeightRequest="70"
                    HorizontalOptions="Center"
                    Orientation="Horizontal"
                    Spacing="28">


                    <Frame
                        Padding="0"
                        BackgroundColor="#A7C2DC"
                        CornerRadius="22"
                        HasShadow="False"
                        HeightRequest="44"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        WidthRequest="44">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=Decrement10Seconds}" />
                        </Frame.GestureRecognizers>
                        <Image
                            Margin="0"
                            Aspect="Fill"
                            Source="player10secondsBack.png" />
                    </Frame>


                    <Grid
                        HeightRequest="70"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        WidthRequest="70">
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Tapped="togglePlayer" />
                        </Grid.GestureRecognizers>
                        <RadioButton
                            x:Name="playerBtn"
                            HeightRequest="70"
                            HorizontalOptions="Start"
                            InputTransparent="True"
                            IsChecked="True"
                            Style="{x:StaticResource player_start_pause_rb}"
                            VerticalOptions="Center"
                            WidthRequest="70" />
                    </Grid>



                    <Frame
                        Padding="0"
                        BackgroundColor="#A7C2DC"
                        CornerRadius="22"
                        HasShadow="False"
                        HeightRequest="44"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        WidthRequest="44">
                        <Frame.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=Increment10Seconds}" />
                        </Frame.GestureRecognizers>
                        <Image
                            Margin="8"
                            Aspect="Fill"
                            Source="player10secondsForward.png" />
                    </Frame>


                </StackLayout>

            </StackLayout>



        </Grid>
    </ContentPage.Content>
</ContentPage>