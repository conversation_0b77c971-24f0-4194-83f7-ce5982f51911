### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ThemeBindings
  commentId: T:DrawnUi.Draw.ThemeBindings
  id: ThemeBindings
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ThemeBindings.GetThemeValue``1(``0,``0,``0)
  - DrawnUi.Draw.ThemeBindings.SetThemeBinding(Microsoft.Maui.Controls.BindableObject,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  - DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(``0,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  langs:
  - csharp
  - vb
  name: ThemeBindings
  nameWithType: ThemeBindings
  fullName: DrawnUi.Draw.ThemeBindings
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ThemeBindings
    path: ../src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
    startLine: 316
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Helper methods for using theme bindings in code-behind
  example: []
  syntax:
    content: public static class ThemeBindings
    content.vb: Public Module ThemeBindings
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Draw.ThemeBindings.SetThemeBinding(Microsoft.Maui.Controls.BindableObject,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  commentId: M:DrawnUi.Draw.ThemeBindings.SetThemeBinding(Microsoft.Maui.Controls.BindableObject,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  id: SetThemeBinding(Microsoft.Maui.Controls.BindableObject,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  parent: DrawnUi.Draw.ThemeBindings
  langs:
  - csharp
  - vb
  name: SetThemeBinding(BindableObject, BindableProperty, object, object, object)
  nameWithType: ThemeBindings.SetThemeBinding(BindableObject, BindableProperty, object, object, object)
  fullName: DrawnUi.Draw.ThemeBindings.SetThemeBinding(Microsoft.Maui.Controls.BindableObject, Microsoft.Maui.Controls.BindableProperty, object, object, object)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetThemeBinding
    path: ../src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
    startLine: 321
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets up a theme binding in code-behind
  example: []
  syntax:
    content: public static ThemeBinding SetThemeBinding(BindableObject target, BindableProperty property, object lightValue, object darkValue, object defaultValue = null)
    parameters:
    - id: target
      type: Microsoft.Maui.Controls.BindableObject
    - id: property
      type: Microsoft.Maui.Controls.BindableProperty
    - id: lightValue
      type: System.Object
    - id: darkValue
      type: System.Object
    - id: defaultValue
      type: System.Object
    return:
      type: DrawnUi.Draw.ThemeBinding
    content.vb: Public Shared Function SetThemeBinding(target As BindableObject, [property] As BindableProperty, lightValue As Object, darkValue As Object, defaultValue As Object = Nothing) As ThemeBinding
  overload: DrawnUi.Draw.ThemeBindings.SetThemeBinding*
  nameWithType.vb: ThemeBindings.SetThemeBinding(BindableObject, BindableProperty, Object, Object, Object)
  fullName.vb: DrawnUi.Draw.ThemeBindings.SetThemeBinding(Microsoft.Maui.Controls.BindableObject, Microsoft.Maui.Controls.BindableProperty, Object, Object, Object)
  name.vb: SetThemeBinding(BindableObject, BindableProperty, Object, Object, Object)
- uid: DrawnUi.Draw.ThemeBindings.GetThemeValue``1(``0,``0,``0)
  commentId: M:DrawnUi.Draw.ThemeBindings.GetThemeValue``1(``0,``0,``0)
  id: GetThemeValue``1(``0,``0,``0)
  parent: DrawnUi.Draw.ThemeBindings
  langs:
  - csharp
  - vb
  name: GetThemeValue<T>(T, T, T)
  nameWithType: ThemeBindings.GetThemeValue<T>(T, T, T)
  fullName: DrawnUi.Draw.ThemeBindings.GetThemeValue<T>(T, T, T)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetThemeValue
    path: ../src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
    startLine: 343
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Gets the current theme value without creating a binding
  example: []
  syntax:
    content: public static T GetThemeValue<T>(T lightValue, T darkValue, T defaultValue = default)
    parameters:
    - id: lightValue
      type: '{T}'
    - id: darkValue
      type: '{T}'
    - id: defaultValue
      type: '{T}'
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function GetThemeValue(Of T)(lightValue As T, darkValue As T, defaultValue As T = Nothing) As T
  overload: DrawnUi.Draw.ThemeBindings.GetThemeValue*
  nameWithType.vb: ThemeBindings.GetThemeValue(Of T)(T, T, T)
  fullName.vb: DrawnUi.Draw.ThemeBindings.GetThemeValue(Of T)(T, T, T)
  name.vb: GetThemeValue(Of T)(T, T, T)
- uid: DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(``0,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  commentId: M:DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(``0,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  id: WithThemeBinding``1(``0,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  isExtensionMethod: true
  parent: DrawnUi.Draw.ThemeBindings
  langs:
  - csharp
  - vb
  name: WithThemeBinding<TControl>(TControl, BindableProperty, object, object, object)
  nameWithType: ThemeBindings.WithThemeBinding<TControl>(TControl, BindableProperty, object, object, object)
  fullName: DrawnUi.Draw.ThemeBindings.WithThemeBinding<TControl>(TControl, Microsoft.Maui.Controls.BindableProperty, object, object, object)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithThemeBinding
    path: ../src/Maui/DrawnUi/Shared/Extensions/ThemeBindingExtension.cs
    startLine: 357
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Fluent extension method for setting theme bindings
  example: []
  syntax:
    content: 'public static TControl WithThemeBinding<TControl>(this TControl control, BindableProperty property, object lightValue, object darkValue, object defaultValue = null) where TControl : BindableObject'
    parameters:
    - id: control
      type: '{TControl}'
    - id: property
      type: Microsoft.Maui.Controls.BindableProperty
    - id: lightValue
      type: System.Object
    - id: darkValue
      type: System.Object
    - id: defaultValue
      type: System.Object
    typeParameters:
    - id: TControl
    return:
      type: '{TControl}'
    content.vb: Public Shared Function WithThemeBinding(Of TControl As BindableObject)(control As TControl, [property] As BindableProperty, lightValue As Object, darkValue As Object, defaultValue As Object = Nothing) As TControl
  overload: DrawnUi.Draw.ThemeBindings.WithThemeBinding*
  nameWithType.vb: ThemeBindings.WithThemeBinding(Of TControl)(TControl, BindableProperty, Object, Object, Object)
  fullName.vb: DrawnUi.Draw.ThemeBindings.WithThemeBinding(Of TControl)(TControl, Microsoft.Maui.Controls.BindableProperty, Object, Object, Object)
  name.vb: WithThemeBinding(Of TControl)(TControl, BindableProperty, Object, Object, Object)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.ThemeBindings.SetThemeBinding*
  commentId: Overload:DrawnUi.Draw.ThemeBindings.SetThemeBinding
  href: DrawnUi.Draw.ThemeBindings.html#DrawnUi_Draw_ThemeBindings_SetThemeBinding_Microsoft_Maui_Controls_BindableObject_Microsoft_Maui_Controls_BindableProperty_System_Object_System_Object_System_Object_
  name: SetThemeBinding
  nameWithType: ThemeBindings.SetThemeBinding
  fullName: DrawnUi.Draw.ThemeBindings.SetThemeBinding
- uid: Microsoft.Maui.Controls.BindableObject
  commentId: T:Microsoft.Maui.Controls.BindableObject
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  name: BindableObject
  nameWithType: BindableObject
  fullName: Microsoft.Maui.Controls.BindableObject
- uid: Microsoft.Maui.Controls.BindableProperty
  commentId: T:Microsoft.Maui.Controls.BindableProperty
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  name: BindableProperty
  nameWithType: BindableProperty
  fullName: Microsoft.Maui.Controls.BindableProperty
- uid: DrawnUi.Draw.ThemeBinding
  commentId: T:DrawnUi.Draw.ThemeBinding
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ThemeBinding.html
  name: ThemeBinding
  nameWithType: ThemeBinding
  fullName: DrawnUi.Draw.ThemeBinding
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: DrawnUi.Draw.ThemeBindings.GetThemeValue*
  commentId: Overload:DrawnUi.Draw.ThemeBindings.GetThemeValue
  href: DrawnUi.Draw.ThemeBindings.html#DrawnUi_Draw_ThemeBindings_GetThemeValue__1___0___0___0_
  name: GetThemeValue
  nameWithType: ThemeBindings.GetThemeValue
  fullName: DrawnUi.Draw.ThemeBindings.GetThemeValue
- uid: '{T}'
  commentId: '!:T'
  definition: T
  name: T
  nameWithType: T
  fullName: T
- uid: T
  name: T
  nameWithType: T
  fullName: T
- uid: DrawnUi.Draw.ThemeBindings.WithThemeBinding*
  commentId: Overload:DrawnUi.Draw.ThemeBindings.WithThemeBinding
  href: DrawnUi.Draw.ThemeBindings.html#DrawnUi_Draw_ThemeBindings_WithThemeBinding__1___0_Microsoft_Maui_Controls_BindableProperty_System_Object_System_Object_System_Object_
  name: WithThemeBinding
  nameWithType: ThemeBindings.WithThemeBinding
  fullName: DrawnUi.Draw.ThemeBindings.WithThemeBinding
- uid: '{TControl}'
  commentId: '!:TControl'
  definition: TControl
  name: TControl
  nameWithType: TControl
  fullName: TControl
- uid: TControl
  name: TControl
  nameWithType: TControl
  fullName: TControl
