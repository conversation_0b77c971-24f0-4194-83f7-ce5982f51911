﻿using MobileAPIWrapper;
using MobileAPIWrapper.Methods;
using MobileAPIWrapper.Methods.General.Users;
using MobileAPIWrapper.Methods.Library;

using System.Linq;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models;
using Triggero.Models.Enums;
using Triggero.Models.General.UserStats;
using Triggero.Models.Practices;
using System.Net.Http;

namespace Triggero.MauiMobileApp.Extensions.Helpers.Modules
{
    public static class StatsHelper
    {
        #region Просмотры
        public async static Task AddExerciseWatch(int id)
        {
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.LibraryMethods.ExercisesMethods.AddWatch(id);
            }
            else
            {
                string url = ExercisesMethods.BASE_HOST + $"AddWatch?exerciseId={id}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put));
            }
        }
        public async static Task AddTopicWatch(int id)
        {
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.LibraryMethods.TopicsMethods.AddWatch(id);
            }
            else
            {
                string url = TopicsMethods.BASE_HOST + $"AddWatch?topicId={id}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put));
            }
        }
        public async static Task AddPracticeWatch(int id)
        {
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.LibraryMethods.PracticesMethods.AddWatch(id);
            }
            else
            {
                string url = PracticesMethods.BASE_HOST + $"AddWatch?practiceId={id}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put));
            }
        }
        public async static Task AddTestWatch(int id)
        {
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.TestsMethods.AddWatch(id);
            }
            else
            {
                string url = TestsMethods.BASE_HOST + $"AddWatch?testId={id}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put));
            }
        }
        #endregion

        #region Результаты прохождения
        public async static Task AddExercisePassingResult(Exercise exercise, FeelingEmojiType type)
        {
            var result = new ExercisePassingResult
            {
                ExerciseId = exercise.Id,
                Exercise = exercise,
                EmojiType = type
            };

            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.GeneralMethods.UserMethods.UserStatisticsMethods.AddExercisePassingResult(AuthHelper.UserId, result);
            }
            else
            {
                string url = UserStatisticsMethods.BASE_HOST + $"AddExercisePassingResult?userId={AuthHelper.UserId}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put, result));
            }

            var found = ApplicationState.Data.ExercisePassingResults.FirstOrDefault(o => o.ExerciseId == exercise.Id);
            if (found == null)
            {
                ApplicationState.Data.ExercisePassingResults.Add(new ExercisePassingResult
                {
                    Exercise = exercise,
                    ExerciseId = exercise.Id,
                    EmojiType = type
                });
            }
            else
            {
                found.EmojiType = type;
            }
        }
        public async static Task AddPracticePassingResult(int id)
        {
            var result = new PracticePassingResult { PracticeId = id };
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.GeneralMethods.UserMethods.UserStatisticsMethods.AddPracticePassingResult(AuthHelper.UserId, result);
            }
            else
            {
                string url = UserStatisticsMethods.BASE_HOST + $"AddPracticePassingResult?userId={AuthHelper.UserId}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put, result));
            }
        }
        public async static Task AddBreathPracticePassingResult(int id)
        {
            var result = new BreathPracticePassingResult { BreathPracticeId = id };
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.GeneralMethods.UserMethods.UserStatisticsMethods.AddBreathPracticePassingResult(AuthHelper.UserId, result);
            }
            else
            {
                string url = UserStatisticsMethods.BASE_HOST + $"AddBreathPracticePassingResult?userId={AuthHelper.UserId}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put, result));
            }
        }
        public async static Task AddTestPassingResult(TestPassingResult result)
        {
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.GeneralMethods.UserMethods.UserStatisticsMethods.AddTestPassingResult(AuthHelper.UserId, result);
            }
            else
            {
                string url = UserStatisticsMethods.BASE_HOST + $"AddTestPassingResult?userId={AuthHelper.UserId}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put, result));
            }

            ApplicationState.Data.TestPassingResults.Add(result);
            ApplicationState.Data.SaveChangesToMemory();
        }
        #endregion

        public async static Task<bool> ToggleTopicRate(int topicId, RateType rate)
        {
            bool result = false;
            if (ConnectionHelper.HasInternet())
            {
                result = await TriggeroMobileAPI.LibraryMethods.TopicsMethods.ToggleRate(AuthHelper.UserId, topicId, rate);
            }
            else
            {
                string url = TopicsMethods.BASE_HOST + $"ToggleRate?userId={AuthHelper.UserId}&topicId={topicId}&rateType={(int)rate}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put));
            }
            return result;
        }
    }
}
