<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Class Pendulum | DrawnUI for .NET MAUI </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Class Pendulum | DrawnUI for .NET MAUI ">
      
      <meta name="description" content="DrawnUI for .NET MAUI - Rendering engine built on SkiaSharp. Create pixel-perfect cross-platform apps for iOS, Android, Windows, MacCatalyst with advanced animations, gestures, and visual effects.">
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      <meta name="docfx:docurl" content="https://github.com/taublast/drawnui/new/master/apiSpec/new?filename=DrawnUi_Infrastructure_Pendulum.md&amp;value=---%0Auid%3A%20DrawnUi.Infrastructure.Pendulum%0Asummary%3A%20&#39;*You%20can%20override%20summary%20for%20the%20API%20here%20using%20*MARKDOWN*%20syntax&#39;%0A---%0A%0A*Please%20type%20below%20more%20information%20about%20this%20API%3A*%0A%0A">
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/drawnuint38.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Infrastructure.Pendulum">



  <h1 id="DrawnUi_Infrastructure_Pendulum" data-uid="DrawnUi.Infrastructure.Pendulum" class="text-break">
Class Pendulum  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L42"><i class="bi bi-code-slash"></i></a>
  </h1>

  <div class="facts text-secondary">
    <dl><dt>Namespace</dt><dd><a class="xref" href="DrawnUi.html">DrawnUi</a>.<a class="xref" href="DrawnUi.Infrastructure.html">Infrastructure</a></dd></dl>
  <dl><dt>Assembly</dt><dd>DrawnUi.Maui.dll</dd></dl>
  </div>

  <div class="markdown summary"></div>
  <div class="markdown conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public class Pendulum</code></pre>
  </div>




  <dl class="typelist inheritance">
    <dt>Inheritance</dt>
    <dd>
      <div><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object">object</a></div>
      <div><span class="xref">Pendulum</span></div>
    </dd>
  </dl>


  <dl class="typelist derived">
    <dt>Derived</dt>
    <dd>
      <div><a class="xref" href="DrawnUi.Infrastructure.PerpetualPendulum.html">PerpetualPendulum</a></div>
    </dd>
  </dl>

  <dl class="typelist inheritedMembers">
    <dt>Inherited Members</dt>
    <dd>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)">object.Equals(object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)">object.Equals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gethashcode">object.GetHashCode()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.gettype">object.GetType()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone">object.MemberwiseClone()</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.referenceequals">object.ReferenceEquals(object, object)</a>
    </div>
    <div>
      <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.object.tostring">object.ToString()</a>
    </div>
  </dd></dl>

  <dl class="typelist extensionMethods">
    <dt>Extension Methods</dt>
    <dd>
  <div>
      <a class="xref" href="DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_">InternalExtensions.FromPlatform(object)</a>
  </div>
  </dd></dl>





  <h2 class="section" id="constructors">Constructors
</h2>


  <a id="DrawnUi_Infrastructure_Pendulum__ctor_" data-uid="DrawnUi.Infrastructure.Pendulum.#ctor*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum__ctor" data-uid="DrawnUi.Infrastructure.Pendulum.#ctor">
  Pendulum()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L82"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Pendulum()</code></pre>
  </div>













  <h2 class="section" id="fields">Fields
</h2>



  <h3 id="DrawnUi_Infrastructure_Pendulum_accelerationVector" data-uid="DrawnUi.Infrastructure.Pendulum.accelerationVector">
  accelerationVector
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L45"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Vector accelerationVector</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Infrastructure.Vector.html">Vector</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Infrastructure_Pendulum_angle" data-uid="DrawnUi.Infrastructure.Pendulum.angle">
  angle
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L73"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected double angle</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Infrastructure_Pendulum_angularAcceleration" data-uid="DrawnUi.Infrastructure.Pendulum.angularAcceleration">
  angularAcceleration
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L48"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected double angularAcceleration</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Infrastructure_Pendulum_velocityVector" data-uid="DrawnUi.Infrastructure.Pendulum.velocityVector">
  velocityVector
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L46"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Vector velocityVector</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Infrastructure.Vector.html">Vector</a></dt>
    <dd></dd>
  </dl>










  <h3 id="DrawnUi_Infrastructure_Pendulum_wireVector" data-uid="DrawnUi.Infrastructure.Pendulum.wireVector">
  wireVector
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L44"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">protected Vector wireVector</code></pre>
  </div>




  <h4 class="section">Field Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Infrastructure.Vector.html">Vector</a></dt>
    <dd></dd>
  </dl>









  <h2 class="section" id="properties">Properties
</h2>


  <a id="DrawnUi_Infrastructure_Pendulum_AirResistance_" data-uid="DrawnUi.Infrastructure.Pendulum.AirResistance*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_AirResistance" data-uid="DrawnUi.Infrastructure.Pendulum.AirResistance">
  AirResistance
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L69"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double AirResistance { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Infrastructure_Pendulum_AngularVelocity_" data-uid="DrawnUi.Infrastructure.Pendulum.AngularVelocity*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_AngularVelocity" data-uid="DrawnUi.Infrastructure.Pendulum.AngularVelocity">
  AngularVelocity
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L49"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double AngularVelocity { get; protected set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Infrastructure_Pendulum_Gravity_" data-uid="DrawnUi.Infrastructure.Pendulum.Gravity*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_Gravity" data-uid="DrawnUi.Infrastructure.Pendulum.Gravity">
  Gravity
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L71"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double Gravity { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <a id="DrawnUi_Infrastructure_Pendulum_RodLength_" data-uid="DrawnUi.Infrastructure.Pendulum.RodLength*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_RodLength" data-uid="DrawnUi.Infrastructure.Pendulum.RodLength">
  RodLength
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L51"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double RodLength { get; set; }</code></pre>
  </div>





  <h4 class="section">Property Value</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>








  <h2 class="section" id="methods">Methods
</h2>


  <a id="DrawnUi_Infrastructure_Pendulum_Reset_" data-uid="DrawnUi.Infrastructure.Pendulum.Reset*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_Reset" data-uid="DrawnUi.Infrastructure.Pendulum.Reset">
  Reset()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L134"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void Reset()</code></pre>
  </div>













  <a id="DrawnUi_Infrastructure_Pendulum_SetAmplitude_" data-uid="DrawnUi.Infrastructure.Pendulum.SetAmplitude*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_SetAmplitude_System_Double_" data-uid="DrawnUi.Infrastructure.Pendulum.SetAmplitude(System.Double)">
  SetAmplitude(double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L141"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void SetAmplitude(double value)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>value</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Infrastructure_Pendulum_Update_" data-uid="DrawnUi.Infrastructure.Pendulum.Update*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_Update_System_Double_" data-uid="DrawnUi.Infrastructure.Pendulum.Update(System.Double)">
  Update(double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L91"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public virtual void Update(double timeStep)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>timeStep</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Infrastructure_Pendulum_getAccelerationVector_" data-uid="DrawnUi.Infrastructure.Pendulum.getAccelerationVector*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_getAccelerationVector" data-uid="DrawnUi.Infrastructure.Pendulum.getAccelerationVector">
  getAccelerationVector()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L157"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector getAccelerationVector()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Infrastructure.Vector.html">Vector</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pendulum_getAirResistence_" data-uid="DrawnUi.Infrastructure.Pendulum.getAirResistence*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_getAirResistence" data-uid="DrawnUi.Infrastructure.Pendulum.getAirResistence">
  getAirResistence()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L153"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double getAirResistence()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pendulum_getAngle_" data-uid="DrawnUi.Infrastructure.Pendulum.getAngle*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_getAngle" data-uid="DrawnUi.Infrastructure.Pendulum.getAngle">
  getAngle()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L149"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double getAngle()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pendulum_getAngularAcceleration_" data-uid="DrawnUi.Infrastructure.Pendulum.getAngularAcceleration*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_getAngularAcceleration" data-uid="DrawnUi.Infrastructure.Pendulum.getAngularAcceleration">
  getAngularAcceleration()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L160"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double getAngularAcceleration()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pendulum_getAngularVelocity_" data-uid="DrawnUi.Infrastructure.Pendulum.getAngularVelocity*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_getAngularVelocity" data-uid="DrawnUi.Infrastructure.Pendulum.getAngularVelocity">
  getAngularVelocity()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L161"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double getAngularVelocity()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pendulum_getInitialAngle_" data-uid="DrawnUi.Infrastructure.Pendulum.getInitialAngle*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_getInitialAngle" data-uid="DrawnUi.Infrastructure.Pendulum.getInitialAngle">
  getInitialAngle()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L143"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double getInitialAngle()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pendulum_getInitialVelocity_" data-uid="DrawnUi.Infrastructure.Pendulum.getInitialVelocity*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_getInitialVelocity" data-uid="DrawnUi.Infrastructure.Pendulum.getInitialVelocity">
  getInitialVelocity()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L146"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public double getInitialVelocity()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pendulum_getVelocityVector_" data-uid="DrawnUi.Infrastructure.Pendulum.getVelocityVector*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_getVelocityVector" data-uid="DrawnUi.Infrastructure.Pendulum.getVelocityVector">
  getVelocityVector()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L158"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector getVelocityVector()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Infrastructure.Vector.html">Vector</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pendulum_getWireVector_" data-uid="DrawnUi.Infrastructure.Pendulum.getWireVector*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_getWireVector" data-uid="DrawnUi.Infrastructure.Pendulum.getWireVector">
  getWireVector()
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L156"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public Vector getWireVector()</code></pre>
  </div>


  <h4 class="section">Returns</h4>
  <dl class="parameters">
    <dt><a class="xref" href="DrawnUi.Infrastructure.Vector.html">Vector</a></dt>
    <dd></dd>
  </dl>











  <a id="DrawnUi_Infrastructure_Pendulum_setAirResistance_" data-uid="DrawnUi.Infrastructure.Pendulum.setAirResistance*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_setAirResistance_System_Double_" data-uid="DrawnUi.Infrastructure.Pendulum.setAirResistance(System.Double)">
  setAirResistance(double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L154"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void setAirResistance(double airResistence)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>airResistence</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Infrastructure_Pendulum_setInitialAngle_" data-uid="DrawnUi.Infrastructure.Pendulum.setInitialAngle*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_setInitialAngle_System_Double_" data-uid="DrawnUi.Infrastructure.Pendulum.setInitialAngle(System.Double)">
  setInitialAngle(double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L144"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void setInitialAngle(double initialAngle)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>initialAngle</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>












  <a id="DrawnUi_Infrastructure_Pendulum_setInitialVelocity_" data-uid="DrawnUi.Infrastructure.Pendulum.setInitialVelocity*"></a>

  <h3 id="DrawnUi_Infrastructure_Pendulum_setInitialVelocity_System_Double_" data-uid="DrawnUi.Infrastructure.Pendulum.setInitialVelocity(System.Double)">
  setInitialVelocity(double)
  <a class="header-action link-secondary" title="View source" href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L147"><i class="bi bi-code-slash"></i></a>
  </h3>

  <div class="markdown level1 summary"></div>
  <div class="markdown level1 conceptual"></div>

  <div class="codewrapper">
    <pre><code class="lang-csharp hljs">public void setInitialVelocity(double initialVelocity)</code></pre>
  </div>

  <h4 class="section">Parameters</h4>
  <dl class="parameters">
    <dt><code>initialVelocity</code> <a class="xref" href="https://learn.microsoft.com/dotnet/api/system.double">double</a></dt>
    <dd></dd>
  </dl>













</article>

        <div class="contribution d-print-none">
          <a href="https://github.com/taublast/DrawnUi/blob/2-A/src/Shared/Draw/Internals/Helpers/Pendulum.cs/#L42" class="edit-link">Edit this page</a>
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          Made by <a href="https://taublast.github.io/about/">Nick Kovalsky aka AppoMobi (@taublast)</a>
        </div>
      </div>
    </footer>
  </body>
</html>
