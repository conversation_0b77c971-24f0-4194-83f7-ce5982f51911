﻿<?xml version="1.0" encoding="utf-8" ?>
<pages1:BasePage
    x:Class="Triggero.MauiPort.Views.Pages.MainPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:parts="clr-namespace:Triggero.Controls.Parts"
    xmlns:pages1="clr-namespace:Triggero.MauiMobileApp.Views.Pages"
    x:Name="ThisPage"
    BackgroundColor="White">

    <ContentPage.Content>

        <!--  Root layout grid with switchable views  -->
        <!--RowDefinitions="*, 34, Auto"-->
        <Grid
            HorizontalOptions="Fill"
            VerticalOptions="Fill"
            RowSpacing="0">

            <!--  HOME  -->
            <Grid
                x:Name="MainViewGrid"
                Grid.Row="0"
                Grid.RowSpan="2"
                Margin="{x:OnPlatform Android='0',
                                    iOS='0,-60,0,0'}"
                Padding="0"
                x:FieldModifier="public"
                ChildAdded="onChildAdded"
                IsVisible="True" />

            <!--  LIBRARY  -->
            <Grid
                x:Name="LibraryGrid"
                Grid.Row="0"
                Grid.RowSpan="2"
                Padding="0"
                x:FieldModifier="public"
                ChildAdded="onChildAdded"
                IsVisible="False" />

            <!--  TESTS  -->
            <Grid
                x:Name="TestsGrid"
                Grid.Row="0"
                Grid.RowSpan="2"
                Padding="0"
                x:FieldModifier="public"
                ChildAdded="onChildAdded"
                IsVisible="False" />

            <!--  CHAT  -->
            <Grid
                x:Name="ChatBotGrid"
                Grid.Row="0"
                Grid.RowSpan="2"
                Padding="0"
                x:FieldModifier="public"
                ChildAdded="onChildAdded"
                IsVisible="False" />

            <!--  CONTENT  -->
            <Grid
                x:Name="contentGrid"
                Grid.Row="0"
                Grid.RowSpan="2"
                Padding="0"
                x:FieldModifier="public"
                ChildAdded="onChildAdded"
                IsVisible="False" />

            <!--  FOOTER  -->
            <parts:Footer
                VerticalOptions="End"
                x:Name="Footer"
                x:FieldModifier="public"
                MainPage="{Binding Source={x:Reference ThisPage}}" />

        </Grid>

    </ContentPage.Content>

</pages1:BasePage>