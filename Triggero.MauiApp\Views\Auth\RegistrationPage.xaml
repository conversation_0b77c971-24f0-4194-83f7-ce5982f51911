﻿<?xml version="1.0" encoding="utf-8" ?>
<pages1:BasePage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.RegistrationPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pages="clr-namespace:Triggero.MauiMobileApp.Views.Pages;assembly=Triggero.MauiMobileApp"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:pages1="clr-namespace:Triggero.MauiMobileApp.Views.Pages"
    x:Name="this">
    <ContentPage.Content>
        <Grid>


            <Image
                Aspect="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"
                VerticalOptions="Fill" />

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="183" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">


                    <Label
                        Margin="20,0,0,20"
                        FontAttributes="Bold"
                        FontSize="22"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.CreateYourAccount}"
                        TextColor="{x:StaticResource greyTextColor}"
                        VerticalOptions="End" />

                </Grid>

                <pancakeview:PancakeView
                    Grid.Row="1"
                    Padding="0"
                    BackgroundColor="#FFFFFF"
                    StrokeShape="RoundRectangle 15,15,0,0">
                    <Grid>

                        <ScrollView>
                            <StackLayout
                                Margin="20,20,20,0"
                                Spacing="0">

                                <Label
                                    Margin="0,0,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Start"
                                    Opacity="0.5"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    VerticalOptions="Start">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="*" />
                                                <Span Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.YourName}" />
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>
                                <Entry
                                    Margin="0,4,0,0"
                                    HeightRequest="50"
                                    HorizontalOptions="Fill"
                                    Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.EnterYourName}"
                                    Style="{x:StaticResource grayTextEdit}"
                                    Text="{Binding Source={x:Reference this}, Path=UserModel.Name, Mode=TwoWay}"
                                    VerticalOptions="Start" />



                                <Label
                                    Margin="0,20,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Start"
                                    Opacity="0.5"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    VerticalOptions="Start">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="*" />
                                                <Span Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.Email}" />
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                                <!--todo                                     CharacterCasing="Lower"-->
                                <Entry
                                    Margin="0,4,0,0"
                                    HeightRequest="50"
                                    HorizontalOptions="Fill"
                                    Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.EnterEmail}"
                                    Style="{x:StaticResource grayTextEdit}"
                                    Text="{Binding Source={x:Reference this}, Path=UserModel.Email, Mode=TwoWay}"
                                    VerticalOptions="Start" />



                                <Label
                                    Margin="0,20,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Start"
                                    Opacity="0.5"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    VerticalOptions="Start">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="*" />
                                                <Span Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.Phone}" />
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>
                                
                                <Entry
                                    Margin="0,4,0,0"
                                    HeightRequest="50"
                                    HorizontalOptions="Fill"
                                    Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.EnterPhone}"
                                    Style="{x:StaticResource grayTextEdit}"
                                    Text="{Binding Source={x:Reference this}, Path=UserModel.Phone, Mode=TwoWay}"
                                    VerticalOptions="Start" />


                                <Label
                                    Margin="0,20,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Start"
                                    Opacity="0.5"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    VerticalOptions="Start">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="*" />
                                                <Span Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.Password}" />
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>
                                <Grid
                                    Margin="0,4,0,0"
                                    HeightRequest="50">
                                    <Entry
                                        IsPassword="True"
                                        HeightRequest="50"
                                        HorizontalOptions="Fill"
                                        Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.EnterPassword}"
                                        Style="{x:StaticResource grayTextEdit}"
                                        Text="{Binding Source={x:Reference this}, Path=UserModel.Password, Mode=TwoWay}"
                                        VerticalOptions="Start" />
                                </Grid>



                                <StackLayout
                                    Margin="-5,20,0,0"
                                    Orientation="Horizontal"
                                    Spacing="4">
                                    <CheckBox
                                        IsChecked="{Binding Source={x:Reference this}, Path=PrivacyAggreed, Mode=TwoWay}"
                                        Color="#EBEBEB" />


                                    <!--  PRVACY CHECK  -->
                                    <Label
                                        Margin="0,6,0,0"
                                        FontSize="12"

                                        HorizontalOptions="Start"
                                        TextColor="{x:StaticResource greyTextColor}"
                                        WidthRequest="320">
                                        <Label.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=CommandPrivacy}" />
                                        </Label.GestureRecognizers>
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <FormattedString.Spans>
                                                    <Span Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.AcceptRules}" />
                                                    <Span Text=" " />
                                                    <Span
                                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.PrivacyPolicyLowercase}"
                                                        TextDecorations="Underline" />
                                                </FormattedString.Spans>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                </StackLayout>

                                <!--  ERROR  -->
                                <Label
                                    x:Name="errorLabel"
                                    Margin="0,0,0,0"
                                    FontSize="14"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    IsVisible="False"
                                    Opacity="0.5"
                                    Text=""
                                    TextColor="{x:StaticResource blueColor}"
                                    VerticalOptions="Start" />

                                <Button
                                    Margin="0,40,0,0"
                                    Command="{Binding Source={x:Reference this}, Path=CommandRegisterAccount}"
                                    HeightRequest="56"
                                    Style="{x:StaticResource yellow_btn}"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.CreateAccount}"
                                    VerticalOptions="Start" />

                                <StackLayout
                                    Margin="0,26,0,0"
                                    HorizontalOptions="Center"
                                    Orientation="Horizontal"
                                    Spacing="12">
                                    <Label
                                        FontSize="12"
                                        Opacity="0.5"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.HaveAlreadyAccount}"
                                        TextColor="{x:StaticResource greyTextColor}" />

                                    <Label
                                        FontAttributes="Bold"
                                        FontSize="12"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.CreateAccount.SignIn}"
                                        TextColor="{x:StaticResource greyTextColor}"
                                        TextDecorations="Underline">
                                        <Label.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToLogin}" />
                                        </Label.GestureRecognizers>
                                    </Label>
                                </StackLayout>


                            </StackLayout>
                        </ScrollView>

                    </Grid>
                </pancakeview:PancakeView>

            </Grid>

        </Grid>
    </ContentPage.Content>
</pages1:BasePage>