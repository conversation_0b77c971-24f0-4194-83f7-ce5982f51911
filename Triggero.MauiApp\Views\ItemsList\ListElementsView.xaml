﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="Triggero.MauiMobileApp.Views.ListElementsView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:viewModels="clr-namespace:Triggero.MauiMobileApp.ViewModels"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="this"
    x:DataType="viewModels:ElementsListViewModel">

    <ContentView.Content>

        <Grid
            x:Name="mainGrid"
            Padding="0"
            BackgroundColor="#AEAA9F"
            RowSpacing="0">

            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition
                    x:Name="mainGridDef1"
                    Height="50" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="1">


                <ImageButton
                    x:Name="arrowBackBtn"
                    Margin="16,0,0,0"
                    BackgroundColor="Transparent"
                    Command="{Binding Source={x:Reference this}, Path=GoBack}"
                    CornerRadius="0"
                    HeightRequest="18"
                    HorizontalOptions="Start"
                    Source="arrowBackWhite.png"
                    VerticalOptions="Center"
                    WidthRequest="18" />


                <Grid
                    x:Name="pageBackNavigationGrid"
                    Margin="20,0,20,30"
                    Opacity="0"
                    VerticalOptions="End">

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="44" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <ImageButton
                        Grid.Column="0"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=GoBack}"
                        CornerRadius="0"
                        HeightRequest="44"
                        HorizontalOptions="Center"
                        Source="buttonBackBordered.png"
                        VerticalOptions="Center"
                        WidthRequest="44" />

                    <Label
                        x:Name="categoryLabel"
                        Grid.Column="1"
                        Margin="20,0,0,0"
                        IsVisible="False"
                        Opacity="0.001"
                        Style="{x:StaticResource StyleHeaderText}"
                        Text="{Binding Title}"
                        VerticalOptions="Center" />

                </Grid>


            </Grid>

            <pancakeview:PancakeView
                Grid.Row="2"
                Padding="0"
                BackgroundColor="#FFFFFF"
                StrokeShape="RoundRectangle 15,15,0,0">

                <Grid>

                    <Grid.RowDefinitions>
                        <RowDefinition
                            x:Name="rowDefPancake1"
                            Height="60" />
                        <RowDefinition Height="45" />
                        <RowDefinition Height="*" />
                    </Grid.RowDefinitions>


                    <Grid Grid.Row="0">

                        <Label
                            x:Name="categoryLabelCollapsed"
                            Margin="20,15,0,0"
                            Style="{x:StaticResource StyleHeaderText}"
                            Text="{Binding Title}"
                            VerticalOptions="Center" />

                    </Grid>

                    <!--  HEADER  -->
                    <Grid Grid.Row="1">

                        <Frame
                            Margin="20,0,20,0"
                            Padding="0"
                            Background="Transparent"
                            BackgroundColor="Transparent"
                            BorderColor="{x:StaticResource lightBlueColor}"
                            CornerRadius="12"
                            HasShadow="False"
                            HeightRequest="32"
                            VerticalOptions="Center">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*" />
                                    <ColumnDefinition Width="1*" />
                                </Grid.ColumnDefinitions>

                                <RadioButton
                                    Grid.Column="0"
                                    CheckedChanged="filterByNewCheckedChanged"
                                    Content="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.FilterNew}"
                                    IsChecked="True"
                                    Style="{x:StaticResource yellow_rb}" />

                                <RadioButton
                                    Grid.Column="1"
                                    CheckedChanged="filterByWatchesCheckedChanged"
                                    Content="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.FilterPopular}"
                                    Style="{x:StaticResource yellow_rb}" />

                            </Grid>
                        </Frame>

                    </Grid>

                    <!--  LIST  -->


                    <!--  NATIVE  -->
                    <!--<CollectionView
                           Grid.Row="2
                            x:Name="collection"
                            Margin="20,25,20,0"
                            Scrolled="onScrolled">
                            <CollectionView.ItemTemplate>
                                <DataTemplate>
                                    <templates:ExerciseCard
                                        Padding="0,6,0,0"
                                        HeightRequest="104"
                                        HorizontalOptions="Fill"
                                        VerticalOptions="Start" />

                                </DataTemplate>
                            </CollectionView.ItemTemplate>
                        </CollectionView>-->

                    <!--  DRAWN  -->
                    <draw:Canvas
                        x:Name="DrawnCanvas"
                        Grid.Row="2"
                        Margin="10,6,10,0"
                        Gestures="Lock"
                        RenderingMode="Accelerated"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill">

                        <draw:SkiaLayout
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill">

                            <draw:SkiaScroll
                                FrictionScrolled="0.35"
                                HorizontalOptions="Fill"
                                Scrolled="SkiaScroll_Scrolled"
                                VerticalOptions="Fill">

                                <draw:SkiaLayout
                                    x:Name="StackCells"
                                    Padding="0,0,0,0"
                                    HorizontalOptions="Fill"
                                    ItemTemplate="{Binding ItemTemplate}"
                                    ItemsSource="{Binding Items}"
                                    RecyclingTemplate="Enabled"
                                    MeasureItemsStrategy="MeasureVisible"
                                    Spacing="-8"
                                    Type="Column"
                                    VirtualisationInflated="200" />

                            </draw:SkiaScroll>

                            <draw:SkiaLabel
                                IsVisible="{x:Static triggeroV2:Globals.ShowDebugInfo}"
                                UseCache="Operations"
                                Margin="8,0,8,100"
                                BackgroundColor="Black"
                                HorizontalOptions="Start"
                                InputTransparent="True"
                                Text="{Binding Source={x:Reference StackCells}, Path=DebugString}"
                                TextColor="Lime"
                                VerticalOptions="End" />

                            <!--  FPS  -->
                            <draw:SkiaLabelFps
                                Margin="0,0,4,84"
                                BackgroundColor="DarkRed"
                                ForceRefresh="False"
                                HorizontalOptions="End"
                                IsVisible="{x:Static triggeroV2:Globals.ShowFPS}"
                                Rotation="-45"
                                TextColor="White"
                                VerticalOptions="End" />

                        </draw:SkiaLayout>

                    </draw:Canvas>

                    <!--<ActivityIndicator
                        Grid.Row="2"
                        HorizontalOptions="Center"
                        IsRunning="{Binding IsBusy}"
                        IsVisible="{Binding IsBusy}"
                        VerticalOptions="Center" />-->

                </Grid>

            </pancakeview:PancakeView>


        </Grid>
    </ContentView.Content>
</ContentView>