﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage3"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="170"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="140"/>
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="3"
                Aspect="Fill"
                Source="tutorialBlur3.png"/>

            <Grid Grid.Row="0">

             
            </Grid>

            <Grid Grid.Row="1">

                <StackLayout
                    Margin="0,-10,0,0"
                    Spacing="12">


                    <Grid
                        Margin="-3,0,0,0"
                        HorizontalOptions="Fill"
                        VerticalOptions="Start"
                        HeightRequest="269">
                        <Image
                            Aspect="Fill"
                            Source="tutorialContainer1.png"/>

                        <Grid
                            HeightRequest="210"
                            RowSpacing="12"
                            Margin="20,20,20,20">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="1*"/>
                                <RowDefinition Height="1*"/>
                            </Grid.RowDefinitions>

                            <Grid Grid.Row="0"
                                  ColumnSpacing="12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*"/>
                                    <ColumnDefinition Width="1*"/>
                                </Grid.ColumnDefinitions>

                                <Grid Grid.Column="0"
                                       Padding="0">
                                   
                                    <Grid>

                                        <Frame 
                                            Opacity="0.4"      
                                            CornerRadius="16"
                                            HasShadow="False">
                                            <Frame.Background>
                                                <LinearGradientBrush>
                                                    <LinearGradientBrush.GradientStops>
                                                        <GradientStop Color="#F9EDC7" Offset="0.1" />
                                                        <GradientStop Color="#ECA069" Offset="1.0" />
                                                    </LinearGradientBrush.GradientStops>
                                                </LinearGradientBrush>
                                            </Frame.Background>
                                        </Frame>
                                        
                                        
                                        <Label 
                                            TextColor="{x:StaticResource greyTextColor}"
                                            FontSize="{x:OnPlatform Android=Default,iOS=14}"
                                            FontAttributes="Bold"
                                            HorizontalOptions="Start"
                                            VerticalOptions="Start"
                                            WidthRequest="49"
                                            Margin="12,16,0,0"
                                            Text="Узнай себя"/>
                                        <Image
                                            Source="knowYourself.png"
                                            HorizontalOptions="End"
                                            VerticalOptions="Start"
                                            Margin="0,-10,12,0"
                                            HeightRequest="94"
                                            WidthRequest="100"/>
                                    </Grid>
                                </Grid>

                                <Image Grid.Column="1"
                                       Aspect="Fill"
                                       Source="breathBlured.png">
                                   
                                </Image>


                            </Grid>

                            <Grid Grid.Row="1">
                                <Image
                                    Aspect="Fill"
                                    Source="practicesBlured.png"/>
                            </Grid>

                        </Grid>

                    </Grid>
                   


                    <Label 
                        Margin="0,60,0,0"
                        TextColor="#000000"
                        FontAttributes="Bold"
                        FontSize="{x:OnPlatform Android=Large,iOS=19}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="317"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage3.Header}"/>
                    <Label 
                        Margin="0,0,0,0"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="{x:OnPlatform Android=Default,iOS=16}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="307"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage3.Description}"/>


                </StackLayout>

            </Grid>

            <Grid Grid.Row="2">
                <Button 
                    Command="{Binding Source={x:Reference this},Path=GoNext}"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill"
                    Margin="63,0,63,0"
                    HeightRequest="56"
                    FontSize="17"
                    FontAttributes="Bold"
                    Style="{x:StaticResource yellow_btn}"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage3.GoNext}"/>

            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>