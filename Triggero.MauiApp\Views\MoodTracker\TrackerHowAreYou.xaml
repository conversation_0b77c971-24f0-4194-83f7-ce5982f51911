﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.MoodTracker.TrackerHowAreYou"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:moodtracker="clr-namespace:Triggero.Controls.MoodTracker"
    
    x:Name="this"
    Background="White"
    BackgroundColor="White">
    <ContentPage.Content>
        <Grid>
            <Image
                Aspect="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"
                VerticalOptions="Fill" />

            <ScrollView>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="152" />
                        <RowDefinition Height="710" />
                        <RowDefinition Height="110" />
                    </Grid.RowDefinitions>



                    <Grid Grid.Row="0">

                        <StackLayout
                            HorizontalOptions="Fill"
                            Spacing="8"
                            VerticalOptions="End">
                            <Label
                                FontAttributes="Bold"
                                FontFamily="FontTextLight"
                                FontSize="17"
                                HorizontalOptions="Center"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerHowAreYou.HowAreYou}"
                                TextColor="{x:StaticResource greyTextColor}"
                                VerticalOptions="Center" />
                            <Label
                                FontAttributes="None"
                                FontFamily="FontTextLight"
                                FontSize="14"
                                HorizontalOptions="Center"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerHowAreYou.HowAreYouDescription}"
                                TextColor="{x:StaticResource ColorTextGray}"
                                VerticalOptions="Center" />
                        </StackLayout>

                    </Grid>


                    <!--<sh:Shadows
                        Grid.Row="1"
                        Margin="20,20,20,20"
                        Shades="{sh:SingleShade Offset='2, 2',
                                                BlurRadius=12,
                                                Opacity=0.06,
                                                Color=#27527A}">-->

                    <moodtracker:TrackerFeelingsView
                        Grid.Row="1"
                        Margin="20,20,20,20"
                        Tracker="{Binding Source={x:Reference this}, Path=Tracker}" />

                    <!--</sh:Shadows>-->

                    <Grid Grid.Row="2">
                        <Grid
                            Margin="20,0,20,0"
                            ColumnSpacing="16"
                            HeightRequest="56"
                            VerticalOptions="Start">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="56" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <ImageButton
                                Grid.Column="0"
                                BackgroundColor="Transparent"
                                Command="{Binding Source={x:Reference this}, Path=GoBack}"
                                CornerRadius="0"
                                HeightRequest="56"
                                HorizontalOptions="Center"
                                Source="buttonBackBordered.png"
                                VerticalOptions="Center"
                                WidthRequest="56" />

                            <Button
                                x:Name="goNextBtn"
                                Grid.Column="1"
                                Command="{Binding Source={x:Reference this}, Path=GoNext}"
                                HeightRequest="56"
                                Style="{x:StaticResource yellow_btn}"
                                Text="Далее 2/5"
                                VerticalOptions="Fill" />


                        </Grid>
                    </Grid>

                </Grid>
            </ScrollView>
        </Grid>
    </ContentPage.Content>
</ContentPage>