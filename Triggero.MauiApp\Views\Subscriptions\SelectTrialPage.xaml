﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:custom="clr-namespace:Triggero.Custom" 
             x:Class="Triggero.MauiMobileApp.Views.Pages.Subscriptions.SelectTrialPage"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="160"/>
                <RowDefinition Height="240"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="4"
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="trialStartBg.png"/>

            <Grid Grid.Row="0">

                <!--<ImageButton 
                    Source="close.png"
                    Opacity="0.5"
                    VerticalOptions="Center"
                    HorizontalOptions="End"
                    Margin="0,-10,25,0"
                    WidthRequest="14"
                    HeightRequest="14"
                    CornerRadius="0"
                    BackgroundColor="Transparent"/>-->

                <StackLayout 
                    Spacing="8"
                    VerticalOptions="End"
                    Margin="0,0,0,20"
                    HorizontalOptions="Center"
                    Orientation="Horizontal">
                    <Label 
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="22"
                        FontAttributes="Bold"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.TrialPage.StartWeekWith}"/>
                    <Image 
                        Source="logoTitleAqua.png"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        WidthRequest="142"
                        HeightRequest="20"/>
                </StackLayout>
                
            </Grid>

            <Grid Grid.Row="1">
                <StackLayout
                    VerticalOptions="Center"
                    Spacing="0">
      

                    <ScrollView
                        x:Name="sliderScrollView"
                        Scrolled="onSliderScrolled"
                        HeightRequest="205"
                        HorizontalOptions="Fill"
                        VerticalScrollBarVisibility="Never"
                        HorizontalScrollBarVisibility="Never"
                        Orientation="Horizontal">
                        <StackLayout
                            x:Name="sliderLayout"
                            Padding="20,0,200,0"
                            Orientation="Horizontal"
                            Spacing="20">
                            
                        </StackLayout>
                    </ScrollView>
                    
                    <custom:DotsView 
                        Margin="0,20,0,0"
                        HeightRequest="10"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        x:Name="dotsView"/>
                </StackLayout>
            </Grid>

            <Grid Grid.Row="2"
                  ColumnSpacing="12"
                  Margin="20,0,20,0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Grid 
                    x:Name="monthPlanSlot"
                    Grid.Column="0">

                </Grid>

                <Grid 
                    x:Name="threeMonthsPlanSlot"
                    Grid.Column="1">

                </Grid>


                <Grid
                    x:Name="yearPlanSlot"
                    Grid.Column="2">
                   
                </Grid>

            </Grid>


            <Grid>
                
            </Grid>

            <Grid Grid.Row="3">

                <StackLayout 
                    Spacing="0"
                    Margin="20,0,20,0">

                    <Button 
                        Command="{Binding Source={x:Reference this},Path=StartTrial}"
                        Margin="0,0,0,0"
                        VerticalOptions="Start"
                        HeightRequest="56"
                        FontAttributes="Bold"
                        FontSize="17"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.TrialPage.StartTrialWeek}"/>

                    <StackLayout
                        Margin="0,12,0,0"
                        HorizontalOptions="Center"
                        Orientation="Horizontal">
                        
                        <!--<Label 
                            TextColor="{x:StaticResource greyTextColor}"
                            FontSize="12"
                            Text="{Binding Source={x:Static app:App.This},Path=Interface.Subscriptions.TrialPage.RestoreSubscription}"/>-->

                        <StackLayout 
                            IsVisible="False"
                            Orientation="Horizontal"
                            x:Name="appstoreStackLayout">
                            <Label 
                                TextColor="{x:StaticResource greyTextColor}"
                                FontSize="12"
                                Text="Оплатить через Appstore">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToAppstorePage}"/>
                                </Label.GestureRecognizers>
                            </Label>

                            <Label 
                            Opacity="0.5"
                            TextColor="{x:StaticResource greyTextColor}"
                            FontSize="12"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.TrialPage.Or}"/>
                        </StackLayout>
                        
                        

                        <!--<Label 
                            TextColor="{x:StaticResource greyTextColor}"
                            FontSize="12"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.TrialPage.SignIn}">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToLoginPage}"/>
                            </Label.GestureRecognizers>
                        </Label>-->

                    </StackLayout>

                    <Grid
                        Margin="0,30,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                            <ColumnDefinition Width="1*"/>
                        </Grid.ColumnDefinitions>

                        <Grid Grid.Column="0">

                            <Label 
                                Opacity="0.5"
                                TextColor="{x:StaticResource greyTextColor}"
                                FontSize="{x:OnPlatform Android=10,iOS=10}"
                                WidthRequest="80"
                                HorizontalTextAlignment="Center"
                                HorizontalOptions="Center"
                                TextDecorations="Underline"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.SubscriptionMain.Terms}">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToTerms}"/>
                                </Label.GestureRecognizers>
                            </Label>

                        </Grid>

                        <Grid Grid.Column="1">

                            <Label 
                                Opacity="0.5"
                                TextColor="{x:StaticResource greyTextColor}"
                                FontSize="{x:OnPlatform Android=10,iOS=10}"
                                WidthRequest="80"
                                HorizontalTextAlignment="Center"
                                HorizontalOptions="Center"
                                TextDecorations="Underline"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.SubscriptionMain.EULA}">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToEULA}"/>
                                </Label.GestureRecognizers>
                            </Label>

                        </Grid>

                        <Grid Grid.Column="2">
                            <Label 
                                Opacity="0.5"
                                TextColor="{x:StaticResource greyTextColor}"
                                FontSize="{x:OnPlatform Android=10,iOS=10}"
                                WidthRequest="80"
                                HorizontalTextAlignment="Center"
                                HorizontalOptions="Center"
                                TextDecorations="Underline"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Subscriptions.SubscriptionMain.Privacy}">
                                <Label.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToPrivacy}"/>
                                </Label.GestureRecognizers>
                            </Label>
                        </Grid>

                    </Grid>


                </StackLayout>
                
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>