### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.MauiThemeProvider
  commentId: T:DrawnUi.Draw.MauiThemeProvider
  id: MauiThemeProvider
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.MauiThemeProvider.#ctor
  - DrawnUi.Draw.MauiThemeProvider.CurrentTheme
  - DrawnUi.Draw.MauiThemeProvider.ThemeChanged
  langs:
  - csharp
  - vb
  name: MauiThemeProvider
  nameWithType: MauiThemeProvider
  fullName: DrawnUi.Draw.MauiThemeProvider
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MauiThemeProvider
    path: ../src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: MAUI-specific theme provider implementation
  example: []
  syntax:
    content: 'public class MauiThemeProvider : IThemeProvider'
    content.vb: Public Class MauiThemeProvider Implements IThemeProvider
  inheritance:
  - System.Object
  implements:
  - DrawnUi.Draw.IThemeProvider
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.MauiThemeProvider.CurrentTheme
  commentId: P:DrawnUi.Draw.MauiThemeProvider.CurrentTheme
  id: CurrentTheme
  parent: DrawnUi.Draw.MauiThemeProvider
  langs:
  - csharp
  - vb
  name: CurrentTheme
  nameWithType: MauiThemeProvider.CurrentTheme
  fullName: DrawnUi.Draw.MauiThemeProvider.CurrentTheme
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CurrentTheme
    path: ../src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: public AppTheme CurrentTheme { get; }
    parameters: []
    return:
      type: Microsoft.Maui.ApplicationModel.AppTheme
    content.vb: Public ReadOnly Property CurrentTheme As AppTheme
  overload: DrawnUi.Draw.MauiThemeProvider.CurrentTheme*
  implements:
  - DrawnUi.Draw.IThemeProvider.CurrentTheme
- uid: DrawnUi.Draw.MauiThemeProvider.ThemeChanged
  commentId: E:DrawnUi.Draw.MauiThemeProvider.ThemeChanged
  id: ThemeChanged
  parent: DrawnUi.Draw.MauiThemeProvider
  langs:
  - csharp
  - vb
  name: ThemeChanged
  nameWithType: MauiThemeProvider.ThemeChanged
  fullName: DrawnUi.Draw.MauiThemeProvider.ThemeChanged
  type: Event
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ThemeChanged
    path: ../src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
    startLine: 23
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: public event EventHandler<ThemeChangedEventArgs> ThemeChanged
    return:
      type: System.EventHandler{DrawnUi.Draw.ThemeChangedEventArgs}
    content.vb: Public Event ThemeChanged As EventHandler(Of ThemeChangedEventArgs)
  implements:
  - DrawnUi.Draw.IThemeProvider.ThemeChanged
- uid: DrawnUi.Draw.MauiThemeProvider.#ctor
  commentId: M:DrawnUi.Draw.MauiThemeProvider.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.MauiThemeProvider
  langs:
  - csharp
  - vb
  name: MauiThemeProvider()
  nameWithType: MauiThemeProvider.MauiThemeProvider()
  fullName: DrawnUi.Draw.MauiThemeProvider.MauiThemeProvider()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
    startLine: 25
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public MauiThemeProvider()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.MauiThemeProvider.#ctor*
  nameWithType.vb: MauiThemeProvider.New()
  fullName.vb: DrawnUi.Draw.MauiThemeProvider.New()
  name.vb: New()
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.IThemeProvider
  commentId: T:DrawnUi.Draw.IThemeProvider
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IThemeProvider.html
  name: IThemeProvider
  nameWithType: IThemeProvider
  fullName: DrawnUi.Draw.IThemeProvider
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.MauiThemeProvider.CurrentTheme*
  commentId: Overload:DrawnUi.Draw.MauiThemeProvider.CurrentTheme
  href: DrawnUi.Draw.MauiThemeProvider.html#DrawnUi_Draw_MauiThemeProvider_CurrentTheme
  name: CurrentTheme
  nameWithType: MauiThemeProvider.CurrentTheme
  fullName: DrawnUi.Draw.MauiThemeProvider.CurrentTheme
- uid: DrawnUi.Draw.IThemeProvider.CurrentTheme
  commentId: P:DrawnUi.Draw.IThemeProvider.CurrentTheme
  parent: DrawnUi.Draw.IThemeProvider
  href: DrawnUi.Draw.IThemeProvider.html#DrawnUi_Draw_IThemeProvider_CurrentTheme
  name: CurrentTheme
  nameWithType: IThemeProvider.CurrentTheme
  fullName: DrawnUi.Draw.IThemeProvider.CurrentTheme
- uid: Microsoft.Maui.ApplicationModel.AppTheme
  commentId: T:Microsoft.Maui.ApplicationModel.AppTheme
  parent: Microsoft.Maui.ApplicationModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.applicationmodel.apptheme
  name: AppTheme
  nameWithType: AppTheme
  fullName: Microsoft.Maui.ApplicationModel.AppTheme
- uid: Microsoft.Maui.ApplicationModel
  commentId: N:Microsoft.Maui.ApplicationModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.ApplicationModel
  nameWithType: Microsoft.Maui.ApplicationModel
  fullName: Microsoft.Maui.ApplicationModel
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.ApplicationModel
    name: ApplicationModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.applicationmodel
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.ApplicationModel
    name: ApplicationModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.applicationmodel
- uid: DrawnUi.Draw.IThemeProvider.ThemeChanged
  commentId: E:DrawnUi.Draw.IThemeProvider.ThemeChanged
  parent: DrawnUi.Draw.IThemeProvider
  href: DrawnUi.Draw.IThemeProvider.html#DrawnUi_Draw_IThemeProvider_ThemeChanged
  name: ThemeChanged
  nameWithType: IThemeProvider.ThemeChanged
  fullName: DrawnUi.Draw.IThemeProvider.ThemeChanged
- uid: System.EventHandler{DrawnUi.Draw.ThemeChangedEventArgs}
  commentId: T:System.EventHandler{DrawnUi.Draw.ThemeChangedEventArgs}
  parent: System
  definition: System.EventHandler`1
  href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  name: EventHandler<ThemeChangedEventArgs>
  nameWithType: EventHandler<ThemeChangedEventArgs>
  fullName: System.EventHandler<DrawnUi.Draw.ThemeChangedEventArgs>
  nameWithType.vb: EventHandler(Of ThemeChangedEventArgs)
  fullName.vb: System.EventHandler(Of DrawnUi.Draw.ThemeChangedEventArgs)
  name.vb: EventHandler(Of ThemeChangedEventArgs)
  spec.csharp:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: <
  - uid: DrawnUi.Draw.ThemeChangedEventArgs
    name: ThemeChangedEventArgs
    href: DrawnUi.Draw.ThemeChangedEventArgs.html
  - name: '>'
  spec.vb:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.ThemeChangedEventArgs
    name: ThemeChangedEventArgs
    href: DrawnUi.Draw.ThemeChangedEventArgs.html
  - name: )
- uid: System.EventHandler`1
  commentId: T:System.EventHandler`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  name: EventHandler<TEventArgs>
  nameWithType: EventHandler<TEventArgs>
  fullName: System.EventHandler<TEventArgs>
  nameWithType.vb: EventHandler(Of TEventArgs)
  fullName.vb: System.EventHandler(Of TEventArgs)
  name.vb: EventHandler(Of TEventArgs)
  spec.csharp:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: <
  - name: TEventArgs
  - name: '>'
  spec.vb:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: (
  - name: Of
  - name: " "
  - name: TEventArgs
  - name: )
- uid: DrawnUi.Draw.MauiThemeProvider.#ctor*
  commentId: Overload:DrawnUi.Draw.MauiThemeProvider.#ctor
  href: DrawnUi.Draw.MauiThemeProvider.html#DrawnUi_Draw_MauiThemeProvider__ctor
  name: MauiThemeProvider
  nameWithType: MauiThemeProvider.MauiThemeProvider
  fullName: DrawnUi.Draw.MauiThemeProvider.MauiThemeProvider
  nameWithType.vb: MauiThemeProvider.New
  fullName.vb: DrawnUi.Draw.MauiThemeProvider.New
  name.vb: New
