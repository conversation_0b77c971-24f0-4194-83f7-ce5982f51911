# DrawnUI for .NET MAUI

![License](https://img.shields.io/github/license/taublast/DrawnUi.svg)
![NuGet Version](https://img.shields.io/nuget/v/DrawnUi.Maui.svg)
![NuGet Downloads](https://img.shields.io/nuget/dt/AppoMobi.Maui.DrawnUi.svg)

[Source Code](https://github.com/taublast/DrawnUi.Maui) 👈

**A rendering engine for .NET MAUI built on top of SkiaSharp**

**Hardware-accelerated rendering engine** for **iOS**, **MacCatalyst**, **Android**, **Windows** with enhanced WPF-like layout system, gestures and animations, powered by [SkiaSharp](https://github.com/mono/SkiaSharp).

---

## 📦 Quick Install

**Install from nuget:**
```bash
dotnet add package DrawnUi.Maui
```

**Initialize in MauiProgram.cs:**
```csharp
builder.UseDrawnUi();
```

👉 More in the [Getting Started Guide](getting-started.md)!

---

## 📚 Knowledge Base

### Documentation & Guides
- **[Getting Started Guide](getting-started.md)** - Complete installation and setup
- **[Tutorials](tutorials.md)** - Tutorials and example projects
- **[Fluent Extensions](fluent-extensions.md)** - Code-behind UI creation patterns
- **[FAQ](faq.md)** - Frequently asked questions and answers
- **[Controls Documentation](controls/index.md)** - Complete controls reference
- **[Advanced Features](advanced/index.md)** - Performance and platform topics

### Community & Support
- **[GitHub Discussions](https://github.com/taublast/DrawnUi/discussions)** - Community help and discussions
- **[GitHub Issues](https://github.com/taublast/DrawnUi.Maui/issues)** - Report bugs or ask questions

### Additional Resources
- **[Sample Apps](sample-apps.md)** - Apps built with DrawnUI
- **[How DrawnUI was created](https://taublast.github.io/posts/MauiJuly/)** - article by the creator

**Can't find what you're looking for?** → **[Ask in GitHub Discussions](https://github.com/taublast/DrawnUi/discussions)** - The community is here to help!

---

## Features

### 🎨 **Rendering & Graphics**
* **Hardware-accelerated** SkiaSharp rendering with max performance
* **Pixel-perfect controls** with complete visual customization
* **2D and 3D transforms** for advanced visual effects
* **Visual effects** for every control: filters, shaders, shadows, blur
* **Caching system** for optimized re-drawing performance

### 😍 **Development Experience**
* **Design in XAML or code-behind** - choose your preferred approach
* **Fluent C# syntax** for programmatic UI creation
* **Hot Reload compatible** for rapid development iteration
* **Virtual controls** - no native views/handlers, background thread accessible

### 🚀 **Performance & Optimization**
* **Optimized rendering** - only visible elements drawn
* **Template recycling** for efficient memory usage
* **Hardware acceleration** on all supported platforms
* **Smooth animations** targeting maximum FPS

### 👆 **Interaction & Input**
* **Advanced gesture support** - panning, scrolling, zooming, custom gestures
* **Keyboard support** - track any key combination
* **Touch and mouse** input handling
* **Multi-platform input** normalization

### 🧭 **Navigation & Layout**
* **Familiar MAUI Shell** navigation techniques on canvas
* **SkiaShell + SkiaViewSwitcher** for fully drawn app navigation
* **Modals, popups, toasts** and custom overlays
* **Enhanced layout system** with advanced positioning


---


 
