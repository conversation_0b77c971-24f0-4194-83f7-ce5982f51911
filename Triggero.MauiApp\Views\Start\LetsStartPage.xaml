﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Start.LetsStartPage"
             xmlns:app="clr-namespace:Triggero"
             
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>

            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"/>


            <StackLayout
                Spacing="0"
                VerticalOptions="Center">

                <!--<Image
                    WidthRequest="242"
                    HeightRequest="269"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    Source="letsStart.png"/>-->

         

                <Grid
                    x:Name="animatedLayout"
                    WidthRequest="242"
                    HeightRequest="269"
                    VerticalOptions="Start"
                    HorizontalOptions="Center">

                    <Image
                        x:Name="notAnimatedImg"
                        WidthRequest="242"
                        HeightRequest="269"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        Source="animationScooter.png"/>

                </Grid>

                <Label 
                    Margin="0,40,0,0"
                    TextColor="{x:StaticResource greyTextColor}"
                    FontSize="22"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.LetsStartPage.Header}"/>


                <StackLayout
                    Spacing="1"
                    HorizontalOptions="Center"
                    Margin="0,20,0,0">

                    <Label 
                        TextColor="{x:StaticResource greyTextColor}"
                        Opacity="0.5"
                        FontSize="17"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="{x:OnPlatform Android=206,iOS=250}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.LetsStartPage.Text}"/>
                 

                </StackLayout>

                <Grid 
                    HeightRequest="56"
                    ColumnSpacing="15"
                    Margin="20,90,20,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>


                    <Button 
                        Command="{Binding Source={x:Reference this},Path=GoToRegistration}"
                        Grid.Column="0"
                        VerticalOptions="Center"
                        HeightRequest="56"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.LetsStartPage.SignUp}"/>

                    <Button 
                        Command="{Binding Source={x:Reference this},Path=GoToLogin}"
                        Grid.Column="1"
                        VerticalOptions="Center"
                        HeightRequest="56"
                        Style="{x:StaticResource grey_cornered_btn}"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.LetsStartPage.SignIn}"/>
                </Grid>



            </StackLayout>
            
            
            
            

        </Grid>
    </ContentPage.Content>
</ContentPage>