﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Subscriptions.TrialActivatedPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    
    
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
    x:Name="this">
    <ContentPage.Content>

        <Grid
            RowSpacing="0"
            VerticalOptions="FillAndExpand">

            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="2"
                Aspect="Fill"
                HorizontalOptions="Fill"
                Source="trialActivatedBg.png"
                VerticalOptions="Fill" />

            <Frame
                Grid.Row="1"
                Margin="30,0,30,0"
                HeightRequest="400"
                VerticalOptions="Center"
                Padding="0"
                Background="White"
                BackgroundColor="White"
                CornerRadius="16"
                HasShadow="False"
                HorizontalOptions="Fill">
                <Frame.Shadow>
                    <Shadow Brush="#27527A"
                            Offset="2,2"
                            Radius="12"
                            Opacity="0.06" />
                </Frame.Shadow>
                    <Grid>
                        <StackLayout
                            HorizontalOptions="Fill"
                            Spacing="0"
                            VerticalOptions="Center">


                            <!--<Image
                                Margin="0,30,0,0"
                                HeightRequest="120"
                                WidthRequest="107"
                                HorizontalOptions="Center"
                                VerticalOptions="Start"
                                Source="trialGirl.png"/>-->

                            <Grid
                                x:Name="animatedLayout"
                                Margin="0,10,0,0"
                                HeightRequest="200"
                                HorizontalOptions="Center"
                                VerticalOptions="Start"
                                WidthRequest="180">

                                <Image
                                    x:Name="notAnimatedImg"
                                    HeightRequest="200"
                                    HorizontalOptions="Center"
                                    Source="animationGirlWithHearts.png"
                                    VerticalOptions="Start"
                                    WidthRequest="180" />

                            </Grid>




                            <StackLayout
                                Margin="0,12,0,0"
                                HorizontalOptions="Center"
                                Spacing="1">

                                <Label
                                    FontSize="{x:OnPlatform Android=14,
                                                          iOS=17}"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.TrialStarted.Text}"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    VerticalOptions="Start"
                                    WidthRequest="{x:OnPlatform Android=184,
                                                              iOS=224}" />

                            </StackLayout>


                            <Button
                                Margin="33,40,33,0"
                                Command="{Binding Source={x:Reference this}, Path=GoToTutorial}"
                                HeightRequest="56"
                                Style="{x:StaticResource yellow_btn}"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Subscriptions.TrialStarted.GoMain}"
                                VerticalOptions="Start" />


                        </StackLayout>
                    </Grid>
            </Frame>


        </Grid>
    </ContentPage.Content>
</ContentPage>