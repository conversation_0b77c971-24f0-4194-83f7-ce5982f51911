﻿<?xml version="1.0" encoding="UTF-8"?>

<ContentView
    x:Class="Triggero.MauiMobileApp.Views.LibraryView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:library="clr-namespace:Triggero.MauiMobileApp.Views"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="ThisControl"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">

    <ContentView.Content>

        <Grid
            Padding="0"
            BackgroundColor="#FFFFFF"
            HorizontalOptions="Fill"
            RowSpacing="0"
            VerticalOptions="Fill">

            <Grid.RowDefinitions>
                <RowDefinition Height="150" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>


            <Grid.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <LinearGradientBrush.GradientStops>
                        <GradientStop Offset="0.0" Color="#F9FCFF" />
                        <GradientStop Offset="1.0" Color="#CDE9FF" />
                    </LinearGradientBrush.GradientStops>
                </LinearGradientBrush>
            </Grid.Background>

            <Grid Grid.Row="0">

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="2*" />
                    <ColumnDefinition Width="1*" />
                </Grid.ColumnDefinitions>

                <Label
                    Margin="20,0,0,30"
                    Style="{x:StaticResource StyleHeaderText}"
                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Library}"
                    VerticalOptions="End" />

                <Grid Grid.Column="1">

                    <StackLayout
                        Margin="0,0,20,39"
                        HorizontalOptions="End"
                        Orientation="Horizontal"
                        Spacing="24"
                        VerticalOptions="End">
                        <ImageButton
                            BackgroundColor="Transparent"
                            Command="{Binding Source={x:Reference ThisControl}, Path=GoToSearch}"
                            HeightRequest="20"
                            HorizontalOptions="Center"
                            Source="search.png"
                            VerticalOptions="Center"
                            WidthRequest="20" />
                        <ImageButton
                            BackgroundColor="Transparent"
                            Command="{Binding Source={x:Reference ThisControl}, Path=GoToFavorites}"
                            HeightRequest="20"
                            HorizontalOptions="Center"
                            Source="likeSet.png"
                            VerticalOptions="Center"
                            WidthRequest="20" />
                    </StackLayout>

                </Grid>

            </Grid>

            <draw:Canvas
                Tag="Library"
                Grid.Row="1"
                Gestures="Lock"
                RenderingMode="Accelerated"
                HorizontalOptions="Fill"
                VerticalOptions="Fill">
                <draw:SkiaLayout
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">

                    <draw:SkiaShape
                        Padding="0,16,0,0"
                        BackgroundColor="#FFFFFF"
                        CornerRadius="15,15,0,0"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill">

                        <draw:SkiaLayout
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill">

                            <!--  HEADER - 4 BTNS-->
                            <draw:SkiaLayout
                                Margin="20,0,20,0"
                                ColumnDefinitions="1*,1*,1*,1*"
                                HeightRequest="90"
                                HorizontalOptions="Fill"
                                Type="Grid"
                                UseCache="Image">


                                <!--  BTN 0  -->
                                <draw:SkiaShape
                                    CornerRadius="16"
                                    x:Name="exercisesRB"
                                    Grid.Column="0"
                                    draw:AddGestures.CommandTapped="{Binding Source={x:Reference ThisControl}, Path=CommandSelectTab}"
                                    draw:AddGestures.CommandTappedParameter="0"
                                    HeightRequest="90"
                                    HorizontalOptions="Center"
                                    UseCache="Image"
                                    VerticalOptions="Center"
                                    WidthRequest="75">

                                    <draw:SkiaLayout
                                        HorizontalOptions="Fill"
                                        VerticalOptions="Fill">

                                        <!--gradient-->
                                        <draw:SkiaImage
                                            Aspect="Fill"
                                            HorizontalOptions="Fill"
                                            Source="menuExercisesSelected.png"
                                            VerticalOptions="Fill">

                                            <draw:SkiaImage.Triggers>
                                                <DataTrigger
                                                    Binding="{Binding Source={x:Reference ThisControl}, Path=SelectedTab, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter='0'}"
                                                    TargetType="draw:SkiaImage"
                                                    Value="true">
                                                    <Setter Property="Source" Value="menuExercisesSelected.png" />
                                                </DataTrigger>
                                                <DataTrigger
                                                    Binding="{Binding Source={x:Reference ThisControl}, Path=SelectedTab, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter='0'}"
                                                    TargetType="draw:SkiaImage"
                                                    Value="false">
                                                    <Setter Property="Source" Value="menuExercisesUnselected.png" />
                                                </DataTrigger>
                                            </draw:SkiaImage.Triggers>
                                        </draw:SkiaImage>

                                        <draw:SkiaLayout
                                            HorizontalOptions="Fill"
                                            RowDefinitions="60, 30"
                                            Type="Grid"

                                            VerticalOptions="Fill">

                                            <!--icon-->
                                            <draw:SkiaImage
                                                Aspect="AspectFit"
                                                HeightRequest="46"
                                                HorizontalOptions="Center"
                                                Source="menuExercises.png"
                                                VerticalOptions="Center"
                                                WidthRequest="60" />

                                            <draw:SkiaLabel
                                                Grid.Row="1"
                                                FontSize="10"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Exercises}"
                                                TextColor="{x:StaticResource greyTextColor}" />

                                        </draw:SkiaLayout>

                                    </draw:SkiaLayout>

                                </draw:SkiaShape>

                                <!--  BTN 1  -->
                                <draw:SkiaShape
                                    x:Name="practicesRB"
                                    Grid.Column="1"
                                    draw:AddGestures.CommandTapped="{Binding Source={x:Reference ThisControl}, Path=CommandSelectTab}"
                                    draw:AddGestures.CommandTappedParameter="1"
                                    HeightRequest="90"
                                    HorizontalOptions="Center"
                                    UseCache="Image"
                                    VerticalOptions="Center"
                                    WidthRequest="75">

                                    <draw:SkiaLayout
                                        HorizontalOptions="Fill"
                                        VerticalOptions="Fill">

                                        <draw:SkiaImage
                                            Aspect="Fill"
                                            HorizontalOptions="Fill"
                                            Source="menuPracticesUnselected.png"
                                            VerticalOptions="Fill">
                                            <draw:SkiaImage.Triggers>
                                                <DataTrigger
                                                    Binding="{Binding Source={x:Reference ThisControl}, Path=SelectedTab, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter='1'}"
                                                    TargetType="draw:SkiaImage"
                                                    Value="true">
                                                    <Setter Property="Source" Value="menuPracticesSelected.png" />
                                                </DataTrigger>
                                                <DataTrigger
                                                    Binding="{Binding Source={x:Reference ThisControl}, Path=SelectedTab, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter='1'}"
                                                    TargetType="draw:SkiaImage"
                                                    Value="false">
                                                    <Setter Property="Source" Value="menuPracticesUnselected.png" />
                                                </DataTrigger>
                                            </draw:SkiaImage.Triggers>
                                        </draw:SkiaImage>

                                        <draw:SkiaLayout

                                            HorizontalOptions="Fill"
                                            RowDefinitions="60, 30"
                                            Type="Grid"
                                            VerticalOptions="Fill">

                                            <draw:SkiaImage
                                                Aspect="AspectFitFill"
                                                HeightRequest="46"
                                                HorizontalOptions="Center"
                                                Source="menuPractices.png"
                                                VerticalOptions="Center"
                                                WidthRequest="60" />

                                            <draw:SkiaLabel
                                                Grid.Row="1"
                                                FontSize="10"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Practices}"
                                                TextColor="{x:StaticResource greyTextColor}" />

                                        </draw:SkiaLayout>

                                    </draw:SkiaLayout>


                                </draw:SkiaShape>

                                <!--  BTN 2  -->
                                <draw:SkiaShape
                                    x:Name="topicsRB"
                                    Grid.Column="2"
                                    draw:AddGestures.CommandTapped="{Binding Source={x:Reference ThisControl}, Path=CommandSelectTab}"
                                    draw:AddGestures.CommandTappedParameter="2"
                                    HeightRequest="90"
                                    HorizontalOptions="Center"
                                    UseCache="Image"
                                    VerticalOptions="Center"
                                    WidthRequest="75">

                                    <draw:SkiaLayout
                                        HorizontalOptions="Fill"
                                        VerticalOptions="Fill">

                                        <draw:SkiaImage

                                            Aspect="Fill"
                                            HorizontalOptions="Fill"
                                            Source="menuTopicsUnselected.png"
                                            VerticalOptions="Fill">
                                            <draw:SkiaImage.Triggers>
                                                <DataTrigger
                                                    Binding="{Binding Source={x:Reference ThisControl}, Path=SelectedTab, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter='2'}"
                                                    TargetType="draw:SkiaImage"
                                                    Value="true">
                                                    <Setter Property="Source" Value="menuTopicsSelected.png" />
                                                </DataTrigger>
                                                <DataTrigger
                                                    Binding="{Binding Source={x:Reference ThisControl}, Path=SelectedTab, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter='2'}"
                                                    TargetType="draw:SkiaImage"
                                                    Value="false">
                                                    <Setter Property="Source" Value="menuTopicsUnselected.png" />
                                                </DataTrigger>
                                            </draw:SkiaImage.Triggers>
                                        </draw:SkiaImage>

                                        <draw:SkiaLayout
                                            HorizontalOptions="Fill"
                                            RowDefinitions="60, 30"
                                            Type="Grid"

                                            VerticalOptions="Fill">

                                            <draw:SkiaImage
                                                Aspect="AspectFitFill"
                                                HeightRequest="46"
                                                HorizontalOptions="Center"
                                                Source="menuTopics.png"
                                                VerticalOptions="Center"
                                                WidthRequest="60" />

                                            <draw:SkiaLabel
                                                Grid.Row="1"
                                                FontSize="10"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Topics}"
                                                TextColor="{x:StaticResource greyTextColor}" />

                                        </draw:SkiaLayout>

                                    </draw:SkiaLayout>

                                </draw:SkiaShape>

                                <!--  BTN 3 BREATH-->
                                <draw:SkiaShape
                                    x:Name="breathRB"
                                    Grid.Column="3"
                                    draw:AddGestures.CommandTapped="{Binding Source={x:Reference ThisControl}, Path=CommandSelectTab}"
                                    draw:AddGestures.CommandTappedParameter="3"
                                    HeightRequest="90"
                                    HorizontalOptions="Center"
                                    UseCache="Image"
                                    VerticalOptions="Center"
                                    WidthRequest="75">

                                    <draw:SkiaLayout
                                        HorizontalOptions="Fill"
                                        VerticalOptions="Fill">

                                        <draw:SkiaImage
                                            Aspect="Fill"
                                            HorizontalOptions="Fill"
                                            Source="menuBreathUnselected.png"
                                            VerticalOptions="Fill">

                                            <draw:SkiaImage.Triggers>
                                                <DataTrigger
                                                    Binding="{Binding Source={x:Reference ThisControl}, Path=SelectedTab, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter='3'}"
                                                    TargetType="draw:SkiaImage"
                                                    Value="true">
                                                    <Setter Property="Source" Value="menuBreathSelected.png" />
                                                </DataTrigger>
                                                <DataTrigger
                                                    Binding="{Binding Source={x:Reference ThisControl}, Path=SelectedTab, Converter={x:StaticResource CompareIntegersConverter}, ConverterParameter='3'}"
                                                    TargetType="draw:SkiaImage"
                                                    Value="false">
                                                    <Setter Property="Source" Value="menuBreathUnselected.png" />
                                                </DataTrigger>
                                            </draw:SkiaImage.Triggers>

                                        </draw:SkiaImage>

                                        <draw:SkiaLayout
                                            HorizontalOptions="Fill"
                                            RowDefinitions="60, 30"
                                            Type="Grid"

                                            VerticalOptions="Fill">

                                            <draw:SkiaImage
                                                Aspect="AspectFitFill"
                                                HeightRequest="46"
                                                HorizontalOptions="Center"
                                                Source="menuBreath.png"
                                                VerticalOptions="Center"
                                                WidthRequest="60" />

                                            <draw:SkiaLabel
                                                Grid.Row="1"
                                                FontSize="10"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Breath}"
                                                TextColor="{x:StaticResource greyTextColor}" />

                                        </draw:SkiaLayout>

                                    </draw:SkiaLayout>


                                </draw:SkiaShape>

                            </draw:SkiaLayout>

                            <!--  CATEGORIES  -->
                            <draw:SkiaLayout
                                x:Name="TabsContainer"
                                Margin="16,108,16,0"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <!--  CATEGORIES TABS  created in code behind for some reason  -->

                            </draw:SkiaLayout>

                        </draw:SkiaLayout>

                    </draw:SkiaShape>

                    <!--  FPS  -->
                    <draw:SkiaLabelFps
                        Margin="0,0,4,84"
                        BackgroundColor="DarkRed"
                        ForceRefresh="False"
                        HorizontalOptions="End"
                        IsVisible="{x:Static mobile:Globals.ShowFPS}"
                        Rotation="-45"
                        TextColor="White"
                        VerticalOptions="End" />

                </draw:SkiaLayout>
            </draw:Canvas>


        </Grid>
    </ContentView.Content>
</ContentView>