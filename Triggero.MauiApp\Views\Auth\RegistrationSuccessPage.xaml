﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             
             x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.RegistrationSuccessPage"
             xmlns:app="clr-namespace:Triggero"
             
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
             x:Name="this">
    <ContentPage.Content>
        <Grid>

            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"/>

            <Frame
                Margin="30,0,30,0"
                HeightRequest="400"
                VerticalOptions="Center"
                CornerRadius="16"
                BackgroundColor="White"
                Background="White"
                HorizontalOptions="Fill"
                HasShadow="False"
                Padding="0">
                <Frame.Shadow>
                    <Shadow Brush="#27527A"
                            Offset="2,2"
                            Radius="12"
                            Opacity="0.06" />
                </Frame.Shadow>
                    <Grid>
                        <StackLayout
                            Spacing="0">
                            
                            <!--<Image 
                                Margin="0,30,0,0"
                                HeightRequest="100"
                                WidthRequest="100"
                                HorizontalOptions="Center"
                                VerticalOptions="Start"
                                Source="accountSuccessfullyCreated.png"/>-->

                           

                            <Grid
                                x:Name="animatedLayout"
                                Margin="0,30,0,0"
                                HeightRequest="100"
                                WidthRequest="100"
                                HorizontalOptions="Center"
                                VerticalOptions="Start">
                                
                                <Image
                                    x:Name="notAnimatedImg"
                                    HeightRequest="100"
                                    WidthRequest="100"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Start"
                                    Source="animationSuccess.png"/>
                                
                            </Grid>

                            <Label 
                                Margin="0,30,0,0"
                                TextColor="{x:StaticResource greyTextColor}"
                                FontSize="17"
                                HorizontalOptions="Center"
                                FontAttributes="Bold"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Auth.AccountRegistered.Successfully}"/>

                            <StackLayout
                                Spacing="1"
                                HorizontalOptions="Center"
                                Margin="0,12,0,0">

                                <Label 
                                    TextColor="{x:StaticResource greyTextColor}"
                                    Opacity="0.5"
                                    FontSize="17"
                                    VerticalOptions="Start"
                                    HorizontalOptions="Center"
                                    HorizontalTextAlignment="Center"
                                    WidthRequest="{x:OnPlatform Android=249,iOS=275}"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Auth.AccountRegistered.Text}"/>
                            </StackLayout>

                            <ImageButton 
                                Command="{Binding Source={x:Reference this},Path=GoToTutorial}"
                                Margin="0,40,0,0"
                                WidthRequest="92"
                                HeightRequest="64"
                                HorizontalOptions="Center"
                                VerticalOptions="Start"
                                CornerRadius="0"
                                BackgroundColor="Transparent"
                                Source="yellowContinueBtn.png"/>

                        </StackLayout>
                    </Grid>
            </Frame>
        </Grid>
    </ContentPage.Content>
</ContentPage>