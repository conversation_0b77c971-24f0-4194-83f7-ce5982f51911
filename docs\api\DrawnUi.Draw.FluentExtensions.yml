### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.FluentExtensions
  commentId: T:DrawnUi.Draw.FluentExtensions
  id: FluentExtensions
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.FluentExtensions.Adapt``1(``0,System.Action{``0})
  - DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  - DrawnUi.Draw.FluentExtensions.AssignParent``1(``0,DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.FluentExtensions.Assign``1(``0,``0@)
  - DrawnUi.Draw.FluentExtensions.BindProperty``1(``0,Microsoft.Maui.Controls.BindableProperty,System.ComponentModel.INotifyPropertyChanged,System.String,Microsoft.Maui.Controls.BindingMode)
  - DrawnUi.Draw.FluentExtensions.BindProperty``1(``0,Microsoft.Maui.Controls.BindableProperty,System.String,Microsoft.Maui.Controls.BindingMode)
  - DrawnUi.Draw.FluentExtensions.BindProperty``2(``0,Microsoft.Maui.Controls.BindableProperty,System.String,Microsoft.Maui.Controls.IValueConverter,System.Object,Microsoft.Maui.Controls.BindingMode)
  - DrawnUi.Draw.FluentExtensions.CenterX``1(``0)
  - DrawnUi.Draw.FluentExtensions.CenterY``1(``0)
  - DrawnUi.Draw.FluentExtensions.Center``1(``0)
  - DrawnUi.Draw.FluentExtensions.EndX``1(``0)
  - DrawnUi.Draw.FluentExtensions.EndY``1(``0)
  - DrawnUi.Draw.FluentExtensions.FillX``1(``0)
  - DrawnUi.Draw.FluentExtensions.FillY``1(``0)
  - DrawnUi.Draw.FluentExtensions.Fill``1(``0)
  - DrawnUi.Draw.FluentExtensions.Initialize``1(``0,System.Action{``0})
  - DrawnUi.Draw.FluentExtensions.ObserveBindingContextOn``3(``0,``1,System.Action{``0,``1,``2,System.String},System.Boolean)
  - DrawnUi.Draw.FluentExtensions.ObserveBindingContext``2(``0,System.Action{``0,``1,System.String},System.Boolean)
  - DrawnUi.Draw.FluentExtensions.ObserveOn``3(``0,``1,System.Func{``2},System.String,System.Action{``0,System.String},System.String[])
  - DrawnUi.Draw.FluentExtensions.ObservePropertiesOn``3(``0,``1,System.Func{``2},System.String,System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  - DrawnUi.Draw.FluentExtensions.ObserveProperties``2(``0,System.Func{``1},System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  - DrawnUi.Draw.FluentExtensions.ObserveProperties``2(``0,``1,System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  - DrawnUi.Draw.FluentExtensions.ObservePropertyOn``3(``0,``1,System.Func{``2},System.String,System.String,System.Action{``0})
  - DrawnUi.Draw.FluentExtensions.ObserveProperty``2(``0,System.Func{``1},System.String,System.Action{``0})
  - DrawnUi.Draw.FluentExtensions.ObserveProperty``2(``0,``1,System.String,System.Action{``0})
  - DrawnUi.Draw.FluentExtensions.ObserveSelf``1(``0,System.Action{``0,System.String})
  - DrawnUi.Draw.FluentExtensions.Observe``2(``0,System.Func{``1},System.Action{``0,System.String},System.String[])
  - DrawnUi.Draw.FluentExtensions.Observe``2(``0,System.Func{``1},System.Action{``0},System.String[])
  - DrawnUi.Draw.FluentExtensions.Observe``2(``0,``1,System.Action{``0,System.String},System.String[])
  - DrawnUi.Draw.FluentExtensions.Observe``2(``0,``1,System.Action{``0},System.String[])
  - DrawnUi.Draw.FluentExtensions.OnBindingContextSet``1(``0,System.Action{``0,System.Object},System.String[])
  - DrawnUi.Draw.FluentExtensions.OnLongPressing``1(``0,System.Action{``0})
  - DrawnUi.Draw.FluentExtensions.OnPaint``1(``0,System.Action{``0,DrawnUi.Draw.DrawingContext})
  - DrawnUi.Draw.FluentExtensions.OnTapped``1(``0,System.Action{``0})
  - DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEditor,System.Action{DrawnUi.Controls.SkiaMauiEditor,System.String})
  - DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEntry,System.Action{DrawnUi.Controls.SkiaMauiEntry,System.String})
  - DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Draw.SkiaLabel,System.Action{DrawnUi.Draw.SkiaLabel,System.String})
  - DrawnUi.Draw.FluentExtensions.OnToggled``1(``0,System.Action{``0,System.Boolean})
  - DrawnUi.Draw.FluentExtensions.SetGrid``1(``0,System.Int32,System.Int32)
  - DrawnUi.Draw.FluentExtensions.SetGrid``1(``0,System.Int32,System.Int32,System.Int32,System.Int32)
  - DrawnUi.Draw.FluentExtensions.Shape``1(``0,DrawnUi.Draw.ShapeType)
  - DrawnUi.Draw.FluentExtensions.StartX``1(``0)
  - DrawnUi.Draw.FluentExtensions.StartY``1(``0)
  - DrawnUi.Draw.FluentExtensions.WithAspect``1(``0,DrawnUi.Draw.TransformAspect)
  - DrawnUi.Draw.FluentExtensions.WithBackgroundColor``1(``0,Microsoft.Maui.Graphics.Color)
  - DrawnUi.Draw.FluentExtensions.WithBottom(Microsoft.Maui.Thickness,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithCache``1(``0,DrawnUi.Draw.SkiaCacheType)
  - DrawnUi.Draw.FluentExtensions.WithChildren``1(``0,DrawnUi.Draw.SkiaControl[])
  - DrawnUi.Draw.FluentExtensions.WithColumnDefinitions(DrawnUi.Draw.SkiaLayout,System.String)
  - DrawnUi.Draw.FluentExtensions.WithColumnSpacing``1(``0,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithColumnSpan``1(``0,System.Int32)
  - DrawnUi.Draw.FluentExtensions.WithColumn``1(``0,System.Int32)
  - DrawnUi.Draw.FluentExtensions.WithContent``1(``0,DrawnUi.Draw.SkiaControl)
  - DrawnUi.Draw.FluentExtensions.WithEnabled``1(``0,System.Boolean)
  - DrawnUi.Draw.FluentExtensions.WithFontSize``1(``0,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithGestures``1(``0,System.Func{``0,DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo,DrawnUi.Draw.ISkiaGestureListener})
  - DrawnUi.Draw.FluentExtensions.WithHeight``1(``0,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithHorizontalOptions``1(``0,Microsoft.Maui.Controls.LayoutOptions)
  - DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment``1(``0,DrawnUi.Draw.DrawTextAlignment)
  - DrawnUi.Draw.FluentExtensions.WithItemsSource``1(``0,System.Collections.IList)
  - DrawnUi.Draw.FluentExtensions.WithLeft(Microsoft.Maui.Thickness,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,Microsoft.Maui.Thickness)
  - DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,System.Double,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,System.Double,System.Double,System.Double,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithOpacity``1(``0,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double,System.Double,System.Double,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithParent``1(``0,DrawnUi.Draw.IDrawnBase)
  - DrawnUi.Draw.FluentExtensions.WithPoints(DrawnUi.Draw.SkiaShape,System.String)
  - DrawnUi.Draw.FluentExtensions.WithRight(Microsoft.Maui.Thickness,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithRotation``1(``0,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithRowDefinitions(DrawnUi.Draw.SkiaLayout,System.String)
  - DrawnUi.Draw.FluentExtensions.WithRowSpacing``1(``0,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithRowSpan``1(``0,System.Int32)
  - DrawnUi.Draw.FluentExtensions.WithRow``1(``0,System.Int32)
  - DrawnUi.Draw.FluentExtensions.WithScale``1(``0,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithScale``1(``0,System.Double,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithShapeType``1(``0,DrawnUi.Draw.ShapeType)
  - DrawnUi.Draw.FluentExtensions.WithSpacing``1(``0,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithTag``1(``0,System.String)
  - DrawnUi.Draw.FluentExtensions.WithTextColor``1(``0,Microsoft.Maui.Graphics.Color)
  - DrawnUi.Draw.FluentExtensions.WithText``1(``0,System.String)
  - DrawnUi.Draw.FluentExtensions.WithTop(Microsoft.Maui.Thickness,System.Double)
  - DrawnUi.Draw.FluentExtensions.WithType``1(``0,DrawnUi.Draw.LayoutType)
  - DrawnUi.Draw.FluentExtensions.WithVerticalOptions``1(``0,Microsoft.Maui.Controls.LayoutOptions)
  - DrawnUi.Draw.FluentExtensions.WithVisibility``1(``0,System.Boolean)
  - DrawnUi.Draw.FluentExtensions.WithWidth``1(``0,System.Double)
  langs:
  - csharp
  - vb
  name: FluentExtensions
  nameWithType: FluentExtensions
  fullName: DrawnUi.Draw.FluentExtensions
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FluentExtensions
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Provides extension methods for fluent API design pattern with DrawnUI controls
  example: []
  syntax:
    content: public static class FluentExtensions
    content.vb: Public Module FluentExtensions
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  commentId: M:DrawnUi.Draw.FluentExtensions.AssignNative``1(``0,``0@)
  id: AssignNative``1(``0,``0@)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: AssignNative<T>(T, out T)
  nameWithType: FluentExtensions.AssignNative<T>(T, out T)
  fullName: DrawnUi.Draw.FluentExtensions.AssignNative<T>(T, out T)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AssignNative
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public static T AssignNative<T>(this T control, out T variable) where T : VisualElement'
    parameters:
    - id: control
      type: '{T}'
    - id: variable
      type: '{T}'
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function AssignNative(Of T As VisualElement)(control As T, variable As T) As T
  overload: DrawnUi.Draw.FluentExtensions.AssignNative*
  nameWithType.vb: FluentExtensions.AssignNative(Of T)(T, T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.AssignNative(Of T)(T, T)
  name.vb: AssignNative(Of T)(T, T)
- uid: DrawnUi.Draw.FluentExtensions.WithRow``1(``0,System.Int32)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithRow``1(``0,System.Int32)
  id: WithRow``1(``0,System.Int32)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithRow<T>(T, int)
  nameWithType: FluentExtensions.WithRow<T>(T, int)
  fullName: DrawnUi.Draw.FluentExtensions.WithRow<T>(T, int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithRow
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 27
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Grid.Row attached property for the control
  example: []
  syntax:
    content: 'public static T WithRow<T>(this T view, int row) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set the row for
    - id: row
      type: System.Int32
      description: The row index
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithRow(Of T As SkiaControl)(view As T, row As Integer) As T
  overload: DrawnUi.Draw.FluentExtensions.WithRow*
  nameWithType.vb: FluentExtensions.WithRow(Of T)(T, Integer)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithRow(Of T)(T, Integer)
  name.vb: WithRow(Of T)(T, Integer)
- uid: DrawnUi.Draw.FluentExtensions.WithColumn``1(``0,System.Int32)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithColumn``1(``0,System.Int32)
  id: WithColumn``1(``0,System.Int32)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithColumn<T>(T, int)
  nameWithType: FluentExtensions.WithColumn<T>(T, int)
  fullName: DrawnUi.Draw.FluentExtensions.WithColumn<T>(T, int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithColumn
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 40
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Grid.Column attached property for the control
  example: []
  syntax:
    content: 'public static T WithColumn<T>(this T view, int column) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set the column for
    - id: column
      type: System.Int32
      description: The column index
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithColumn(Of T As SkiaControl)(view As T, column As Integer) As T
  overload: DrawnUi.Draw.FluentExtensions.WithColumn*
  nameWithType.vb: FluentExtensions.WithColumn(Of T)(T, Integer)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithColumn(Of T)(T, Integer)
  name.vb: WithColumn(Of T)(T, Integer)
- uid: DrawnUi.Draw.FluentExtensions.WithRowSpan``1(``0,System.Int32)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithRowSpan``1(``0,System.Int32)
  id: WithRowSpan``1(``0,System.Int32)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithRowSpan<T>(T, int)
  nameWithType: FluentExtensions.WithRowSpan<T>(T, int)
  fullName: DrawnUi.Draw.FluentExtensions.WithRowSpan<T>(T, int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithRowSpan
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 53
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Grid.RowSpan attached property for the control
  example: []
  syntax:
    content: 'public static T WithRowSpan<T>(this T view, int rowSpan) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set the row span for
    - id: rowSpan
      type: System.Int32
      description: The number of rows to span
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithRowSpan(Of T As SkiaControl)(view As T, rowSpan As Integer) As T
  overload: DrawnUi.Draw.FluentExtensions.WithRowSpan*
  nameWithType.vb: FluentExtensions.WithRowSpan(Of T)(T, Integer)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithRowSpan(Of T)(T, Integer)
  name.vb: WithRowSpan(Of T)(T, Integer)
- uid: DrawnUi.Draw.FluentExtensions.WithColumnSpan``1(``0,System.Int32)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithColumnSpan``1(``0,System.Int32)
  id: WithColumnSpan``1(``0,System.Int32)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithColumnSpan<T>(T, int)
  nameWithType: FluentExtensions.WithColumnSpan<T>(T, int)
  fullName: DrawnUi.Draw.FluentExtensions.WithColumnSpan<T>(T, int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithColumnSpan
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 66
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Grid.ColumnSpan attached property for the control
  example: []
  syntax:
    content: 'public static T WithColumnSpan<T>(this T view, int columnSpan) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set the column span for
    - id: columnSpan
      type: System.Int32
      description: The number of columns to span
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithColumnSpan(Of T As SkiaControl)(view As T, columnSpan As Integer) As T
  overload: DrawnUi.Draw.FluentExtensions.WithColumnSpan*
  nameWithType.vb: FluentExtensions.WithColumnSpan(Of T)(T, Integer)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithColumnSpan(Of T)(T, Integer)
  name.vb: WithColumnSpan(Of T)(T, Integer)
- uid: DrawnUi.Draw.FluentExtensions.WithColumnDefinitions(DrawnUi.Draw.SkiaLayout,System.String)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithColumnDefinitions(DrawnUi.Draw.SkiaLayout,System.String)
  id: WithColumnDefinitions(DrawnUi.Draw.SkiaLayout,System.String)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithColumnDefinitions(SkiaLayout, string)
  nameWithType: FluentExtensions.WithColumnDefinitions(SkiaLayout, string)
  fullName: DrawnUi.Draw.FluentExtensions.WithColumnDefinitions(DrawnUi.Draw.SkiaLayout, string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithColumnDefinitions
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 79
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Parses a string representation of column definitions and sets them on the grid
  example: []
  syntax:
    content: public static SkiaLayout WithColumnDefinitions(this SkiaLayout grid, string columnDefinitions)
    parameters:
    - id: grid
      type: DrawnUi.Draw.SkiaLayout
      description: The grid to set column definitions for
    - id: columnDefinitions
      type: System.String
      description: String in format like "Auto,*,2*,100"
    return:
      type: DrawnUi.Draw.SkiaLayout
      description: The grid for chaining
    content.vb: Public Shared Function WithColumnDefinitions(grid As SkiaLayout, columnDefinitions As String) As SkiaLayout
  overload: DrawnUi.Draw.FluentExtensions.WithColumnDefinitions*
  exceptions:
  - type: System.InvalidOperationException
    commentId: T:System.InvalidOperationException
    description: Thrown if conversion fails
  nameWithType.vb: FluentExtensions.WithColumnDefinitions(SkiaLayout, String)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithColumnDefinitions(DrawnUi.Draw.SkiaLayout, String)
  name.vb: WithColumnDefinitions(SkiaLayout, String)
- uid: DrawnUi.Draw.FluentExtensions.WithPoints(DrawnUi.Draw.SkiaShape,System.String)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithPoints(DrawnUi.Draw.SkiaShape,System.String)
  id: WithPoints(DrawnUi.Draw.SkiaShape,System.String)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithPoints(SkiaShape, string)
  nameWithType: FluentExtensions.WithPoints(SkiaShape, string)
  fullName: DrawnUi.Draw.FluentExtensions.WithPoints(DrawnUi.Draw.SkiaShape, string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithPoints
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 101
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Parses a string representation of points and sets them on the shape
  example: []
  syntax:
    content: public static SkiaShape WithPoints(this SkiaShape shape, string columnDefinitions)
    parameters:
    - id: shape
      type: DrawnUi.Draw.SkiaShape
      description: ''
    - id: columnDefinitions
      type: System.String
      description: ''
    return:
      type: DrawnUi.Draw.SkiaShape
      description: ''
    content.vb: Public Shared Function WithPoints(shape As SkiaShape, columnDefinitions As String) As SkiaShape
  overload: DrawnUi.Draw.FluentExtensions.WithPoints*
  exceptions:
  - type: System.InvalidOperationException
    commentId: T:System.InvalidOperationException
    description: ''
  nameWithType.vb: FluentExtensions.WithPoints(SkiaShape, String)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithPoints(DrawnUi.Draw.SkiaShape, String)
  name.vb: WithPoints(SkiaShape, String)
- uid: DrawnUi.Draw.FluentExtensions.WithRowDefinitions(DrawnUi.Draw.SkiaLayout,System.String)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithRowDefinitions(DrawnUi.Draw.SkiaLayout,System.String)
  id: WithRowDefinitions(DrawnUi.Draw.SkiaLayout,System.String)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithRowDefinitions(SkiaLayout, string)
  nameWithType: FluentExtensions.WithRowDefinitions(SkiaLayout, string)
  fullName: DrawnUi.Draw.FluentExtensions.WithRowDefinitions(DrawnUi.Draw.SkiaLayout, string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithRowDefinitions
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 123
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Parses a string representation of row definitions and sets them on the grid
  example: []
  syntax:
    content: public static SkiaLayout WithRowDefinitions(this SkiaLayout grid, string definitions)
    parameters:
    - id: grid
      type: DrawnUi.Draw.SkiaLayout
      description: The grid to set row definitions for
    - id: definitions
      type: System.String
      description: String in format like "Auto,*,2*,100"
    return:
      type: DrawnUi.Draw.SkiaLayout
      description: The grid for chaining
    content.vb: Public Shared Function WithRowDefinitions(grid As SkiaLayout, definitions As String) As SkiaLayout
  overload: DrawnUi.Draw.FluentExtensions.WithRowDefinitions*
  exceptions:
  - type: System.InvalidOperationException
    commentId: T:System.InvalidOperationException
    description: Thrown if conversion fails
  nameWithType.vb: FluentExtensions.WithRowDefinitions(SkiaLayout, String)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithRowDefinitions(DrawnUi.Draw.SkiaLayout, String)
  name.vb: WithRowDefinitions(SkiaLayout, String)
- uid: DrawnUi.Draw.FluentExtensions.SetGrid``1(``0,System.Int32,System.Int32)
  commentId: M:DrawnUi.Draw.FluentExtensions.SetGrid``1(``0,System.Int32,System.Int32)
  id: SetGrid``1(``0,System.Int32,System.Int32)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: SetGrid<T>(T, int, int)
  nameWithType: FluentExtensions.SetGrid<T>(T, int, int)
  fullName: DrawnUi.Draw.FluentExtensions.SetGrid<T>(T, int, int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetGrid
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 146
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Grid row and column in a single call
  example: []
  syntax:
    content: 'public static T SetGrid<T>(this T view, int column, int row) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set the grid position for
    - id: column
      type: System.Int32
      description: The column index
    - id: row
      type: System.Int32
      description: The row index
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function SetGrid(Of T As SkiaControl)(view As T, column As Integer, row As Integer) As T
  overload: DrawnUi.Draw.FluentExtensions.SetGrid*
  nameWithType.vb: FluentExtensions.SetGrid(Of T)(T, Integer, Integer)
  fullName.vb: DrawnUi.Draw.FluentExtensions.SetGrid(Of T)(T, Integer, Integer)
  name.vb: SetGrid(Of T)(T, Integer, Integer)
- uid: DrawnUi.Draw.FluentExtensions.SetGrid``1(``0,System.Int32,System.Int32,System.Int32,System.Int32)
  commentId: M:DrawnUi.Draw.FluentExtensions.SetGrid``1(``0,System.Int32,System.Int32,System.Int32,System.Int32)
  id: SetGrid``1(``0,System.Int32,System.Int32,System.Int32,System.Int32)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: SetGrid<T>(T, int, int, int, int)
  nameWithType: FluentExtensions.SetGrid<T>(T, int, int, int, int)
  fullName: DrawnUi.Draw.FluentExtensions.SetGrid<T>(T, int, int, int, int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetGrid
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 163
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Grid row, column, rowspan and columnspan in a single call
  example: []
  syntax:
    content: 'public static T SetGrid<T>(this T view, int column, int row, int columnSpan, int rowSpan) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set the grid position for
    - id: column
      type: System.Int32
      description: The column index
    - id: row
      type: System.Int32
      description: The row index
    - id: columnSpan
      type: System.Int32
      description: The number of columns to span
    - id: rowSpan
      type: System.Int32
      description: The number of rows to span
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function SetGrid(Of T As SkiaControl)(view As T, column As Integer, row As Integer, columnSpan As Integer, rowSpan As Integer) As T
  overload: DrawnUi.Draw.FluentExtensions.SetGrid*
  nameWithType.vb: FluentExtensions.SetGrid(Of T)(T, Integer, Integer, Integer, Integer)
  fullName.vb: DrawnUi.Draw.FluentExtensions.SetGrid(Of T)(T, Integer, Integer, Integer, Integer)
  name.vb: SetGrid(Of T)(T, Integer, Integer, Integer, Integer)
- uid: DrawnUi.Draw.FluentExtensions.BindProperty``1(``0,Microsoft.Maui.Controls.BindableProperty,System.String,Microsoft.Maui.Controls.BindingMode)
  commentId: M:DrawnUi.Draw.FluentExtensions.BindProperty``1(``0,Microsoft.Maui.Controls.BindableProperty,System.String,Microsoft.Maui.Controls.BindingMode)
  id: BindProperty``1(``0,Microsoft.Maui.Controls.BindableProperty,System.String,Microsoft.Maui.Controls.BindingMode)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: BindProperty<T>(T, BindableProperty, string, BindingMode)
  nameWithType: FluentExtensions.BindProperty<T>(T, BindableProperty, string, BindingMode)
  fullName: DrawnUi.Draw.FluentExtensions.BindProperty<T>(T, Microsoft.Maui.Controls.BindableProperty, string, Microsoft.Maui.Controls.BindingMode)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BindProperty
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 188
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets up a simple non-compiled binding for a property
  example: []
  syntax:
    content: 'public static T BindProperty<T>(this T view, BindableProperty property, string path, BindingMode mode = BindingMode.Default) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set the binding for
    - id: property
      type: Microsoft.Maui.Controls.BindableProperty
    - id: path
      type: System.String
      description: The binding path
    - id: mode
      type: Microsoft.Maui.Controls.BindingMode
      description: The binding mode
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function BindProperty(Of T As SkiaControl)(view As T, [property] As BindableProperty, path As String, mode As BindingMode = BindingMode.Default) As T
  overload: DrawnUi.Draw.FluentExtensions.BindProperty*
  nameWithType.vb: FluentExtensions.BindProperty(Of T)(T, BindableProperty, String, BindingMode)
  fullName.vb: DrawnUi.Draw.FluentExtensions.BindProperty(Of T)(T, Microsoft.Maui.Controls.BindableProperty, String, Microsoft.Maui.Controls.BindingMode)
  name.vb: BindProperty(Of T)(T, BindableProperty, String, BindingMode)
- uid: DrawnUi.Draw.FluentExtensions.BindProperty``1(``0,Microsoft.Maui.Controls.BindableProperty,System.ComponentModel.INotifyPropertyChanged,System.String,Microsoft.Maui.Controls.BindingMode)
  commentId: M:DrawnUi.Draw.FluentExtensions.BindProperty``1(``0,Microsoft.Maui.Controls.BindableProperty,System.ComponentModel.INotifyPropertyChanged,System.String,Microsoft.Maui.Controls.BindingMode)
  id: BindProperty``1(``0,Microsoft.Maui.Controls.BindableProperty,System.ComponentModel.INotifyPropertyChanged,System.String,Microsoft.Maui.Controls.BindingMode)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: BindProperty<T>(T, BindableProperty, INotifyPropertyChanged, string, BindingMode)
  nameWithType: FluentExtensions.BindProperty<T>(T, BindableProperty, INotifyPropertyChanged, string, BindingMode)
  fullName: DrawnUi.Draw.FluentExtensions.BindProperty<T>(T, Microsoft.Maui.Controls.BindableProperty, System.ComponentModel.INotifyPropertyChanged, string, Microsoft.Maui.Controls.BindingMode)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BindProperty
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 205
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Binds a property of a view to a source property using a specified path and binding mode.
  example: []
  syntax:
    content: 'public static T BindProperty<T>(this T view, BindableProperty targetProperty, INotifyPropertyChanged source, string path, BindingMode mode = BindingMode.Default) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The UI element that will have its property bound to a source property.
    - id: targetProperty
      type: Microsoft.Maui.Controls.BindableProperty
      description: The property of the view that will receive the binding.
    - id: source
      type: System.ComponentModel.INotifyPropertyChanged
      description: The object that implements property change notifications and serves as the data source.
    - id: path
      type: System.String
      description: The path to the property on the source object that will be bound to the target property.
    - id: mode
      type: Microsoft.Maui.Controls.BindingMode
      description: Specifies the binding mode, determining how the source and target properties interact.
    typeParameters:
    - id: T
      description: Represents a type that extends SkiaControl, allowing for binding operations on UI elements.
    return:
      type: '{T}'
      description: Returns the view after setting up the binding.
    content.vb: Public Shared Function BindProperty(Of T As SkiaControl)(view As T, targetProperty As BindableProperty, source As INotifyPropertyChanged, path As String, mode As BindingMode = BindingMode.Default) As T
  overload: DrawnUi.Draw.FluentExtensions.BindProperty*
  nameWithType.vb: FluentExtensions.BindProperty(Of T)(T, BindableProperty, INotifyPropertyChanged, String, BindingMode)
  fullName.vb: DrawnUi.Draw.FluentExtensions.BindProperty(Of T)(T, Microsoft.Maui.Controls.BindableProperty, System.ComponentModel.INotifyPropertyChanged, String, Microsoft.Maui.Controls.BindingMode)
  name.vb: BindProperty(Of T)(T, BindableProperty, INotifyPropertyChanged, String, BindingMode)
- uid: DrawnUi.Draw.FluentExtensions.BindProperty``2(``0,Microsoft.Maui.Controls.BindableProperty,System.String,Microsoft.Maui.Controls.IValueConverter,System.Object,Microsoft.Maui.Controls.BindingMode)
  commentId: M:DrawnUi.Draw.FluentExtensions.BindProperty``2(``0,Microsoft.Maui.Controls.BindableProperty,System.String,Microsoft.Maui.Controls.IValueConverter,System.Object,Microsoft.Maui.Controls.BindingMode)
  id: BindProperty``2(``0,Microsoft.Maui.Controls.BindableProperty,System.String,Microsoft.Maui.Controls.IValueConverter,System.Object,Microsoft.Maui.Controls.BindingMode)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: BindProperty<T, TProperty>(T, BindableProperty, string, IValueConverter, object, BindingMode)
  nameWithType: FluentExtensions.BindProperty<T, TProperty>(T, BindableProperty, string, IValueConverter, object, BindingMode)
  fullName: DrawnUi.Draw.FluentExtensions.BindProperty<T, TProperty>(T, Microsoft.Maui.Controls.BindableProperty, string, Microsoft.Maui.Controls.IValueConverter, object, Microsoft.Maui.Controls.BindingMode)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BindProperty
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 229
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets up a simple binding for a property with a converter
  example: []
  syntax:
    content: 'public static T BindProperty<T, TProperty>(this T view, BindableProperty targetProperty, string path, IValueConverter converter, object converterParameter = null, BindingMode mode = BindingMode.Default) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set the binding for
    - id: targetProperty
      type: Microsoft.Maui.Controls.BindableProperty
      description: The target property
    - id: path
      type: System.String
      description: The binding path
    - id: converter
      type: Microsoft.Maui.Controls.IValueConverter
      description: The value converter
    - id: converterParameter
      type: System.Object
      description: The converter parameter
    - id: mode
      type: Microsoft.Maui.Controls.BindingMode
      description: The binding mode
    typeParameters:
    - id: T
      description: Type of SkiaControl
    - id: TProperty
      description: Type of the property
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function BindProperty(Of T As SkiaControl, TProperty)(view As T, targetProperty As BindableProperty, path As String, converter As IValueConverter, converterParameter As Object = Nothing, mode As BindingMode = BindingMode.Default) As T
  overload: DrawnUi.Draw.FluentExtensions.BindProperty*
  nameWithType.vb: FluentExtensions.BindProperty(Of T, TProperty)(T, BindableProperty, String, IValueConverter, Object, BindingMode)
  fullName.vb: DrawnUi.Draw.FluentExtensions.BindProperty(Of T, TProperty)(T, Microsoft.Maui.Controls.BindableProperty, String, Microsoft.Maui.Controls.IValueConverter, Object, Microsoft.Maui.Controls.BindingMode)
  name.vb: BindProperty(Of T, TProperty)(T, BindableProperty, String, IValueConverter, Object, BindingMode)
- uid: DrawnUi.Draw.FluentExtensions.WithTop(Microsoft.Maui.Thickness,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithTop(Microsoft.Maui.Thickness,System.Double)
  id: WithTop(Microsoft.Maui.Thickness,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithTop(Thickness, double)
  nameWithType: FluentExtensions.WithTop(Thickness, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithTop(Microsoft.Maui.Thickness, double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithTop
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 239
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static Thickness WithTop(this Thickness existing, double value)
    parameters:
    - id: existing
      type: Microsoft.Maui.Thickness
    - id: value
      type: System.Double
    return:
      type: Microsoft.Maui.Thickness
    content.vb: Public Shared Function WithTop(existing As Thickness, value As Double) As Thickness
  overload: DrawnUi.Draw.FluentExtensions.WithTop*
  nameWithType.vb: FluentExtensions.WithTop(Thickness, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithTop(Microsoft.Maui.Thickness, Double)
  name.vb: WithTop(Thickness, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithBottom(Microsoft.Maui.Thickness,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithBottom(Microsoft.Maui.Thickness,System.Double)
  id: WithBottom(Microsoft.Maui.Thickness,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithBottom(Thickness, double)
  nameWithType: FluentExtensions.WithBottom(Thickness, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithBottom(Microsoft.Maui.Thickness, double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithBottom
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 249
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static Thickness WithBottom(this Thickness existing, double value)
    parameters:
    - id: existing
      type: Microsoft.Maui.Thickness
    - id: value
      type: System.Double
    return:
      type: Microsoft.Maui.Thickness
    content.vb: Public Shared Function WithBottom(existing As Thickness, value As Double) As Thickness
  overload: DrawnUi.Draw.FluentExtensions.WithBottom*
  nameWithType.vb: FluentExtensions.WithBottom(Thickness, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithBottom(Microsoft.Maui.Thickness, Double)
  name.vb: WithBottom(Thickness, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithLeft(Microsoft.Maui.Thickness,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithLeft(Microsoft.Maui.Thickness,System.Double)
  id: WithLeft(Microsoft.Maui.Thickness,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithLeft(Thickness, double)
  nameWithType: FluentExtensions.WithLeft(Thickness, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithLeft(Microsoft.Maui.Thickness, double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithLeft
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 259
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static Thickness WithLeft(this Thickness existing, double value)
    parameters:
    - id: existing
      type: Microsoft.Maui.Thickness
    - id: value
      type: System.Double
    return:
      type: Microsoft.Maui.Thickness
    content.vb: Public Shared Function WithLeft(existing As Thickness, value As Double) As Thickness
  overload: DrawnUi.Draw.FluentExtensions.WithLeft*
  nameWithType.vb: FluentExtensions.WithLeft(Thickness, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithLeft(Microsoft.Maui.Thickness, Double)
  name.vb: WithLeft(Thickness, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithRight(Microsoft.Maui.Thickness,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithRight(Microsoft.Maui.Thickness,System.Double)
  id: WithRight(Microsoft.Maui.Thickness,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithRight(Thickness, double)
  nameWithType: FluentExtensions.WithRight(Thickness, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithRight(Microsoft.Maui.Thickness, double)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithRight
    path: ../src/Maui/DrawnUi/Internals/Extensions/FluentExtensions.Maui.cs
    startLine: 269
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static Thickness WithRight(this Thickness existing, double value)
    parameters:
    - id: existing
      type: Microsoft.Maui.Thickness
    - id: value
      type: System.Double
    return:
      type: Microsoft.Maui.Thickness
    content.vb: Public Shared Function WithRight(existing As Thickness, value As Double) As Thickness
  overload: DrawnUi.Draw.FluentExtensions.WithRight*
  nameWithType.vb: FluentExtensions.WithRight(Thickness, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithRight(Microsoft.Maui.Thickness, Double)
  name.vb: WithRight(Thickness, Double)
- uid: DrawnUi.Draw.FluentExtensions.Assign``1(``0,``0@)
  commentId: M:DrawnUi.Draw.FluentExtensions.Assign``1(``0,``0@)
  id: Assign``1(``0,``0@)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: Assign<T>(T, out T)
  nameWithType: FluentExtensions.Assign<T>(T, out T)
  fullName: DrawnUi.Draw.FluentExtensions.Assign<T>(T, out T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Assign
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 23
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Assigns the control to a variable and returns the control to continue the fluent chain
  example: []
  syntax:
    content: 'public static T Assign<T>(this T control, out T variable) where T : SkiaControl'
    parameters:
    - id: control
      type: '{T}'
      description: The control to assign
    - id: variable
      type: '{T}'
      description: The out variable to store the reference
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function Assign(Of T As SkiaControl)(control As T, variable As T) As T
  overload: DrawnUi.Draw.FluentExtensions.Assign*
  nameWithType.vb: FluentExtensions.Assign(Of T)(T, T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.Assign(Of T)(T, T)
  name.vb: Assign(Of T)(T, T)
- uid: DrawnUi.Draw.FluentExtensions.AssignParent``1(``0,DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.FluentExtensions.AssignParent``1(``0,DrawnUi.Draw.SkiaControl)
  id: AssignParent``1(``0,DrawnUi.Draw.SkiaControl)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: AssignParent<T>(T, SkiaControl)
  nameWithType: FluentExtensions.AssignParent<T>(T, SkiaControl)
  fullName: DrawnUi.Draw.FluentExtensions.AssignParent<T>(T, DrawnUi.Draw.SkiaControl)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AssignParent
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 36
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Assigns the control to a parent and returns the control for fluent chaining.
  example: []
  syntax:
    content: 'public static T AssignParent<T>(this T control, SkiaControl parent) where T : SkiaControl'
    parameters:
    - id: control
      type: '{T}'
      description: The control to assign
    - id: parent
      type: DrawnUi.Draw.SkiaControl
      description: The parent control to add to
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function AssignParent(Of T As SkiaControl)(control As T, parent As SkiaControl) As T
  overload: DrawnUi.Draw.FluentExtensions.AssignParent*
  nameWithType.vb: FluentExtensions.AssignParent(Of T)(T, SkiaControl)
  fullName.vb: DrawnUi.Draw.FluentExtensions.AssignParent(Of T)(T, DrawnUi.Draw.SkiaControl)
  name.vb: AssignParent(Of T)(T, SkiaControl)
- uid: DrawnUi.Draw.FluentExtensions.Adapt``1(``0,System.Action{``0})
  commentId: M:DrawnUi.Draw.FluentExtensions.Adapt``1(``0,System.Action{``0})
  id: Adapt``1(``0,System.Action{``0})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: Adapt<T>(T, Action<T>)
  nameWithType: FluentExtensions.Adapt<T>(T, Action<T>)
  fullName: DrawnUi.Draw.FluentExtensions.Adapt<T>(T, System.Action<T>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Adapt
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 49
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Performs an action on the control and returns it to continue the fluent chain
  example: []
  syntax:
    content: 'public static T Adapt<T>(this T view, Action<T> action) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to act upon
    - id: action
      type: System.Action{{T}}
      description: The action to perform
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function Adapt(Of T As SkiaControl)(view As T, action As Action(Of T)) As T
  overload: DrawnUi.Draw.FluentExtensions.Adapt*
  nameWithType.vb: FluentExtensions.Adapt(Of T)(T, Action(Of T))
  fullName.vb: DrawnUi.Draw.FluentExtensions.Adapt(Of T)(T, System.Action(Of T))
  name.vb: Adapt(Of T)(T, Action(Of T))
- uid: DrawnUi.Draw.FluentExtensions.WithGestures``1(``0,System.Func{``0,DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo,DrawnUi.Draw.ISkiaGestureListener})
  commentId: M:DrawnUi.Draw.FluentExtensions.WithGestures``1(``0,System.Func{``0,DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo,DrawnUi.Draw.ISkiaGestureListener})
  id: WithGestures``1(``0,System.Func{``0,DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo,DrawnUi.Draw.ISkiaGestureListener})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithGestures<T>(T, Func<T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener>)
  nameWithType: FluentExtensions.WithGestures<T>(T, Func<T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener>)
  fullName: DrawnUi.Draw.FluentExtensions.WithGestures<T>(T, System.Func<T, DrawnUi.Draw.SkiaGesturesParameters, DrawnUi.Draw.GestureEventProcessingInfo, DrawnUi.Draw.ISkiaGestureListener>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithGestures
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 72
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Attaches a gesture handler to a SkiaLayout, allowing custom gesture processing.

    You must return this control if you consumed a gesture, return null if not.

    The UP gesture should be marked as consumed ONLY for specific scenarios, return null for it if unsure.
  example: []
  syntax:
    content: 'public static T WithGestures<T>(this T view, Func<T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener> func) where T : SkiaLayout'
    parameters:
    - id: view
      type: '{T}'
      description: The layout to attach gestures to
    - id: func
      type: System.Func{{T},DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo,DrawnUi.Draw.ISkiaGestureListener}
      description: A function that returns a gesture listener for the layout
    typeParameters:
    - id: T
      description: Type of SkiaLayout
    return:
      type: '{T}'
      description: The layout for chaining
    content.vb: Public Shared Function WithGestures(Of T As SkiaLayout)(view As T, func As Func(Of T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener)) As T
  overload: DrawnUi.Draw.FluentExtensions.WithGestures*
  nameWithType.vb: FluentExtensions.WithGestures(Of T)(T, Func(Of T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener))
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithGestures(Of T)(T, System.Func(Of T, DrawnUi.Draw.SkiaGesturesParameters, DrawnUi.Draw.GestureEventProcessingInfo, DrawnUi.Draw.ISkiaGestureListener))
  name.vb: WithGestures(Of T)(T, Func(Of T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener))
- uid: DrawnUi.Draw.FluentExtensions.Initialize``1(``0,System.Action{``0})
  commentId: M:DrawnUi.Draw.FluentExtensions.Initialize``1(``0,System.Action{``0})
  id: Initialize``1(``0,System.Action{``0})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: Initialize<T>(T, Action<T>)
  nameWithType: FluentExtensions.Initialize<T>(T, Action<T>)
  fullName: DrawnUi.Draw.FluentExtensions.Initialize<T>(T, System.Action<T>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Initialize
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 90
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Registers a callback to be executed after the control is added to the view tree and initialized.

    Use for setup that requires the control to be part of the visual tree.

    This is called after the control default content was created and all variables have been assigned inside the fluent chain.
  example: []
  syntax:
    content: 'public static T Initialize<T>(this T view, Action<T> action) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to initialize
    - id: action
      type: System.Action{{T}}
      description: Initialization logic to run
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function Initialize(Of T As SkiaControl)(view As T, action As Action(Of T)) As T
  overload: DrawnUi.Draw.FluentExtensions.Initialize*
  nameWithType.vb: FluentExtensions.Initialize(Of T)(T, Action(Of T))
  fullName.vb: DrawnUi.Draw.FluentExtensions.Initialize(Of T)(T, System.Action(Of T))
  name.vb: Initialize(Of T)(T, Action(Of T))
- uid: DrawnUi.Draw.FluentExtensions.OnPaint``1(``0,System.Action{``0,DrawnUi.Draw.DrawingContext})
  commentId: M:DrawnUi.Draw.FluentExtensions.OnPaint``1(``0,System.Action{``0,DrawnUi.Draw.DrawingContext})
  id: OnPaint``1(``0,System.Action{``0,DrawnUi.Draw.DrawingContext})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: OnPaint<T>(T, Action<T, DrawingContext>)
  nameWithType: FluentExtensions.OnPaint<T>(T, Action<T, DrawingContext>)
  fullName: DrawnUi.Draw.FluentExtensions.OnPaint<T>(T, System.Action<T, DrawnUi.Draw.DrawingContext>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnPaint
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 104
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Registers a callback to be executed during the paint phase of the control's rendering.

    Called inside the base.Paint(..).
  example: []
  syntax:
    content: 'public static T OnPaint<T>(this T view, Action<T, DrawingContext> action) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to attach paint logic to
    - id: action
      type: System.Action{{T},DrawnUi.Draw.DrawingContext}
      description: Paint logic to run
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function OnPaint(Of T As SkiaControl)(view As T, action As Action(Of T, DrawingContext)) As T
  overload: DrawnUi.Draw.FluentExtensions.OnPaint*
  nameWithType.vb: FluentExtensions.OnPaint(Of T)(T, Action(Of T, DrawingContext))
  fullName.vb: DrawnUi.Draw.FluentExtensions.OnPaint(Of T)(T, System.Action(Of T, DrawnUi.Draw.DrawingContext))
  name.vb: OnPaint(Of T)(T, Action(Of T, DrawingContext))
- uid: DrawnUi.Draw.FluentExtensions.OnBindingContextSet``1(``0,System.Action{``0,System.Object},System.String[])
  commentId: M:DrawnUi.Draw.FluentExtensions.OnBindingContextSet``1(``0,System.Action{``0,System.Object},System.String[])
  id: OnBindingContextSet``1(``0,System.Action{``0,System.Object},System.String[])
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: OnBindingContextSet<T>(T, Action<T, object>, string[])
  nameWithType: FluentExtensions.OnBindingContextSet<T>(T, Action<T, object>, string[])
  fullName: DrawnUi.Draw.FluentExtensions.OnBindingContextSet<T>(T, System.Action<T, object>, string[])
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnBindingContextSet
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 119
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Registers a callback to be executed when the control's BindingContext was set/changed.

    Called inside base.ApplyBindingContext().
  example: []
  syntax:
    content: 'public static T OnBindingContextSet<T>(this T control, Action<T, object> callback, string[] propertyFilter = null) where T : SkiaControl'
    parameters:
    - id: control
      type: '{T}'
      description: The control to observe
    - id: callback
      type: System.Action{{T},System.Object}
      description: Callback to execute when BindingContext is set
    - id: propertyFilter
      type: System.String[]
      description: Optional property filter
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function OnBindingContextSet(Of T As SkiaControl)(control As T, callback As Action(Of T, Object), propertyFilter As String() = Nothing) As T
  overload: DrawnUi.Draw.FluentExtensions.OnBindingContextSet*
  nameWithType.vb: FluentExtensions.OnBindingContextSet(Of T)(T, Action(Of T, Object), String())
  fullName.vb: DrawnUi.Draw.FluentExtensions.OnBindingContextSet(Of T)(T, System.Action(Of T, Object), String())
  name.vb: OnBindingContextSet(Of T)(T, Action(Of T, Object), String())
- uid: DrawnUi.Draw.FluentExtensions.ObserveSelf``1(``0,System.Action{``0,System.String})
  commentId: M:DrawnUi.Draw.FluentExtensions.ObserveSelf``1(``0,System.Action{``0,System.String})
  id: ObserveSelf``1(``0,System.Action{``0,System.String})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: ObserveSelf<T>(T, Action<T, string>)
  nameWithType: FluentExtensions.ObserveSelf<T>(T, Action<T, string>)
  fullName: DrawnUi.Draw.FluentExtensions.ObserveSelf<T>(T, System.Action<T, string>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObserveSelf
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 145
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Subscribes to PropertyChanged of this control, will unsubscribe upon control disposal.
  example: []
  syntax:
    content: 'public static T ObserveSelf<T>(this T view, Action<T, string> action) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
    - id: action
      type: System.Action{{T},System.String}
    typeParameters:
    - id: T
      description: Type of the target control (the one being extended)
    return:
      type: '{T}'
      description: The target control for chaining
    content.vb: Public Shared Function ObserveSelf(Of T As SkiaControl)(view As T, action As Action(Of T, String)) As T
  overload: DrawnUi.Draw.FluentExtensions.ObserveSelf*
  nameWithType.vb: FluentExtensions.ObserveSelf(Of T)(T, Action(Of T, String))
  fullName.vb: DrawnUi.Draw.FluentExtensions.ObserveSelf(Of T)(T, System.Action(Of T, String))
  name.vb: ObserveSelf(Of T)(T, Action(Of T, String))
- uid: DrawnUi.Draw.FluentExtensions.Observe``2(``0,``1,System.Action{``0,System.String},System.String[])
  commentId: M:DrawnUi.Draw.FluentExtensions.Observe``2(``0,``1,System.Action{``0,System.String},System.String[])
  id: Observe``2(``0,``1,System.Action{``0,System.String},System.String[])
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: Observe<T, TSource>(T, TSource, Action<T, string>, string[])
  nameWithType: FluentExtensions.Observe<T, TSource>(T, TSource, Action<T, string>, string[])
  fullName: DrawnUi.Draw.FluentExtensions.Observe<T, TSource>(T, TSource, System.Action<T, string>, string[])
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Observe
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 161
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Subscribes to property changes on a source control and executes a callback when they occur.

    Will unsubscribe upon control disposal.
  example: []
  syntax:
    content: 'public static T Observe<T, TSource>(this T control, TSource target, Action<T, string> callback, string[] propertyFilter = null) where T : SkiaControl where TSource : INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: The control subscribing to changes
    - id: target
      type: '{TSource}'
      description: The control being observed
    - id: callback
      type: System.Action{{T},System.String}
      description: Callback that receives the property name when changed
    - id: propertyFilter
      type: System.String[]
      description: Optional filter to only trigger on specific properties
    typeParameters:
    - id: T
      description: Type of the target control (the one being extended)
    - id: TSource
      description: Type of the source control (the one being observed)
    return:
      type: '{T}'
      description: The target control for chaining
    content.vb: Public Shared Function Observe(Of T As SkiaControl, TSource As INotifyPropertyChanged)(control As T, target As TSource, callback As Action(Of T, String), propertyFilter As String() = Nothing) As T
  overload: DrawnUi.Draw.FluentExtensions.Observe*
  nameWithType.vb: FluentExtensions.Observe(Of T, TSource)(T, TSource, Action(Of T, String), String())
  fullName.vb: DrawnUi.Draw.FluentExtensions.Observe(Of T, TSource)(T, TSource, System.Action(Of T, String), String())
  name.vb: Observe(Of T, TSource)(T, TSource, Action(Of T, String), String())
- uid: DrawnUi.Draw.FluentExtensions.ObservePropertiesOn``3(``0,``1,System.Func{``2},System.String,System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  commentId: M:DrawnUi.Draw.FluentExtensions.ObservePropertiesOn``3(``0,``1,System.Func{``2},System.String,System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  id: ObservePropertiesOn``3(``0,``1,System.Func{``2},System.String,System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: ObservePropertiesOn<T, TParent, TTarget>(T, TParent, Func<TTarget>, string, IEnumerable<string>, Action<T>)
  nameWithType: FluentExtensions.ObservePropertiesOn<T, TParent, TTarget>(T, TParent, Func<TTarget>, string, IEnumerable<string>, Action<T>)
  fullName: DrawnUi.Draw.FluentExtensions.ObservePropertiesOn<T, TParent, TTarget>(T, TParent, System.Func<TTarget>, string, System.Collections.Generic.IEnumerable<string>, System.Action<T>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObservePropertiesOn
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 217
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Observes specific properties on a dynamically resolved target object using a function selector.

    When the parent's properties change, re-evaluates the selector and automatically 

    unsubscribes from old target and subscribes to new one.

    You can omit BindingContext as it will be added at all times.
  example: []
  syntax:
    content: 'public static T ObservePropertiesOn<T, TParent, TTarget>(this T control, TParent parent, Func<TTarget> targetSelector, string parentPropertyName, IEnumerable<string> propertyNames, Action<T> callback) where T : SkiaControl where TParent : INotifyPropertyChanged where TTarget : class, INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: The control subscribing to changes
    - id: parent
      type: '{TParent}'
      description: The parent object that contains the dynamic property
    - id: targetSelector
      type: System.Func{{TTarget}}
      description: Function that selects the target object (e.g., () =&gt; CurrentTimer)
    - id: parentPropertyName
      type: System.String
      description: Name of the property on parent that affects the target selector
    - id: propertyNames
      type: System.Collections.Generic.IEnumerable{System.String}
      description: Names of the properties to observe on the target
    - id: callback
      type: System.Action{{T}}
      description: Callback that receives the control when target's specified properties change
    typeParameters:
    - id: T
      description: Type of the control being extended
    - id: TParent
      description: Type of the parent object that contains the dynamic property
    - id: TTarget
      description: Type of the target object to observe
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function ObservePropertiesOn(Of T As SkiaControl, TParent As INotifyPropertyChanged, TTarget As {Class, INotifyPropertyChanged})(control As T, parent As TParent, targetSelector As Func(Of TTarget), parentPropertyName As String, propertyNames As IEnumerable(Of String), callback As Action(Of T)) As T
  overload: DrawnUi.Draw.FluentExtensions.ObservePropertiesOn*
  nameWithType.vb: FluentExtensions.ObservePropertiesOn(Of T, TParent, TTarget)(T, TParent, Func(Of TTarget), String, IEnumerable(Of String), Action(Of T))
  fullName.vb: DrawnUi.Draw.FluentExtensions.ObservePropertiesOn(Of T, TParent, TTarget)(T, TParent, System.Func(Of TTarget), String, System.Collections.Generic.IEnumerable(Of String), System.Action(Of T))
  name.vb: ObservePropertiesOn(Of T, TParent, TTarget)(T, TParent, Func(Of TTarget), String, IEnumerable(Of String), Action(Of T))
- uid: DrawnUi.Draw.FluentExtensions.ObservePropertyOn``3(``0,``1,System.Func{``2},System.String,System.String,System.Action{``0})
  commentId: M:DrawnUi.Draw.FluentExtensions.ObservePropertyOn``3(``0,``1,System.Func{``2},System.String,System.String,System.Action{``0})
  id: ObservePropertyOn``3(``0,``1,System.Func{``2},System.String,System.String,System.Action{``0})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: ObservePropertyOn<T, TParent, TTarget>(T, TParent, Func<TTarget>, string, string, Action<T>)
  nameWithType: FluentExtensions.ObservePropertyOn<T, TParent, TTarget>(T, TParent, Func<TTarget>, string, string, Action<T>)
  fullName: DrawnUi.Draw.FluentExtensions.ObservePropertyOn<T, TParent, TTarget>(T, TParent, System.Func<TTarget>, string, string, System.Action<T>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObservePropertyOn
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 258
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Observes a specific property on a dynamically resolved target object using a function selector.

    When the parent's properties change, re-evaluates the selector and automatically 

    unsubscribes from old target and subscribes to new one.
  example: []
  syntax:
    content: 'public static T ObservePropertyOn<T, TParent, TTarget>(this T control, TParent parent, Func<TTarget> targetSelector, string parentPropertyName, string propertyName, Action<T> callback) where T : SkiaControl where TParent : INotifyPropertyChanged where TTarget : class, INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: ''
    - id: parent
      type: '{TParent}'
      description: ''
    - id: targetSelector
      type: System.Func{{TTarget}}
      description: ''
    - id: parentPropertyName
      type: System.String
      description: ''
    - id: propertyName
      type: System.String
      description: ''
    - id: callback
      type: System.Action{{T}}
      description: ''
    typeParameters:
    - id: T
      description: ''
    - id: TParent
      description: ''
    - id: TTarget
      description: ''
    return:
      type: '{T}'
      description: ''
    content.vb: Public Shared Function ObservePropertyOn(Of T As SkiaControl, TParent As INotifyPropertyChanged, TTarget As {Class, INotifyPropertyChanged})(control As T, parent As TParent, targetSelector As Func(Of TTarget), parentPropertyName As String, propertyName As String, callback As Action(Of T)) As T
  overload: DrawnUi.Draw.FluentExtensions.ObservePropertyOn*
  nameWithType.vb: FluentExtensions.ObservePropertyOn(Of T, TParent, TTarget)(T, TParent, Func(Of TTarget), String, String, Action(Of T))
  fullName.vb: DrawnUi.Draw.FluentExtensions.ObservePropertyOn(Of T, TParent, TTarget)(T, TParent, System.Func(Of TTarget), String, String, System.Action(Of T))
  name.vb: ObservePropertyOn(Of T, TParent, TTarget)(T, TParent, Func(Of TTarget), String, String, Action(Of T))
- uid: DrawnUi.Draw.FluentExtensions.ObserveOn``3(``0,``1,System.Func{``2},System.String,System.Action{``0,System.String},System.String[])
  commentId: M:DrawnUi.Draw.FluentExtensions.ObserveOn``3(``0,``1,System.Func{``2},System.String,System.Action{``0,System.String},System.String[])
  id: ObserveOn``3(``0,``1,System.Func{``2},System.String,System.Action{``0,System.String},System.String[])
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: ObserveOn<T, TParent, TTarget>(T, TParent, Func<TTarget>, string, Action<T, string>, string[])
  nameWithType: FluentExtensions.ObserveOn<T, TParent, TTarget>(T, TParent, Func<TTarget>, string, Action<T, string>, string[])
  fullName: DrawnUi.Draw.FluentExtensions.ObserveOn<T, TParent, TTarget>(T, TParent, System.Func<TTarget>, string, System.Action<T, string>, string[])
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObserveOn
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 300
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Observes a dynamically resolved target object using a function selector.

    When the parent's properties change, re-evaluates the selector and automatically 

    unsubscribes from old target and subscribes to new one.

    AOT-compatible.
  example: []
  syntax:
    content: 'public static T ObserveOn<T, TParent, TTarget>(this T control, TParent parent, Func<TTarget> targetSelector, string parentPropertyName, Action<T, string> callback, string[] propertyFilter = null) where T : SkiaControl where TParent : INotifyPropertyChanged where TTarget : class, INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: The control subscribing to changes
    - id: parent
      type: '{TParent}'
      description: The parent object that contains the dynamic property
    - id: targetSelector
      type: System.Func{{TTarget}}
      description: Function that selects the target object (e.g., () =&gt; CurrentTimer)
    - id: parentPropertyName
      type: System.String
      description: Name of the property on parent that affects the target selector
    - id: callback
      type: System.Action{{T},System.String}
      description: Callback that receives the control and property name when target's properties change
    - id: propertyFilter
      type: System.String[]
      description: Optional filter to only trigger on specific properties
    typeParameters:
    - id: T
      description: Type of the control being extended
    - id: TParent
      description: Type of the parent object that contains the dynamic property
    - id: TTarget
      description: Type of the target object to observe
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function ObserveOn(Of T As SkiaControl, TParent As INotifyPropertyChanged, TTarget As {Class, INotifyPropertyChanged})(control As T, parent As TParent, targetSelector As Func(Of TTarget), parentPropertyName As String, callback As Action(Of T, String), propertyFilter As String() = Nothing) As T
  overload: DrawnUi.Draw.FluentExtensions.ObserveOn*
  nameWithType.vb: FluentExtensions.ObserveOn(Of T, TParent, TTarget)(T, TParent, Func(Of TTarget), String, Action(Of T, String), String())
  fullName.vb: DrawnUi.Draw.FluentExtensions.ObserveOn(Of T, TParent, TTarget)(T, TParent, System.Func(Of TTarget), String, System.Action(Of T, String), String())
  name.vb: ObserveOn(Of T, TParent, TTarget)(T, TParent, Func(Of TTarget), String, Action(Of T, String), String())
- uid: DrawnUi.Draw.FluentExtensions.ObserveProperty``2(``0,``1,System.String,System.Action{``0})
  commentId: M:DrawnUi.Draw.FluentExtensions.ObserveProperty``2(``0,``1,System.String,System.Action{``0})
  id: ObserveProperty``2(``0,``1,System.String,System.Action{``0})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: ObserveProperty<T, TSource>(T, TSource, string, Action<T>)
  nameWithType: FluentExtensions.ObserveProperty<T, TSource>(T, TSource, string, Action<T>)
  fullName: DrawnUi.Draw.FluentExtensions.ObserveProperty<T, TSource>(T, TSource, string, System.Action<T>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObserveProperty
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 410
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Subscribes to one specific property changes on a source control and executes a callback when they occur.

    Will unsubscribe upon control disposal.
  example: []
  syntax:
    content: 'public static T ObserveProperty<T, TSource>(this T control, TSource target, string propertyName, Action<T> callback) where T : SkiaControl where TSource : INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: ''
    - id: target
      type: '{TSource}'
      description: ''
    - id: propertyName
      type: System.String
      description: ''
    - id: callback
      type: System.Action{{T}}
      description: ''
    typeParameters:
    - id: T
      description: ''
    - id: TSource
      description: ''
    return:
      type: '{T}'
      description: ''
    content.vb: Public Shared Function ObserveProperty(Of T As SkiaControl, TSource As INotifyPropertyChanged)(control As T, target As TSource, propertyName As String, callback As Action(Of T)) As T
  overload: DrawnUi.Draw.FluentExtensions.ObserveProperty*
  nameWithType.vb: FluentExtensions.ObserveProperty(Of T, TSource)(T, TSource, String, Action(Of T))
  fullName.vb: DrawnUi.Draw.FluentExtensions.ObserveProperty(Of T, TSource)(T, TSource, String, System.Action(Of T))
  name.vb: ObserveProperty(Of T, TSource)(T, TSource, String, Action(Of T))
- uid: DrawnUi.Draw.FluentExtensions.ObserveProperty``2(``0,System.Func{``1},System.String,System.Action{``0})
  commentId: M:DrawnUi.Draw.FluentExtensions.ObserveProperty``2(``0,System.Func{``1},System.String,System.Action{``0})
  id: ObserveProperty``2(``0,System.Func{``1},System.String,System.Action{``0})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: ObserveProperty<T, TSource>(T, Func<TSource>, string, Action<T>)
  nameWithType: FluentExtensions.ObserveProperty<T, TSource>(T, Func<TSource>, string, Action<T>)
  fullName: DrawnUi.Draw.FluentExtensions.ObserveProperty<T, TSource>(T, System.Func<TSource>, string, System.Action<T>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObserveProperty
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 435
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Subscribes to one specific property changes on a source control obtained via lambda expression and executes a callback when they occur.

    Will unsubscribe upon control disposal.
  example: []
  syntax:
    content: 'public static T ObserveProperty<T, TSource>(this T control, Func<TSource> targetSelector, string propertyName, Action<T> callback) where T : SkiaControl where TSource : INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: The control subscribing to changes
    - id: targetSelector
      type: System.Func{{TSource}}
      description: Lambda expression that returns the target control (e.g., () =&gt; clickLabel)
    - id: propertyName
      type: System.String
      description: Name of the property to observe
    - id: callback
      type: System.Action{{T}}
      description: Callback to execute when the property changes
    typeParameters:
    - id: T
      description: Type of the control being extended
    - id: TSource
      description: Type of the source control being observed
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function ObserveProperty(Of T As SkiaControl, TSource As INotifyPropertyChanged)(control As T, targetSelector As Func(Of TSource), propertyName As String, callback As Action(Of T)) As T
  overload: DrawnUi.Draw.FluentExtensions.ObserveProperty*
  nameWithType.vb: FluentExtensions.ObserveProperty(Of T, TSource)(T, Func(Of TSource), String, Action(Of T))
  fullName.vb: DrawnUi.Draw.FluentExtensions.ObserveProperty(Of T, TSource)(T, System.Func(Of TSource), String, System.Action(Of T))
  name.vb: ObserveProperty(Of T, TSource)(T, Func(Of TSource), String, Action(Of T))
- uid: DrawnUi.Draw.FluentExtensions.ObserveProperties``2(``0,``1,System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  commentId: M:DrawnUi.Draw.FluentExtensions.ObserveProperties``2(``0,``1,System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  id: ObserveProperties``2(``0,``1,System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: ObserveProperties<T, TSource>(T, TSource, IEnumerable<string>, Action<T>)
  nameWithType: FluentExtensions.ObserveProperties<T, TSource>(T, TSource, IEnumerable<string>, Action<T>)
  fullName: DrawnUi.Draw.FluentExtensions.ObserveProperties<T, TSource>(T, TSource, System.Collections.Generic.IEnumerable<string>, System.Action<T>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObserveProperties
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 465
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Subscribes to specific properties changes on a source control and executes a callback when they occur.


    Will unsubscribe upon control disposal.
  example: []
  syntax:
    content: 'public static T ObserveProperties<T, TSource>(this T control, TSource target, IEnumerable<string> propertyNames, Action<T> callback) where T : SkiaControl where TSource : INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: ''
    - id: target
      type: '{TSource}'
      description: ''
    - id: propertyNames
      type: System.Collections.Generic.IEnumerable{System.String}
      description: ''
    - id: callback
      type: System.Action{{T}}
      description: ''
    typeParameters:
    - id: T
      description: ''
    - id: TSource
      description: ''
    return:
      type: '{T}'
      description: ''
    content.vb: Public Shared Function ObserveProperties(Of T As SkiaControl, TSource As INotifyPropertyChanged)(control As T, target As TSource, propertyNames As IEnumerable(Of String), callback As Action(Of T)) As T
  overload: DrawnUi.Draw.FluentExtensions.ObserveProperties*
  nameWithType.vb: FluentExtensions.ObserveProperties(Of T, TSource)(T, TSource, IEnumerable(Of String), Action(Of T))
  fullName.vb: DrawnUi.Draw.FluentExtensions.ObserveProperties(Of T, TSource)(T, TSource, System.Collections.Generic.IEnumerable(Of String), System.Action(Of T))
  name.vb: ObserveProperties(Of T, TSource)(T, TSource, IEnumerable(Of String), Action(Of T))
- uid: DrawnUi.Draw.FluentExtensions.ObserveProperties``2(``0,System.Func{``1},System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  commentId: M:DrawnUi.Draw.FluentExtensions.ObserveProperties``2(``0,System.Func{``1},System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  id: ObserveProperties``2(``0,System.Func{``1},System.Collections.Generic.IEnumerable{System.String},System.Action{``0})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: ObserveProperties<T, TSource>(T, Func<TSource>, IEnumerable<string>, Action<T>)
  nameWithType: FluentExtensions.ObserveProperties<T, TSource>(T, Func<TSource>, IEnumerable<string>, Action<T>)
  fullName: DrawnUi.Draw.FluentExtensions.ObserveProperties<T, TSource>(T, System.Func<TSource>, System.Collections.Generic.IEnumerable<string>, System.Action<T>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObserveProperties
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 491
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Subscribes to specific properties changes on a source control obtained via lambda expression and executes a callback when they occur.

    Will unsubscribe upon control disposal.
  example: []
  syntax:
    content: 'public static T ObserveProperties<T, TSource>(this T control, Func<TSource> targetSelector, IEnumerable<string> propertyNames, Action<T> callback) where T : SkiaControl where TSource : INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: The control subscribing to changes
    - id: targetSelector
      type: System.Func{{TSource}}
      description: Lambda expression that returns the target control (e.g., () =&gt; clickLabel)
    - id: propertyNames
      type: System.Collections.Generic.IEnumerable{System.String}
      description: Names of the properties to observe
    - id: callback
      type: System.Action{{T}}
      description: Callback to execute when properties change
    typeParameters:
    - id: T
      description: Type of the control being extended
    - id: TSource
      description: Type of the source control being observed
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function ObserveProperties(Of T As SkiaControl, TSource As INotifyPropertyChanged)(control As T, targetSelector As Func(Of TSource), propertyNames As IEnumerable(Of String), callback As Action(Of T)) As T
  overload: DrawnUi.Draw.FluentExtensions.ObserveProperties*
  nameWithType.vb: FluentExtensions.ObserveProperties(Of T, TSource)(T, Func(Of TSource), IEnumerable(Of String), Action(Of T))
  fullName.vb: DrawnUi.Draw.FluentExtensions.ObserveProperties(Of T, TSource)(T, System.Func(Of TSource), System.Collections.Generic.IEnumerable(Of String), System.Action(Of T))
  name.vb: ObserveProperties(Of T, TSource)(T, Func(Of TSource), IEnumerable(Of String), Action(Of T))
- uid: DrawnUi.Draw.FluentExtensions.Observe``2(``0,System.Func{``1},System.Action{``0,System.String},System.String[])
  commentId: M:DrawnUi.Draw.FluentExtensions.Observe``2(``0,System.Func{``1},System.Action{``0,System.String},System.String[])
  id: Observe``2(``0,System.Func{``1},System.Action{``0,System.String},System.String[])
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: Observe<T, TSource>(T, Func<TSource>, Action<T, string>, string[])
  nameWithType: FluentExtensions.Observe<T, TSource>(T, Func<TSource>, Action<T, string>, string[])
  fullName: DrawnUi.Draw.FluentExtensions.Observe<T, TSource>(T, System.Func<TSource>, System.Action<T, string>, string[])
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Observe
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 519
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Observes a control that will be assigned later in the initialization process.
  example: []
  syntax:
    content: 'public static T Observe<T, TSource>(this T control, Func<TSource> sourceFetcher, Action<T, string> callback, string[] propertyFilter = null) where T : SkiaControl where TSource : SkiaControl, INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: The control subscribing to changes
    - id: sourceFetcher
      type: System.Func{{TSource}}
      description: Function that will retrieve the source control when needed
    - id: callback
      type: System.Action{{T},System.String}
      description: Callback that receives the control instance and property name when changed
    - id: propertyFilter
      type: System.String[]
      description: Optional filter to only trigger on specific properties
    typeParameters:
    - id: T
      description: Type of the target control (the one being extended)
    - id: TSource
      description: Type of the source control (the one that will be observed)
    return:
      type: '{T}'
      description: The target control for chaining
    content.vb: Public Shared Function Observe(Of T As SkiaControl, TSource As {SkiaControl, INotifyPropertyChanged})(control As T, sourceFetcher As Func(Of TSource), callback As Action(Of T, String), propertyFilter As String() = Nothing) As T
  overload: DrawnUi.Draw.FluentExtensions.Observe*
  nameWithType.vb: FluentExtensions.Observe(Of T, TSource)(T, Func(Of TSource), Action(Of T, String), String())
  fullName.vb: DrawnUi.Draw.FluentExtensions.Observe(Of T, TSource)(T, System.Func(Of TSource), System.Action(Of T, String), String())
  name.vb: Observe(Of T, TSource)(T, Func(Of TSource), Action(Of T, String), String())
- uid: DrawnUi.Draw.FluentExtensions.Observe``2(``0,System.Func{``1},System.Action{``0},System.String[])
  commentId: M:DrawnUi.Draw.FluentExtensions.Observe``2(``0,System.Func{``1},System.Action{``0},System.String[])
  id: Observe``2(``0,System.Func{``1},System.Action{``0},System.String[])
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: Observe<T, TSource>(T, Func<TSource>, Action<T>, string[])
  nameWithType: FluentExtensions.Observe<T, TSource>(T, Func<TSource>, Action<T>, string[])
  fullName: DrawnUi.Draw.FluentExtensions.Observe<T, TSource>(T, System.Func<TSource>, System.Action<T>, string[])
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Observe
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 582
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Observes a control that will be assigned later in the initialization process.

    Simplified version that doesn't pass the property name to the callback.
  example: []
  syntax:
    content: 'public static T Observe<T, TSource>(this T control, Func<TSource> sourceFetcher, Action<T> callback, string[] propertyFilter = null) where T : SkiaControl where TSource : SkiaControl, INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: The control subscribing to changes
    - id: sourceFetcher
      type: System.Func{{TSource}}
      description: Function that will retrieve the source control when needed
    - id: callback
      type: System.Action{{T}}
      description: Callback that receives only the control instance when changed
    - id: propertyFilter
      type: System.String[]
      description: Optional filter to only trigger on specific properties
    typeParameters:
    - id: T
      description: Type of the target control (the one being extended)
    - id: TSource
      description: Type of the source control (the one that will be observed)
    return:
      type: '{T}'
      description: The target control for chaining
    content.vb: Public Shared Function Observe(Of T As SkiaControl, TSource As {SkiaControl, INotifyPropertyChanged})(control As T, sourceFetcher As Func(Of TSource), callback As Action(Of T), propertyFilter As String() = Nothing) As T
  overload: DrawnUi.Draw.FluentExtensions.Observe*
  nameWithType.vb: FluentExtensions.Observe(Of T, TSource)(T, Func(Of TSource), Action(Of T), String())
  fullName.vb: DrawnUi.Draw.FluentExtensions.Observe(Of T, TSource)(T, System.Func(Of TSource), System.Action(Of T), String())
  name.vb: Observe(Of T, TSource)(T, Func(Of TSource), Action(Of T), String())
- uid: DrawnUi.Draw.FluentExtensions.Observe``2(``0,``1,System.Action{``0},System.String[])
  commentId: M:DrawnUi.Draw.FluentExtensions.Observe``2(``0,``1,System.Action{``0},System.String[])
  id: Observe``2(``0,``1,System.Action{``0},System.String[])
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: Observe<T, TSource>(T, TSource, Action<T>, string[])
  nameWithType: FluentExtensions.Observe<T, TSource>(T, TSource, Action<T>, string[])
  fullName: DrawnUi.Draw.FluentExtensions.Observe<T, TSource>(T, TSource, System.Action<T>, string[])
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Observe
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 604
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Observes a source control with a simplified callback that doesn't include the property name.

    Will unsubscribe upon control disposal.
  example: []
  syntax:
    content: 'public static T Observe<T, TSource>(this T control, TSource target, Action<T> callback, string[] propertyFilter = null) where T : SkiaControl where TSource : INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: The control subscribing to changes
    - id: target
      type: '{TSource}'
      description: The control being observed
    - id: callback
      type: System.Action{{T}}
      description: Callback that receives only the control instance when changed
    - id: propertyFilter
      type: System.String[]
      description: Optional filter to only trigger on specific properties
    typeParameters:
    - id: T
      description: Type of the target control (the one being extended)
    - id: TSource
      description: Type of the source control (the one being observed)
    return:
      type: '{T}'
      description: The target control for chaining
    content.vb: Public Shared Function Observe(Of T As SkiaControl, TSource As INotifyPropertyChanged)(control As T, target As TSource, callback As Action(Of T), propertyFilter As String() = Nothing) As T
  overload: DrawnUi.Draw.FluentExtensions.Observe*
  nameWithType.vb: FluentExtensions.Observe(Of T, TSource)(T, TSource, Action(Of T), String())
  fullName.vb: DrawnUi.Draw.FluentExtensions.Observe(Of T, TSource)(T, TSource, System.Action(Of T), String())
  name.vb: Observe(Of T, TSource)(T, TSource, Action(Of T), String())
- uid: DrawnUi.Draw.FluentExtensions.ObserveBindingContext``2(``0,System.Action{``0,``1,System.String},System.Boolean)
  commentId: M:DrawnUi.Draw.FluentExtensions.ObserveBindingContext``2(``0,System.Action{``0,``1,System.String},System.Boolean)
  id: ObserveBindingContext``2(``0,System.Action{``0,``1,System.String},System.Boolean)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: ObserveBindingContext<T, TSource>(T, Action<T, TSource, string>, bool)
  nameWithType: FluentExtensions.ObserveBindingContext<T, TSource>(T, Action<T, TSource, string>, bool)
  fullName: DrawnUi.Draw.FluentExtensions.ObserveBindingContext<T, TSource>(T, System.Action<T, TSource, string>, bool)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObserveBindingContext
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 633
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Watches for property changes on the control's BindingContext of type TSource.

    Works with both immediate and delayed BindingContext assignment scenarios.
  remarks: >-
    This method handles two scenarios:

    1. The BindingContext is already set when the method is called

    2. The BindingContext will be set sometime after the method is called


    The callback will be invoked immediately after subscription with an empty property name,

    allowing initialization based on the current state.
  example: []
  syntax:
    content: 'public static T ObserveBindingContext<T, TSource>(this T control, Action<T, TSource, string> callback, bool debugTypeMismatch = true) where T : SkiaControl where TSource : INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: The control to watch
    - id: callback
      type: System.Action{{T},{TSource},System.String}
      description: Callback executed when properties change, receiving the control, the typed BindingContext, and the property name
    - id: debugTypeMismatch
      type: System.Boolean
      description: "Whether to log a warning when the actual BindingContext type doesn't match TSource (default: true)"
    typeParameters:
    - id: T
      description: Type of the control
    - id: TSource
      description: Expected type of the BindingContext
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function ObserveBindingContext(Of T As SkiaControl, TSource As INotifyPropertyChanged)(control As T, callback As Action(Of T, TSource, String), debugTypeMismatch As Boolean = True) As T
  overload: DrawnUi.Draw.FluentExtensions.ObserveBindingContext*
  nameWithType.vb: FluentExtensions.ObserveBindingContext(Of T, TSource)(T, Action(Of T, TSource, String), Boolean)
  fullName.vb: DrawnUi.Draw.FluentExtensions.ObserveBindingContext(Of T, TSource)(T, System.Action(Of T, TSource, String), Boolean)
  name.vb: ObserveBindingContext(Of T, TSource)(T, Action(Of T, TSource, String), Boolean)
- uid: DrawnUi.Draw.FluentExtensions.ObserveBindingContextOn``3(``0,``1,System.Action{``0,``1,``2,System.String},System.Boolean)
  commentId: M:DrawnUi.Draw.FluentExtensions.ObserveBindingContextOn``3(``0,``1,System.Action{``0,``1,``2,System.String},System.Boolean)
  id: ObserveBindingContextOn``3(``0,``1,System.Action{``0,``1,``2,System.String},System.Boolean)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: ObserveBindingContextOn<T, TTarget, TSource>(T, TTarget, Action<T, TTarget, TSource, string>, bool)
  nameWithType: FluentExtensions.ObserveBindingContextOn<T, TTarget, TSource>(T, TTarget, Action<T, TTarget, TSource, string>, bool)
  fullName: DrawnUi.Draw.FluentExtensions.ObserveBindingContextOn<T, TTarget, TSource>(T, TTarget, System.Action<T, TTarget, TSource, string>, bool)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ObserveBindingContextOn
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 717
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Watches for property changes on another control's BindingContext of type TSource.
  remarks: >-
    This method handles two scenarios:

    1. The target's BindingContext is already set when the method is called

    2. The target's BindingContext will be set sometime after the method is called
  example: []
  syntax:
    content: 'public static T ObserveBindingContextOn<T, TTarget, TSource>(this T control, TTarget target, Action<T, TTarget, TSource, string> callback, bool debugTypeMismatch = true) where T : SkiaControl where TTarget : SkiaControl where TSource : INotifyPropertyChanged'
    parameters:
    - id: control
      type: '{T}'
      description: The control to extend
    - id: target
      type: '{TTarget}'
      description: The target control whose BindingContext to watch
    - id: callback
      type: System.Action{{T},{TTarget},{TSource},System.String}
      description: Callback executed when properties change, receiving the control, the target control, the typed BindingContext, and the property name
    - id: debugTypeMismatch
      type: System.Boolean
      description: "Whether to log a warning when the actual BindingContext type doesn't match TSource (default: true)"
    typeParameters:
    - id: T
      description: Type of the control being extended
    - id: TTarget
      description: Type of the target control whose BindingContext we're watching
    - id: TSource
      description: Expected type of the target control's BindingContext
    return:
      type: '{T}'
      description: The original control for chaining
    content.vb: Public Shared Function ObserveBindingContextOn(Of T As SkiaControl, TTarget As SkiaControl, TSource As INotifyPropertyChanged)(control As T, target As TTarget, callback As Action(Of T, TTarget, TSource, String), debugTypeMismatch As Boolean = True) As T
  overload: DrawnUi.Draw.FluentExtensions.ObserveBindingContextOn*
  nameWithType.vb: FluentExtensions.ObserveBindingContextOn(Of T, TTarget, TSource)(T, TTarget, Action(Of T, TTarget, TSource, String), Boolean)
  fullName.vb: DrawnUi.Draw.FluentExtensions.ObserveBindingContextOn(Of T, TTarget, TSource)(T, TTarget, System.Action(Of T, TTarget, TSource, String), Boolean)
  name.vb: ObserveBindingContextOn(Of T, TTarget, TSource)(T, TTarget, Action(Of T, TTarget, TSource, String), Boolean)
- uid: DrawnUi.Draw.FluentExtensions.OnToggled``1(``0,System.Action{``0,System.Boolean})
  commentId: M:DrawnUi.Draw.FluentExtensions.OnToggled``1(``0,System.Action{``0,System.Boolean})
  id: OnToggled``1(``0,System.Action{``0,System.Boolean})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: OnToggled<T>(T, Action<T, bool>)
  nameWithType: FluentExtensions.OnToggled<T>(T, Action<T, bool>)
  fullName: DrawnUi.Draw.FluentExtensions.OnToggled<T>(T, System.Action<T, bool>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnToggled
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 805
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: State change callback for SkiaToggle and related controls
  example: []
  syntax:
    content: 'public static T OnToggled<T>(this T view, Action<T, bool> action) where T : SkiaToggle'
    parameters:
    - id: view
      type: '{T}'
      description: ''
    - id: action
      type: System.Action{{T},System.Boolean}
      description: ''
    typeParameters:
    - id: T
      description: ''
    return:
      type: '{T}'
      description: ''
    content.vb: Public Shared Function OnToggled(Of T As SkiaToggle)(view As T, action As Action(Of T, Boolean)) As T
  overload: DrawnUi.Draw.FluentExtensions.OnToggled*
  nameWithType.vb: FluentExtensions.OnToggled(Of T)(T, Action(Of T, Boolean))
  fullName.vb: DrawnUi.Draw.FluentExtensions.OnToggled(Of T)(T, System.Action(Of T, Boolean))
  name.vb: OnToggled(Of T)(T, Action(Of T, Boolean))
- uid: DrawnUi.Draw.FluentExtensions.OnTapped``1(``0,System.Action{``0})
  commentId: M:DrawnUi.Draw.FluentExtensions.OnTapped``1(``0,System.Action{``0})
  id: OnTapped``1(``0,System.Action{``0})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: OnTapped<T>(T, Action<T>)
  nameWithType: FluentExtensions.OnTapped<T>(T, Action<T>)
  fullName: DrawnUi.Draw.FluentExtensions.OnTapped<T>(T, System.Action<T>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnTapped
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 834
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Uses an `AddGestures.SetCommandTapped` with this control, will invoke code in passed callback when tapped.
  example: []
  syntax:
    content: 'public static T OnTapped<T>(this T view, Action<T> action) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: ''
    - id: action
      type: System.Action{{T}}
      description: ''
    typeParameters:
    - id: T
      description: ''
    return:
      type: '{T}'
      description: ''
    content.vb: Public Shared Function OnTapped(Of T As SkiaControl)(view As T, action As Action(Of T)) As T
  overload: DrawnUi.Draw.FluentExtensions.OnTapped*
  nameWithType.vb: FluentExtensions.OnTapped(Of T)(T, Action(Of T))
  fullName.vb: DrawnUi.Draw.FluentExtensions.OnTapped(Of T)(T, System.Action(Of T))
  name.vb: OnTapped(Of T)(T, Action(Of T))
- uid: DrawnUi.Draw.FluentExtensions.OnLongPressing``1(``0,System.Action{``0})
  commentId: M:DrawnUi.Draw.FluentExtensions.OnLongPressing``1(``0,System.Action{``0})
  id: OnLongPressing``1(``0,System.Action{``0})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: OnLongPressing<T>(T, Action<T>)
  nameWithType: FluentExtensions.OnLongPressing<T>(T, Action<T>)
  fullName: DrawnUi.Draw.FluentExtensions.OnLongPressing<T>(T, System.Action<T>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnLongPressing
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 848
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public static T OnLongPressing<T>(this T view, Action<T> action) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
    - id: action
      type: System.Action{{T}}
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function OnLongPressing(Of T As SkiaControl)(view As T, action As Action(Of T)) As T
  overload: DrawnUi.Draw.FluentExtensions.OnLongPressing*
  nameWithType.vb: FluentExtensions.OnLongPressing(Of T)(T, Action(Of T))
  fullName.vb: DrawnUi.Draw.FluentExtensions.OnLongPressing(Of T)(T, System.Action(Of T))
  name.vb: OnLongPressing(Of T)(T, Action(Of T))
- uid: DrawnUi.Draw.FluentExtensions.WithChildren``1(``0,DrawnUi.Draw.SkiaControl[])
  commentId: M:DrawnUi.Draw.FluentExtensions.WithChildren``1(``0,DrawnUi.Draw.SkiaControl[])
  id: WithChildren``1(``0,DrawnUi.Draw.SkiaControl[])
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithChildren<T>(T, params SkiaControl[])
  nameWithType: FluentExtensions.WithChildren<T>(T, params SkiaControl[])
  fullName: DrawnUi.Draw.FluentExtensions.WithChildren<T>(T, params DrawnUi.Draw.SkiaControl[])
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithChildren
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 873
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Adds multiple child controls to a layout
  example: []
  syntax:
    content: 'public static T WithChildren<T>(this T view, params SkiaControl[] children) where T : SkiaLayout'
    parameters:
    - id: view
      type: '{T}'
      description: The layout to add children to
    - id: children
      type: DrawnUi.Draw.SkiaControl[]
      description: The children to add
    typeParameters:
    - id: T
      description: Type of SkiaLayout
    return:
      type: '{T}'
      description: The layout for chaining
    content.vb: Public Shared Function WithChildren(Of T As SkiaLayout)(view As T, ParamArray children As SkiaControl()) As T
  overload: DrawnUi.Draw.FluentExtensions.WithChildren*
  nameWithType.vb: FluentExtensions.WithChildren(Of T)(T, ParamArray SkiaControl())
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithChildren(Of T)(T, ParamArray DrawnUi.Draw.SkiaControl())
  name.vb: WithChildren(Of T)(T, ParamArray SkiaControl())
- uid: DrawnUi.Draw.FluentExtensions.WithContent``1(``0,DrawnUi.Draw.SkiaControl)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithContent``1(``0,DrawnUi.Draw.SkiaControl)
  id: WithContent``1(``0,DrawnUi.Draw.SkiaControl)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithContent<T>(T, SkiaControl)
  nameWithType: FluentExtensions.WithContent<T>(T, SkiaControl)
  fullName: DrawnUi.Draw.FluentExtensions.WithContent<T>(T, DrawnUi.Draw.SkiaControl)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithContent
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 890
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the content of a container that implements IWithContent
  example: []
  syntax:
    content: 'public static T WithContent<T>(this T view, SkiaControl child) where T : IWithContent'
    parameters:
    - id: view
      type: '{T}'
      description: The container
    - id: child
      type: DrawnUi.Draw.SkiaControl
      description: The content to set
    typeParameters:
    - id: T
      description: Type implementing IWithContent
    return:
      type: '{T}'
      description: The container for chaining
    content.vb: Public Shared Function WithContent(Of T As IWithContent)(view As T, child As SkiaControl) As T
  overload: DrawnUi.Draw.FluentExtensions.WithContent*
  nameWithType.vb: FluentExtensions.WithContent(Of T)(T, SkiaControl)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithContent(Of T)(T, DrawnUi.Draw.SkiaControl)
  name.vb: WithContent(Of T)(T, SkiaControl)
- uid: DrawnUi.Draw.FluentExtensions.WithParent``1(``0,DrawnUi.Draw.IDrawnBase)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithParent``1(``0,DrawnUi.Draw.IDrawnBase)
  id: WithParent``1(``0,DrawnUi.Draw.IDrawnBase)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithParent<T>(T, IDrawnBase)
  nameWithType: FluentExtensions.WithParent<T>(T, IDrawnBase)
  fullName: DrawnUi.Draw.FluentExtensions.WithParent<T>(T, DrawnUi.Draw.IDrawnBase)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithParent
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 903
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Adds the control to a parent and returns the control for further chaining
  example: []
  syntax:
    content: 'public static T WithParent<T>(this T view, IDrawnBase parent) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to add
    - id: parent
      type: DrawnUi.Draw.IDrawnBase
      description: The parent to add the control to
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithParent(Of T As SkiaControl)(view As T, parent As IDrawnBase) As T
  overload: DrawnUi.Draw.FluentExtensions.WithParent*
  nameWithType.vb: FluentExtensions.WithParent(Of T)(T, IDrawnBase)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithParent(Of T)(T, DrawnUi.Draw.IDrawnBase)
  name.vb: WithParent(Of T)(T, IDrawnBase)
- uid: DrawnUi.Draw.FluentExtensions.CenterX``1(``0)
  commentId: M:DrawnUi.Draw.FluentExtensions.CenterX``1(``0)
  id: CenterX``1(``0)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: CenterX<T>(T)
  nameWithType: FluentExtensions.CenterX<T>(T)
  fullName: DrawnUi.Draw.FluentExtensions.CenterX<T>(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CenterX
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 917
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the control's horizontal options to center
  example: []
  syntax:
    content: 'public static T CenterX<T>(this T view) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to center horizontally
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function CenterX(Of T As SkiaControl)(view As T) As T
  overload: DrawnUi.Draw.FluentExtensions.CenterX*
  nameWithType.vb: FluentExtensions.CenterX(Of T)(T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.CenterX(Of T)(T)
  name.vb: CenterX(Of T)(T)
- uid: DrawnUi.Draw.FluentExtensions.CenterY``1(``0)
  commentId: M:DrawnUi.Draw.FluentExtensions.CenterY``1(``0)
  id: CenterY``1(``0)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: CenterY<T>(T)
  nameWithType: FluentExtensions.CenterY<T>(T)
  fullName: DrawnUi.Draw.FluentExtensions.CenterY<T>(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CenterY
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 929
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the control's vertical options to center
  example: []
  syntax:
    content: 'public static T CenterY<T>(this T view) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to center vertically
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function CenterY(Of T As SkiaControl)(view As T) As T
  overload: DrawnUi.Draw.FluentExtensions.CenterY*
  nameWithType.vb: FluentExtensions.CenterY(Of T)(T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.CenterY(Of T)(T)
  name.vb: CenterY(Of T)(T)
- uid: DrawnUi.Draw.FluentExtensions.Fill``1(``0)
  commentId: M:DrawnUi.Draw.FluentExtensions.Fill``1(``0)
  id: Fill``1(``0)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: Fill<T>(T)
  nameWithType: FluentExtensions.Fill<T>(T)
  fullName: DrawnUi.Draw.FluentExtensions.Fill<T>(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Fill
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 941
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Fills in both directions
  example: []
  syntax:
    content: 'public static T Fill<T>(this T view) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: ''
    typeParameters:
    - id: T
      description: ''
    return:
      type: '{T}'
      description: ''
    content.vb: Public Shared Function Fill(Of T As SkiaControl)(view As T) As T
  overload: DrawnUi.Draw.FluentExtensions.Fill*
  nameWithType.vb: FluentExtensions.Fill(Of T)(T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.Fill(Of T)(T)
  name.vb: Fill(Of T)(T)
- uid: DrawnUi.Draw.FluentExtensions.FillX``1(``0)
  commentId: M:DrawnUi.Draw.FluentExtensions.FillX``1(``0)
  id: FillX``1(``0)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: FillX<T>(T)
  nameWithType: FluentExtensions.FillX<T>(T)
  fullName: DrawnUi.Draw.FluentExtensions.FillX<T>(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FillX
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 954
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Fills horizontally
  example: []
  syntax:
    content: 'public static T FillX<T>(this T view) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: ''
    typeParameters:
    - id: T
      description: ''
    return:
      type: '{T}'
      description: ''
    content.vb: Public Shared Function FillX(Of T As SkiaControl)(view As T) As T
  overload: DrawnUi.Draw.FluentExtensions.FillX*
  nameWithType.vb: FluentExtensions.FillX(Of T)(T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.FillX(Of T)(T)
  name.vb: FillX(Of T)(T)
- uid: DrawnUi.Draw.FluentExtensions.EndX``1(``0)
  commentId: M:DrawnUi.Draw.FluentExtensions.EndX``1(``0)
  id: EndX``1(``0)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: EndX<T>(T)
  nameWithType: FluentExtensions.EndX<T>(T)
  fullName: DrawnUi.Draw.FluentExtensions.EndX<T>(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: EndX
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 960
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public static T EndX<T>(this T view) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function EndX(Of T As SkiaControl)(view As T) As T
  overload: DrawnUi.Draw.FluentExtensions.EndX*
  nameWithType.vb: FluentExtensions.EndX(Of T)(T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.EndX(Of T)(T)
  name.vb: EndX(Of T)(T)
- uid: DrawnUi.Draw.FluentExtensions.EndY``1(``0)
  commentId: M:DrawnUi.Draw.FluentExtensions.EndY``1(``0)
  id: EndY``1(``0)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: EndY<T>(T)
  nameWithType: FluentExtensions.EndY<T>(T)
  fullName: DrawnUi.Draw.FluentExtensions.EndY<T>(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: EndY
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 966
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public static T EndY<T>(this T view) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function EndY(Of T As SkiaControl)(view As T) As T
  overload: DrawnUi.Draw.FluentExtensions.EndY*
  nameWithType.vb: FluentExtensions.EndY(Of T)(T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.EndY(Of T)(T)
  name.vb: EndY(Of T)(T)
- uid: DrawnUi.Draw.FluentExtensions.StartX``1(``0)
  commentId: M:DrawnUi.Draw.FluentExtensions.StartX``1(``0)
  id: StartX``1(``0)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: StartX<T>(T)
  nameWithType: FluentExtensions.StartX<T>(T)
  fullName: DrawnUi.Draw.FluentExtensions.StartX<T>(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartX
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 972
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public static T StartX<T>(this T view) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function StartX(Of T As SkiaControl)(view As T) As T
  overload: DrawnUi.Draw.FluentExtensions.StartX*
  nameWithType.vb: FluentExtensions.StartX(Of T)(T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.StartX(Of T)(T)
  name.vb: StartX(Of T)(T)
- uid: DrawnUi.Draw.FluentExtensions.StartY``1(``0)
  commentId: M:DrawnUi.Draw.FluentExtensions.StartY``1(``0)
  id: StartY``1(``0)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: StartY<T>(T)
  nameWithType: FluentExtensions.StartY<T>(T)
  fullName: DrawnUi.Draw.FluentExtensions.StartY<T>(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartY
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 978
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public static T StartY<T>(this T view) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function StartY(Of T As SkiaControl)(view As T) As T
  overload: DrawnUi.Draw.FluentExtensions.StartY*
  nameWithType.vb: FluentExtensions.StartY(Of T)(T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.StartY(Of T)(T)
  name.vb: StartY(Of T)(T)
- uid: DrawnUi.Draw.FluentExtensions.FillY``1(``0)
  commentId: M:DrawnUi.Draw.FluentExtensions.FillY``1(``0)
  id: FillY``1(``0)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: FillY<T>(T)
  nameWithType: FluentExtensions.FillY<T>(T)
  fullName: DrawnUi.Draw.FluentExtensions.FillY<T>(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FillY
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 991
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Fills vertically
  example: []
  syntax:
    content: 'public static T FillY<T>(this T view) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: ''
    typeParameters:
    - id: T
      description: ''
    return:
      type: '{T}'
      description: ''
    content.vb: Public Shared Function FillY(Of T As SkiaControl)(view As T) As T
  overload: DrawnUi.Draw.FluentExtensions.FillY*
  nameWithType.vb: FluentExtensions.FillY(Of T)(T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.FillY(Of T)(T)
  name.vb: FillY(Of T)(T)
- uid: DrawnUi.Draw.FluentExtensions.Center``1(``0)
  commentId: M:DrawnUi.Draw.FluentExtensions.Center``1(``0)
  id: Center``1(``0)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: Center<T>(T)
  nameWithType: FluentExtensions.Center<T>(T)
  fullName: DrawnUi.Draw.FluentExtensions.Center<T>(T)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Center
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1003
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Centers the control both horizontally and vertically
  example: []
  syntax:
    content: 'public static T Center<T>(this T view) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to center
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function Center(Of T As SkiaControl)(view As T) As T
  overload: DrawnUi.Draw.FluentExtensions.Center*
  nameWithType.vb: FluentExtensions.Center(Of T)(T)
  fullName.vb: DrawnUi.Draw.FluentExtensions.Center(Of T)(T)
  name.vb: Center(Of T)(T)
- uid: DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,System.Double)
  id: WithMargin``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithMargin<T>(T, double)
  nameWithType: FluentExtensions.WithMargin<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithMargin<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithMargin
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1019
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the margin for the control
  example: []
  syntax:
    content: 'public static T WithMargin<T>(this T view, double uniformMargin) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set margin for
    - id: uniformMargin
      type: System.Double
      description: The uniform margin to apply to all sides
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithMargin(Of T As SkiaControl)(view As T, uniformMargin As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithMargin*
  nameWithType.vb: FluentExtensions.WithMargin(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithMargin(Of T)(T, Double)
  name.vb: WithMargin(Of T)(T, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,System.Double,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,System.Double,System.Double)
  id: WithMargin``1(``0,System.Double,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithMargin<T>(T, double, double)
  nameWithType: FluentExtensions.WithMargin<T>(T, double, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithMargin<T>(T, double, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithMargin
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1033
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the margin for the control
  example: []
  syntax:
    content: 'public static T WithMargin<T>(this T view, double horizontal, double vertical) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set margin for
    - id: horizontal
      type: System.Double
      description: The left and right margin
    - id: vertical
      type: System.Double
      description: The top and bottom margin
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithMargin(Of T As SkiaControl)(view As T, horizontal As Double, vertical As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithMargin*
  nameWithType.vb: FluentExtensions.WithMargin(Of T)(T, Double, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithMargin(Of T)(T, Double, Double)
  name.vb: WithMargin(Of T)(T, Double, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,System.Double,System.Double,System.Double,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,System.Double,System.Double,System.Double,System.Double)
  id: WithMargin``1(``0,System.Double,System.Double,System.Double,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithMargin<T>(T, double, double, double, double)
  nameWithType: FluentExtensions.WithMargin<T>(T, double, double, double, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithMargin<T>(T, double, double, double, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithMargin
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1049
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the margin for the control
  example: []
  syntax:
    content: 'public static T WithMargin<T>(this T view, double left, double top, double right, double bottom) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set margin for
    - id: left
      type: System.Double
      description: The left margin
    - id: top
      type: System.Double
      description: The top margin
    - id: right
      type: System.Double
      description: The right margin
    - id: bottom
      type: System.Double
      description: The bottom margin
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithMargin(Of T As SkiaControl)(view As T, left As Double, top As Double, right As Double, bottom As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithMargin*
  nameWithType.vb: FluentExtensions.WithMargin(Of T)(T, Double, Double, Double, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithMargin(Of T)(T, Double, Double, Double, Double)
  name.vb: WithMargin(Of T)(T, Double, Double, Double, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double)
  id: WithPadding``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithPadding<T>(T, double)
  nameWithType: FluentExtensions.WithPadding<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithPadding<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithPadding
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1063
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the padding for a layout control
  example: []
  syntax:
    content: 'public static T WithPadding<T>(this T view, double uniformPadding) where T : SkiaLayout'
    parameters:
    - id: view
      type: '{T}'
      description: The layout to set padding for
    - id: uniformPadding
      type: System.Double
      description: The uniform padding to apply to all sides
    typeParameters:
    - id: T
      description: Type of SkiaLayout
    return:
      type: '{T}'
      description: The layout for chaining
    content.vb: Public Shared Function WithPadding(Of T As SkiaLayout)(view As T, uniformPadding As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithPadding*
  nameWithType.vb: FluentExtensions.WithPadding(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithPadding(Of T)(T, Double)
  name.vb: WithPadding(Of T)(T, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double,System.Double)
  id: WithPadding``1(``0,System.Double,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithPadding<T>(T, double, double)
  nameWithType: FluentExtensions.WithPadding<T>(T, double, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithPadding<T>(T, double, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithPadding
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1077
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the padding for a layout control
  example: []
  syntax:
    content: 'public static T WithPadding<T>(this T view, double horizontal, double vertical) where T : SkiaLayout'
    parameters:
    - id: view
      type: '{T}'
      description: The layout to set padding for
    - id: horizontal
      type: System.Double
      description: The left and right padding
    - id: vertical
      type: System.Double
      description: The top and bottom padding
    typeParameters:
    - id: T
      description: Type of SkiaLayout
    return:
      type: '{T}'
      description: The layout for chaining
    content.vb: Public Shared Function WithPadding(Of T As SkiaLayout)(view As T, horizontal As Double, vertical As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithPadding*
  nameWithType.vb: FluentExtensions.WithPadding(Of T)(T, Double, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithPadding(Of T)(T, Double, Double)
  name.vb: WithPadding(Of T)(T, Double, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double,System.Double,System.Double,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithPadding``1(``0,System.Double,System.Double,System.Double,System.Double)
  id: WithPadding``1(``0,System.Double,System.Double,System.Double,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithPadding<T>(T, double, double, double, double)
  nameWithType: FluentExtensions.WithPadding<T>(T, double, double, double, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithPadding<T>(T, double, double, double, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithPadding
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1093
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the padding for a layout control
  example: []
  syntax:
    content: 'public static T WithPadding<T>(this T view, double left, double top, double right, double bottom) where T : SkiaLayout'
    parameters:
    - id: view
      type: '{T}'
      description: The layout to set padding for
    - id: left
      type: System.Double
      description: The left padding
    - id: top
      type: System.Double
      description: The top padding
    - id: right
      type: System.Double
      description: The right padding
    - id: bottom
      type: System.Double
      description: The bottom padding
    typeParameters:
    - id: T
      description: Type of SkiaLayout
    return:
      type: '{T}'
      description: The layout for chaining
    content.vb: Public Shared Function WithPadding(Of T As SkiaLayout)(view As T, left As Double, top As Double, right As Double, bottom As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithPadding*
  nameWithType.vb: FluentExtensions.WithPadding(Of T)(T, Double, Double, Double, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithPadding(Of T)(T, Double, Double, Double, Double)
  name.vb: WithPadding(Of T)(T, Double, Double, Double, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithTag``1(``0,System.String)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithTag``1(``0,System.String)
  id: WithTag``1(``0,System.String)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithTag<T>(T, string)
  nameWithType: FluentExtensions.WithTag<T>(T, string)
  fullName: DrawnUi.Draw.FluentExtensions.WithTag<T>(T, string)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithTag
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1107
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Tag property for the control
  example: []
  syntax:
    content: 'public static T WithTag<T>(this T view, string tag) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set the Tag for
    - id: tag
      type: System.String
      description: The tag value
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithTag(Of T As SkiaControl)(view As T, tag As String) As T
  overload: DrawnUi.Draw.FluentExtensions.WithTag*
  nameWithType.vb: FluentExtensions.WithTag(Of T)(T, String)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithTag(Of T)(T, String)
  name.vb: WithTag(Of T)(T, String)
- uid: DrawnUi.Draw.FluentExtensions.WithCache``1(``0,DrawnUi.Draw.SkiaCacheType)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithCache``1(``0,DrawnUi.Draw.SkiaCacheType)
  id: WithCache``1(``0,DrawnUi.Draw.SkiaCacheType)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithCache<T>(T, SkiaCacheType)
  nameWithType: FluentExtensions.WithCache<T>(T, SkiaCacheType)
  fullName: DrawnUi.Draw.FluentExtensions.WithCache<T>(T, DrawnUi.Draw.SkiaCacheType)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithCache
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1124
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the cache type for the control
  example: []
  syntax:
    content: 'public static T WithCache<T>(this T view, SkiaCacheType cacheType) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set cache for
    - id: cacheType
      type: DrawnUi.Draw.SkiaCacheType
      description: The cache type
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithCache(Of T As SkiaControl)(view As T, cacheType As SkiaCacheType) As T
  overload: DrawnUi.Draw.FluentExtensions.WithCache*
  nameWithType.vb: FluentExtensions.WithCache(Of T)(T, SkiaCacheType)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithCache(Of T)(T, DrawnUi.Draw.SkiaCacheType)
  name.vb: WithCache(Of T)(T, SkiaCacheType)
- uid: DrawnUi.Draw.FluentExtensions.WithBackgroundColor``1(``0,Microsoft.Maui.Graphics.Color)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithBackgroundColor``1(``0,Microsoft.Maui.Graphics.Color)
  id: WithBackgroundColor``1(``0,Microsoft.Maui.Graphics.Color)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithBackgroundColor<T>(T, Color)
  nameWithType: FluentExtensions.WithBackgroundColor<T>(T, Color)
  fullName: DrawnUi.Draw.FluentExtensions.WithBackgroundColor<T>(T, Microsoft.Maui.Graphics.Color)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithBackgroundColor
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1137
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the background color for the control
  example: []
  syntax:
    content: 'public static T WithBackgroundColor<T>(this T view, Color color) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set background color for
    - id: color
      type: Microsoft.Maui.Graphics.Color
      description: The background color
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithBackgroundColor(Of T As SkiaControl)(view As T, color As Color) As T
  overload: DrawnUi.Draw.FluentExtensions.WithBackgroundColor*
  nameWithType.vb: FluentExtensions.WithBackgroundColor(Of T)(T, Color)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithBackgroundColor(Of T)(T, Microsoft.Maui.Graphics.Color)
  name.vb: WithBackgroundColor(Of T)(T, Color)
- uid: DrawnUi.Draw.FluentExtensions.WithHorizontalOptions``1(``0,Microsoft.Maui.Controls.LayoutOptions)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithHorizontalOptions``1(``0,Microsoft.Maui.Controls.LayoutOptions)
  id: WithHorizontalOptions``1(``0,Microsoft.Maui.Controls.LayoutOptions)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithHorizontalOptions<T>(T, LayoutOptions)
  nameWithType: FluentExtensions.WithHorizontalOptions<T>(T, LayoutOptions)
  fullName: DrawnUi.Draw.FluentExtensions.WithHorizontalOptions<T>(T, Microsoft.Maui.Controls.LayoutOptions)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithHorizontalOptions
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1150
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the horizontal options for the control
  example: []
  syntax:
    content: 'public static T WithHorizontalOptions<T>(this T view, LayoutOptions options) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set horizontal options for
    - id: options
      type: Microsoft.Maui.Controls.LayoutOptions
      description: The horizontal layout options
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithHorizontalOptions(Of T As SkiaControl)(view As T, options As LayoutOptions) As T
  overload: DrawnUi.Draw.FluentExtensions.WithHorizontalOptions*
  nameWithType.vb: FluentExtensions.WithHorizontalOptions(Of T)(T, LayoutOptions)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithHorizontalOptions(Of T)(T, Microsoft.Maui.Controls.LayoutOptions)
  name.vb: WithHorizontalOptions(Of T)(T, LayoutOptions)
- uid: DrawnUi.Draw.FluentExtensions.WithVerticalOptions``1(``0,Microsoft.Maui.Controls.LayoutOptions)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithVerticalOptions``1(``0,Microsoft.Maui.Controls.LayoutOptions)
  id: WithVerticalOptions``1(``0,Microsoft.Maui.Controls.LayoutOptions)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithVerticalOptions<T>(T, LayoutOptions)
  nameWithType: FluentExtensions.WithVerticalOptions<T>(T, LayoutOptions)
  fullName: DrawnUi.Draw.FluentExtensions.WithVerticalOptions<T>(T, Microsoft.Maui.Controls.LayoutOptions)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithVerticalOptions
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1163
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the vertical options for the control
  example: []
  syntax:
    content: 'public static T WithVerticalOptions<T>(this T view, LayoutOptions options) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set vertical options for
    - id: options
      type: Microsoft.Maui.Controls.LayoutOptions
      description: The vertical layout options
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithVerticalOptions(Of T As SkiaControl)(view As T, options As LayoutOptions) As T
  overload: DrawnUi.Draw.FluentExtensions.WithVerticalOptions*
  nameWithType.vb: FluentExtensions.WithVerticalOptions(Of T)(T, LayoutOptions)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithVerticalOptions(Of T)(T, Microsoft.Maui.Controls.LayoutOptions)
  name.vb: WithVerticalOptions(Of T)(T, LayoutOptions)
- uid: DrawnUi.Draw.FluentExtensions.WithHeight``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithHeight``1(``0,System.Double)
  id: WithHeight``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithHeight<T>(T, double)
  nameWithType: FluentExtensions.WithHeight<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithHeight<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithHeight
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1176
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the height request for the control
  example: []
  syntax:
    content: 'public static T WithHeight<T>(this T view, double height) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set height for
    - id: height
      type: System.Double
      description: The height request
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithHeight(Of T As SkiaControl)(view As T, height As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithHeight*
  nameWithType.vb: FluentExtensions.WithHeight(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithHeight(Of T)(T, Double)
  name.vb: WithHeight(Of T)(T, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithWidth``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithWidth``1(``0,System.Double)
  id: WithWidth``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithWidth<T>(T, double)
  nameWithType: FluentExtensions.WithWidth<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithWidth<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithWidth
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1189
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the width request for the control
  example: []
  syntax:
    content: 'public static T WithWidth<T>(this T view, double width) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set width for
    - id: width
      type: System.Double
      description: The width request
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithWidth(Of T As SkiaControl)(view As T, width As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithWidth*
  nameWithType.vb: FluentExtensions.WithWidth(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithWidth(Of T)(T, Double)
  name.vb: WithWidth(Of T)(T, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,Microsoft.Maui.Thickness)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithMargin``1(``0,Microsoft.Maui.Thickness)
  id: WithMargin``1(``0,Microsoft.Maui.Thickness)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithMargin<T>(T, Thickness)
  nameWithType: FluentExtensions.WithMargin<T>(T, Thickness)
  fullName: DrawnUi.Draw.FluentExtensions.WithMargin<T>(T, Microsoft.Maui.Thickness)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithMargin
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1202
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the margin for the control
  example: []
  syntax:
    content: 'public static T WithMargin<T>(this T view, Thickness margin) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
      description: The control to set margin for
    - id: margin
      type: Microsoft.Maui.Thickness
      description: The margin thickness
    typeParameters:
    - id: T
      description: Type of SkiaControl
    return:
      type: '{T}'
      description: The control for chaining
    content.vb: Public Shared Function WithMargin(Of T As SkiaControl)(view As T, margin As Thickness) As T
  overload: DrawnUi.Draw.FluentExtensions.WithMargin*
  nameWithType.vb: FluentExtensions.WithMargin(Of T)(T, Thickness)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithMargin(Of T)(T, Microsoft.Maui.Thickness)
  name.vb: WithMargin(Of T)(T, Thickness)
- uid: DrawnUi.Draw.FluentExtensions.WithVisibility``1(``0,System.Boolean)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithVisibility``1(``0,System.Boolean)
  id: WithVisibility``1(``0,System.Boolean)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithVisibility<T>(T, bool)
  nameWithType: FluentExtensions.WithVisibility<T>(T, bool)
  fullName: DrawnUi.Draw.FluentExtensions.WithVisibility<T>(T, bool)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithVisibility
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1208
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public static T WithVisibility<T>(this T view, bool value) where T : SkiaControl'
    parameters:
    - id: view
      type: '{T}'
    - id: value
      type: System.Boolean
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithVisibility(Of T As SkiaControl)(view As T, value As Boolean) As T
  overload: DrawnUi.Draw.FluentExtensions.WithVisibility*
  nameWithType.vb: FluentExtensions.WithVisibility(Of T)(T, Boolean)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithVisibility(Of T)(T, Boolean)
  name.vb: WithVisibility(Of T)(T, Boolean)
- uid: DrawnUi.Draw.FluentExtensions.WithShapeType``1(``0,DrawnUi.Draw.ShapeType)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithShapeType``1(``0,DrawnUi.Draw.ShapeType)
  id: WithShapeType``1(``0,DrawnUi.Draw.ShapeType)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithShapeType<T>(T, ShapeType)
  nameWithType: FluentExtensions.WithShapeType<T>(T, ShapeType)
  fullName: DrawnUi.Draw.FluentExtensions.WithShapeType<T>(T, DrawnUi.Draw.ShapeType)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithShapeType
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1225
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the shape type for SkiaShape
  example: []
  syntax:
    content: 'public static T WithShapeType<T>(this T shape, ShapeType shapeType) where T : SkiaShape'
    parameters:
    - id: shape
      type: '{T}'
      description: The shape to set type for
    - id: shapeType
      type: DrawnUi.Draw.ShapeType
      description: The shape type
    typeParameters:
    - id: T
      description: Type of SkiaShape
    return:
      type: '{T}'
      description: The shape for chaining
    content.vb: Public Shared Function WithShapeType(Of T As SkiaShape)(shape As T, shapeType As ShapeType) As T
  overload: DrawnUi.Draw.FluentExtensions.WithShapeType*
  nameWithType.vb: FluentExtensions.WithShapeType(Of T)(T, ShapeType)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithShapeType(Of T)(T, DrawnUi.Draw.ShapeType)
  name.vb: WithShapeType(Of T)(T, ShapeType)
- uid: DrawnUi.Draw.FluentExtensions.Shape``1(``0,DrawnUi.Draw.ShapeType)
  commentId: M:DrawnUi.Draw.FluentExtensions.Shape``1(``0,DrawnUi.Draw.ShapeType)
  id: Shape``1(``0,DrawnUi.Draw.ShapeType)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: Shape<T>(T, ShapeType)
  nameWithType: FluentExtensions.Shape<T>(T, ShapeType)
  fullName: DrawnUi.Draw.FluentExtensions.Shape<T>(T, DrawnUi.Draw.ShapeType)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Shape
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1238
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the shape type for SkiaShape (shorter alias)
  example: []
  syntax:
    content: 'public static T Shape<T>(this T shape, ShapeType shapeType) where T : SkiaShape'
    parameters:
    - id: shape
      type: '{T}'
      description: The shape to set type for
    - id: shapeType
      type: DrawnUi.Draw.ShapeType
      description: The shape type
    typeParameters:
    - id: T
      description: Type of SkiaShape
    return:
      type: '{T}'
      description: The shape for chaining
    content.vb: Public Shared Function Shape(Of T As SkiaShape)(shape As T, shapeType As ShapeType) As T
  overload: DrawnUi.Draw.FluentExtensions.Shape*
  nameWithType.vb: FluentExtensions.Shape(Of T)(T, ShapeType)
  fullName.vb: DrawnUi.Draw.FluentExtensions.Shape(Of T)(T, DrawnUi.Draw.ShapeType)
  name.vb: Shape(Of T)(T, ShapeType)
- uid: DrawnUi.Draw.FluentExtensions.WithAspect``1(``0,DrawnUi.Draw.TransformAspect)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithAspect``1(``0,DrawnUi.Draw.TransformAspect)
  id: WithAspect``1(``0,DrawnUi.Draw.TransformAspect)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithAspect<T>(T, TransformAspect)
  nameWithType: FluentExtensions.WithAspect<T>(T, TransformAspect)
  fullName: DrawnUi.Draw.FluentExtensions.WithAspect<T>(T, DrawnUi.Draw.TransformAspect)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithAspect
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1255
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the aspect for SkiaImage
  example: []
  syntax:
    content: 'public static T WithAspect<T>(this T image, TransformAspect aspect) where T : SkiaImage'
    parameters:
    - id: image
      type: '{T}'
      description: The image to set aspect for
    - id: aspect
      type: DrawnUi.Draw.TransformAspect
      description: The transform aspect
    typeParameters:
    - id: T
      description: Type of SkiaImage
    return:
      type: '{T}'
      description: The image for chaining
    content.vb: Public Shared Function WithAspect(Of T As SkiaImage)(image As T, aspect As TransformAspect) As T
  overload: DrawnUi.Draw.FluentExtensions.WithAspect*
  nameWithType.vb: FluentExtensions.WithAspect(Of T)(T, TransformAspect)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithAspect(Of T)(T, DrawnUi.Draw.TransformAspect)
  name.vb: WithAspect(Of T)(T, TransformAspect)
- uid: DrawnUi.Draw.FluentExtensions.WithFontSize``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithFontSize``1(``0,System.Double)
  id: WithFontSize``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithFontSize<T>(T, double)
  nameWithType: FluentExtensions.WithFontSize<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithFontSize<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithFontSize
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1272
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the font size for SkiaLabel
  example: []
  syntax:
    content: 'public static T WithFontSize<T>(this T label, double fontSize) where T : SkiaLabel'
    parameters:
    - id: label
      type: '{T}'
      description: The label to set font size for
    - id: fontSize
      type: System.Double
      description: The font size
    typeParameters:
    - id: T
      description: Type of SkiaLabel
    return:
      type: '{T}'
      description: The label for chaining
    content.vb: Public Shared Function WithFontSize(Of T As SkiaLabel)(label As T, fontSize As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithFontSize*
  nameWithType.vb: FluentExtensions.WithFontSize(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithFontSize(Of T)(T, Double)
  name.vb: WithFontSize(Of T)(T, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithTextColor``1(``0,Microsoft.Maui.Graphics.Color)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithTextColor``1(``0,Microsoft.Maui.Graphics.Color)
  id: WithTextColor``1(``0,Microsoft.Maui.Graphics.Color)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithTextColor<T>(T, Color)
  nameWithType: FluentExtensions.WithTextColor<T>(T, Color)
  fullName: DrawnUi.Draw.FluentExtensions.WithTextColor<T>(T, Microsoft.Maui.Graphics.Color)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithTextColor
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1285
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the text color for SkiaLabel
  example: []
  syntax:
    content: 'public static T WithTextColor<T>(this T label, Color color) where T : SkiaLabel'
    parameters:
    - id: label
      type: '{T}'
      description: The label to set text color for
    - id: color
      type: Microsoft.Maui.Graphics.Color
      description: The text color
    typeParameters:
    - id: T
      description: Type of SkiaLabel
    return:
      type: '{T}'
      description: The label for chaining
    content.vb: Public Shared Function WithTextColor(Of T As SkiaLabel)(label As T, color As Color) As T
  overload: DrawnUi.Draw.FluentExtensions.WithTextColor*
  nameWithType.vb: FluentExtensions.WithTextColor(Of T)(T, Color)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithTextColor(Of T)(T, Microsoft.Maui.Graphics.Color)
  name.vb: WithTextColor(Of T)(T, Color)
- uid: DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment``1(``0,DrawnUi.Draw.DrawTextAlignment)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment``1(``0,DrawnUi.Draw.DrawTextAlignment)
  id: WithHorizontalTextAlignment``1(``0,DrawnUi.Draw.DrawTextAlignment)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithHorizontalTextAlignment<T>(T, DrawTextAlignment)
  nameWithType: FluentExtensions.WithHorizontalTextAlignment<T>(T, DrawTextAlignment)
  fullName: DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment<T>(T, DrawnUi.Draw.DrawTextAlignment)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithHorizontalTextAlignment
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1298
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the horizontal text alignment for SkiaLabel
  example: []
  syntax:
    content: 'public static T WithHorizontalTextAlignment<T>(this T label, DrawTextAlignment alignment) where T : SkiaLabel'
    parameters:
    - id: label
      type: '{T}'
      description: The label to set alignment for
    - id: alignment
      type: DrawnUi.Draw.DrawTextAlignment
      description: The horizontal text alignment
    typeParameters:
    - id: T
      description: Type of SkiaLabel
    return:
      type: '{T}'
      description: The label for chaining
    content.vb: Public Shared Function WithHorizontalTextAlignment(Of T As SkiaLabel)(label As T, alignment As DrawTextAlignment) As T
  overload: DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment*
  nameWithType.vb: FluentExtensions.WithHorizontalTextAlignment(Of T)(T, DrawTextAlignment)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment(Of T)(T, DrawnUi.Draw.DrawTextAlignment)
  name.vb: WithHorizontalTextAlignment(Of T)(T, DrawTextAlignment)
- uid: DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEntry,System.Action{DrawnUi.Controls.SkiaMauiEntry,System.String})
  commentId: M:DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEntry,System.Action{DrawnUi.Controls.SkiaMauiEntry,System.String})
  id: OnTextChanged(DrawnUi.Controls.SkiaMauiEntry,System.Action{DrawnUi.Controls.SkiaMauiEntry,System.String})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: OnTextChanged(SkiaMauiEntry, Action<SkiaMauiEntry, string>)
  nameWithType: FluentExtensions.OnTextChanged(SkiaMauiEntry, Action<SkiaMauiEntry, string>)
  fullName: DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEntry, System.Action<DrawnUi.Controls.SkiaMauiEntry, string>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnTextChanged
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1314
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Registers a callback to be executed when the text of a SkiaMauiEntry changes.
  example: []
  syntax:
    content: public static SkiaMauiEntry OnTextChanged(this SkiaMauiEntry control, Action<SkiaMauiEntry, string> action)
    parameters:
    - id: control
      type: DrawnUi.Controls.SkiaMauiEntry
      description: The entry control to observe
    - id: action
      type: System.Action{DrawnUi.Controls.SkiaMauiEntry,System.String}
      description: Callback receiving the entry and new text
    return:
      type: DrawnUi.Controls.SkiaMauiEntry
      description: The entry control for chaining
    content.vb: Public Shared Function OnTextChanged(control As SkiaMauiEntry, action As Action(Of SkiaMauiEntry, String)) As SkiaMauiEntry
  overload: DrawnUi.Draw.FluentExtensions.OnTextChanged*
  nameWithType.vb: FluentExtensions.OnTextChanged(SkiaMauiEntry, Action(Of SkiaMauiEntry, String))
  fullName.vb: DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEntry, System.Action(Of DrawnUi.Controls.SkiaMauiEntry, String))
  name.vb: OnTextChanged(SkiaMauiEntry, Action(Of SkiaMauiEntry, String))
- uid: DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEditor,System.Action{DrawnUi.Controls.SkiaMauiEditor,System.String})
  commentId: M:DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEditor,System.Action{DrawnUi.Controls.SkiaMauiEditor,System.String})
  id: OnTextChanged(DrawnUi.Controls.SkiaMauiEditor,System.Action{DrawnUi.Controls.SkiaMauiEditor,System.String})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: OnTextChanged(SkiaMauiEditor, Action<SkiaMauiEditor, string>)
  nameWithType: FluentExtensions.OnTextChanged(SkiaMauiEditor, Action<SkiaMauiEditor, string>)
  fullName: DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEditor, System.Action<DrawnUi.Controls.SkiaMauiEditor, string>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnTextChanged
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1327
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Registers a callback to be executed when the text of a SkiaMauiEditor changes.
  example: []
  syntax:
    content: public static SkiaMauiEditor OnTextChanged(this SkiaMauiEditor control, Action<SkiaMauiEditor, string> action)
    parameters:
    - id: control
      type: DrawnUi.Controls.SkiaMauiEditor
      description: The editor control to observe
    - id: action
      type: System.Action{DrawnUi.Controls.SkiaMauiEditor,System.String}
      description: Callback receiving the editor and new text
    return:
      type: DrawnUi.Controls.SkiaMauiEditor
      description: The editor control for chaining
    content.vb: Public Shared Function OnTextChanged(control As SkiaMauiEditor, action As Action(Of SkiaMauiEditor, String)) As SkiaMauiEditor
  overload: DrawnUi.Draw.FluentExtensions.OnTextChanged*
  nameWithType.vb: FluentExtensions.OnTextChanged(SkiaMauiEditor, Action(Of SkiaMauiEditor, String))
  fullName.vb: DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Controls.SkiaMauiEditor, System.Action(Of DrawnUi.Controls.SkiaMauiEditor, String))
  name.vb: OnTextChanged(SkiaMauiEditor, Action(Of SkiaMauiEditor, String))
- uid: DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Draw.SkiaLabel,System.Action{DrawnUi.Draw.SkiaLabel,System.String})
  commentId: M:DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Draw.SkiaLabel,System.Action{DrawnUi.Draw.SkiaLabel,System.String})
  id: OnTextChanged(DrawnUi.Draw.SkiaLabel,System.Action{DrawnUi.Draw.SkiaLabel,System.String})
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: OnTextChanged(SkiaLabel, Action<SkiaLabel, string>)
  nameWithType: FluentExtensions.OnTextChanged(SkiaLabel, Action<SkiaLabel, string>)
  fullName: DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Draw.SkiaLabel, System.Action<DrawnUi.Draw.SkiaLabel, string>)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnTextChanged
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1342
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Registers a callback to be executed when the text of a SkiaLabel changes.
  example: []
  syntax:
    content: public static SkiaLabel OnTextChanged(this SkiaLabel control, Action<SkiaLabel, string> action)
    parameters:
    - id: control
      type: DrawnUi.Draw.SkiaLabel
      description: The label control to observe
    - id: action
      type: System.Action{DrawnUi.Draw.SkiaLabel,System.String}
      description: Callback receiving the label and new text
    return:
      type: DrawnUi.Draw.SkiaLabel
      description: The label control for chaining
    content.vb: Public Shared Function OnTextChanged(control As SkiaLabel, action As Action(Of SkiaLabel, String)) As SkiaLabel
  overload: DrawnUi.Draw.FluentExtensions.OnTextChanged*
  nameWithType.vb: FluentExtensions.OnTextChanged(SkiaLabel, Action(Of SkiaLabel, String))
  fullName.vb: DrawnUi.Draw.FluentExtensions.OnTextChanged(DrawnUi.Draw.SkiaLabel, System.Action(Of DrawnUi.Draw.SkiaLabel, String))
  name.vb: OnTextChanged(SkiaLabel, Action(Of SkiaLabel, String))
- uid: DrawnUi.Draw.FluentExtensions.WithText``1(``0,System.String)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithText``1(``0,System.String)
  id: WithText``1(``0,System.String)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithText<T>(T, string)
  nameWithType: FluentExtensions.WithText<T>(T, string)
  fullName: DrawnUi.Draw.FluentExtensions.WithText<T>(T, string)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithText
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1359
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Text property for SkiaLabel
  example: []
  syntax:
    content: 'public static T WithText<T>(this T label, string text) where T : SkiaLabel'
    parameters:
    - id: label
      type: '{T}'
    - id: text
      type: System.String
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithText(Of T As SkiaLabel)(label As T, text As String) As T
  overload: DrawnUi.Draw.FluentExtensions.WithText*
  nameWithType.vb: FluentExtensions.WithText(Of T)(T, String)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithText(Of T)(T, String)
  name.vb: WithText(Of T)(T, String)
- uid: DrawnUi.Draw.FluentExtensions.WithItemsSource``1(``0,System.Collections.IList)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithItemsSource``1(``0,System.Collections.IList)
  id: WithItemsSource``1(``0,System.Collections.IList)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithItemsSource<T>(T, IList)
  nameWithType: FluentExtensions.WithItemsSource<T>(T, IList)
  fullName: DrawnUi.Draw.FluentExtensions.WithItemsSource<T>(T, System.Collections.IList)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithItemsSource
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1368
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the ItemsSource property for controls that have it
  example: []
  syntax:
    content: 'public static T WithItemsSource<T>(this T control, IList itemsSource) where T : SkiaLayout'
    parameters:
    - id: control
      type: '{T}'
    - id: itemsSource
      type: System.Collections.IList
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithItemsSource(Of T As SkiaLayout)(control As T, itemsSource As IList) As T
  overload: DrawnUi.Draw.FluentExtensions.WithItemsSource*
  nameWithType.vb: FluentExtensions.WithItemsSource(Of T)(T, IList)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithItemsSource(Of T)(T, System.Collections.IList)
  name.vb: WithItemsSource(Of T)(T, IList)
- uid: DrawnUi.Draw.FluentExtensions.WithEnabled``1(``0,System.Boolean)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithEnabled``1(``0,System.Boolean)
  id: WithEnabled``1(``0,System.Boolean)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithEnabled<T>(T, bool)
  nameWithType: FluentExtensions.WithEnabled<T>(T, bool)
  fullName: DrawnUi.Draw.FluentExtensions.WithEnabled<T>(T, bool)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithEnabled
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1380
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the IsEnabled property
  example: []
  syntax:
    content: 'public static T WithEnabled<T>(this T control, bool enabled) where T : SkiaControl'
    parameters:
    - id: control
      type: '{T}'
    - id: enabled
      type: System.Boolean
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithEnabled(Of T As SkiaControl)(control As T, enabled As Boolean) As T
  overload: DrawnUi.Draw.FluentExtensions.WithEnabled*
  nameWithType.vb: FluentExtensions.WithEnabled(Of T)(T, Boolean)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithEnabled(Of T)(T, Boolean)
  name.vb: WithEnabled(Of T)(T, Boolean)
- uid: DrawnUi.Draw.FluentExtensions.WithOpacity``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithOpacity``1(``0,System.Double)
  id: WithOpacity``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithOpacity<T>(T, double)
  nameWithType: FluentExtensions.WithOpacity<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithOpacity<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithOpacity
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1389
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Opacity property
  example: []
  syntax:
    content: 'public static T WithOpacity<T>(this T control, double opacity) where T : SkiaControl'
    parameters:
    - id: control
      type: '{T}'
    - id: opacity
      type: System.Double
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithOpacity(Of T As SkiaControl)(control As T, opacity As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithOpacity*
  nameWithType.vb: FluentExtensions.WithOpacity(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithOpacity(Of T)(T, Double)
  name.vb: WithOpacity(Of T)(T, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithRotation``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithRotation``1(``0,System.Double)
  id: WithRotation``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithRotation<T>(T, double)
  nameWithType: FluentExtensions.WithRotation<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithRotation<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithRotation
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1398
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Rotation property
  example: []
  syntax:
    content: 'public static T WithRotation<T>(this T control, double rotation) where T : SkiaControl'
    parameters:
    - id: control
      type: '{T}'
    - id: rotation
      type: System.Double
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithRotation(Of T As SkiaControl)(control As T, rotation As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithRotation*
  nameWithType.vb: FluentExtensions.WithRotation(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithRotation(Of T)(T, Double)
  name.vb: WithRotation(Of T)(T, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithScale``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithScale``1(``0,System.Double)
  id: WithScale``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithScale<T>(T, double)
  nameWithType: FluentExtensions.WithScale<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithScale<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithScale
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1407
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Scale property
  example: []
  syntax:
    content: 'public static T WithScale<T>(this T control, double scale) where T : SkiaControl'
    parameters:
    - id: control
      type: '{T}'
    - id: scale
      type: System.Double
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithScale(Of T As SkiaControl)(control As T, scale As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithScale*
  nameWithType.vb: FluentExtensions.WithScale(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithScale(Of T)(T, Double)
  name.vb: WithScale(Of T)(T, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithScale``1(``0,System.Double,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithScale``1(``0,System.Double,System.Double)
  id: WithScale``1(``0,System.Double,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithScale<T>(T, double, double)
  nameWithType: FluentExtensions.WithScale<T>(T, double, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithScale<T>(T, double, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithScale
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1416
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets separate X and Y scale values
  example: []
  syntax:
    content: 'public static T WithScale<T>(this T control, double scaleX, double scaleY) where T : SkiaControl'
    parameters:
    - id: control
      type: '{T}'
    - id: scaleX
      type: System.Double
    - id: scaleY
      type: System.Double
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithScale(Of T As SkiaControl)(control As T, scaleX As Double, scaleY As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithScale*
  nameWithType.vb: FluentExtensions.WithScale(Of T)(T, Double, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithScale(Of T)(T, Double, Double)
  name.vb: WithScale(Of T)(T, Double, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithType``1(``0,DrawnUi.Draw.LayoutType)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithType``1(``0,DrawnUi.Draw.LayoutType)
  id: WithType``1(``0,DrawnUi.Draw.LayoutType)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithType<T>(T, LayoutType)
  nameWithType: FluentExtensions.WithType<T>(T, LayoutType)
  fullName: DrawnUi.Draw.FluentExtensions.WithType<T>(T, DrawnUi.Draw.LayoutType)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithType
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1426
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Type property for SkiaLayout
  example: []
  syntax:
    content: 'public static T WithType<T>(this T layout, LayoutType type) where T : SkiaLayout'
    parameters:
    - id: layout
      type: '{T}'
    - id: type
      type: DrawnUi.Draw.LayoutType
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithType(Of T As SkiaLayout)(layout As T, type As LayoutType) As T
  overload: DrawnUi.Draw.FluentExtensions.WithType*
  nameWithType.vb: FluentExtensions.WithType(Of T)(T, LayoutType)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithType(Of T)(T, DrawnUi.Draw.LayoutType)
  name.vb: WithType(Of T)(T, LayoutType)
- uid: DrawnUi.Draw.FluentExtensions.WithSpacing``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithSpacing``1(``0,System.Double)
  id: WithSpacing``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithSpacing<T>(T, double)
  nameWithType: FluentExtensions.WithSpacing<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithSpacing<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithSpacing
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1435
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the Spacing property for SkiaLayout
  example: []
  syntax:
    content: 'public static T WithSpacing<T>(this T layout, double spacing) where T : SkiaLayout'
    parameters:
    - id: layout
      type: '{T}'
    - id: spacing
      type: System.Double
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithSpacing(Of T As SkiaLayout)(layout As T, spacing As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithSpacing*
  nameWithType.vb: FluentExtensions.WithSpacing(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithSpacing(Of T)(T, Double)
  name.vb: WithSpacing(Of T)(T, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithColumnSpacing``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithColumnSpacing``1(``0,System.Double)
  id: WithColumnSpacing``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithColumnSpacing<T>(T, double)
  nameWithType: FluentExtensions.WithColumnSpacing<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithColumnSpacing<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithColumnSpacing
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1444
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the ColumnSpacing property for SkiaGrid
  example: []
  syntax:
    content: 'public static T WithColumnSpacing<T>(this T grid, double spacing) where T : SkiaGrid'
    parameters:
    - id: grid
      type: '{T}'
    - id: spacing
      type: System.Double
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithColumnSpacing(Of T As SkiaGrid)(grid As T, spacing As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithColumnSpacing*
  nameWithType.vb: FluentExtensions.WithColumnSpacing(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithColumnSpacing(Of T)(T, Double)
  name.vb: WithColumnSpacing(Of T)(T, Double)
- uid: DrawnUi.Draw.FluentExtensions.WithRowSpacing``1(``0,System.Double)
  commentId: M:DrawnUi.Draw.FluentExtensions.WithRowSpacing``1(``0,System.Double)
  id: WithRowSpacing``1(``0,System.Double)
  isExtensionMethod: true
  parent: DrawnUi.Draw.FluentExtensions
  langs:
  - csharp
  - vb
  name: WithRowSpacing<T>(T, double)
  nameWithType: FluentExtensions.WithRowSpacing<T>(T, double)
  fullName: DrawnUi.Draw.FluentExtensions.WithRowSpacing<T>(T, double)
  type: Method
  source:
    remote:
      path: src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WithRowSpacing
    path: ../src/Shared/Draw/Internals/Extensions/FluentExtensions.Shared.cs
    startLine: 1453
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the RowSpacing property for SkiaGrid
  example: []
  syntax:
    content: 'public static T WithRowSpacing<T>(this T grid, double spacing) where T : SkiaGrid'
    parameters:
    - id: grid
      type: '{T}'
    - id: spacing
      type: System.Double
    typeParameters:
    - id: T
    return:
      type: '{T}'
    content.vb: Public Shared Function WithRowSpacing(Of T As SkiaGrid)(grid As T, spacing As Double) As T
  overload: DrawnUi.Draw.FluentExtensions.WithRowSpacing*
  nameWithType.vb: FluentExtensions.WithRowSpacing(Of T)(T, Double)
  fullName.vb: DrawnUi.Draw.FluentExtensions.WithRowSpacing(Of T)(T, Double)
  name.vb: WithRowSpacing(Of T)(T, Double)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Draw.FluentExtensions.AssignNative*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.AssignNative
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignNative__1___0___0__
  name: AssignNative
  nameWithType: FluentExtensions.AssignNative
  fullName: DrawnUi.Draw.FluentExtensions.AssignNative
- uid: '{T}'
  commentId: '!:T'
  definition: T
  name: T
  nameWithType: T
  fullName: T
- uid: T
  name: T
  nameWithType: T
  fullName: T
- uid: DrawnUi.Draw.FluentExtensions.WithRow*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithRow
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRow__1___0_System_Int32_
  name: WithRow
  nameWithType: FluentExtensions.WithRow
  fullName: DrawnUi.Draw.FluentExtensions.WithRow
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.FluentExtensions.WithColumn*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithColumn
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumn__1___0_System_Int32_
  name: WithColumn
  nameWithType: FluentExtensions.WithColumn
  fullName: DrawnUi.Draw.FluentExtensions.WithColumn
- uid: DrawnUi.Draw.FluentExtensions.WithRowSpan*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithRowSpan
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRowSpan__1___0_System_Int32_
  name: WithRowSpan
  nameWithType: FluentExtensions.WithRowSpan
  fullName: DrawnUi.Draw.FluentExtensions.WithRowSpan
- uid: DrawnUi.Draw.FluentExtensions.WithColumnSpan*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithColumnSpan
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumnSpan__1___0_System_Int32_
  name: WithColumnSpan
  nameWithType: FluentExtensions.WithColumnSpan
  fullName: DrawnUi.Draw.FluentExtensions.WithColumnSpan
- uid: System.InvalidOperationException
  commentId: T:System.InvalidOperationException
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.invalidoperationexception
  name: InvalidOperationException
  nameWithType: InvalidOperationException
  fullName: System.InvalidOperationException
- uid: DrawnUi.Draw.FluentExtensions.WithColumnDefinitions*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithColumnDefinitions
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumnDefinitions_DrawnUi_Draw_SkiaLayout_System_String_
  name: WithColumnDefinitions
  nameWithType: FluentExtensions.WithColumnDefinitions
  fullName: DrawnUi.Draw.FluentExtensions.WithColumnDefinitions
- uid: DrawnUi.Draw.SkiaLayout
  commentId: T:DrawnUi.Draw.SkiaLayout
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLayout.html
  name: SkiaLayout
  nameWithType: SkiaLayout
  fullName: DrawnUi.Draw.SkiaLayout
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Draw.FluentExtensions.WithPoints*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithPoints
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithPoints_DrawnUi_Draw_SkiaShape_System_String_
  name: WithPoints
  nameWithType: FluentExtensions.WithPoints
  fullName: DrawnUi.Draw.FluentExtensions.WithPoints
- uid: DrawnUi.Draw.SkiaShape
  commentId: T:DrawnUi.Draw.SkiaShape
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaShape.html
  name: SkiaShape
  nameWithType: SkiaShape
  fullName: DrawnUi.Draw.SkiaShape
- uid: DrawnUi.Draw.FluentExtensions.WithRowDefinitions*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithRowDefinitions
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRowDefinitions_DrawnUi_Draw_SkiaLayout_System_String_
  name: WithRowDefinitions
  nameWithType: FluentExtensions.WithRowDefinitions
  fullName: DrawnUi.Draw.FluentExtensions.WithRowDefinitions
- uid: DrawnUi.Draw.FluentExtensions.SetGrid*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.SetGrid
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_SetGrid__1___0_System_Int32_System_Int32_
  name: SetGrid
  nameWithType: FluentExtensions.SetGrid
  fullName: DrawnUi.Draw.FluentExtensions.SetGrid
- uid: DrawnUi.Draw.FluentExtensions.BindProperty*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.BindProperty
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_BindProperty__1___0_Microsoft_Maui_Controls_BindableProperty_System_String_Microsoft_Maui_Controls_BindingMode_
  name: BindProperty
  nameWithType: FluentExtensions.BindProperty
  fullName: DrawnUi.Draw.FluentExtensions.BindProperty
- uid: Microsoft.Maui.Controls.BindableProperty
  commentId: T:Microsoft.Maui.Controls.BindableProperty
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  name: BindableProperty
  nameWithType: BindableProperty
  fullName: Microsoft.Maui.Controls.BindableProperty
- uid: Microsoft.Maui.Controls.BindingMode
  commentId: T:Microsoft.Maui.Controls.BindingMode
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingmode
  name: BindingMode
  nameWithType: BindingMode
  fullName: Microsoft.Maui.Controls.BindingMode
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: Microsoft.Maui.Controls.IValueConverter
  commentId: T:Microsoft.Maui.Controls.IValueConverter
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ivalueconverter
  name: IValueConverter
  nameWithType: IValueConverter
  fullName: Microsoft.Maui.Controls.IValueConverter
- uid: DrawnUi.Draw.FluentExtensions.WithTop*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithTop
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithTop_Microsoft_Maui_Thickness_System_Double_
  name: WithTop
  nameWithType: FluentExtensions.WithTop
  fullName: DrawnUi.Draw.FluentExtensions.WithTop
- uid: Microsoft.Maui.Thickness
  commentId: T:Microsoft.Maui.Thickness
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.thickness
  name: Thickness
  nameWithType: Thickness
  fullName: Microsoft.Maui.Thickness
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: DrawnUi.Draw.FluentExtensions.WithBottom*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithBottom
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithBottom_Microsoft_Maui_Thickness_System_Double_
  name: WithBottom
  nameWithType: FluentExtensions.WithBottom
  fullName: DrawnUi.Draw.FluentExtensions.WithBottom
- uid: DrawnUi.Draw.FluentExtensions.WithLeft*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithLeft
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithLeft_Microsoft_Maui_Thickness_System_Double_
  name: WithLeft
  nameWithType: FluentExtensions.WithLeft
  fullName: DrawnUi.Draw.FluentExtensions.WithLeft
- uid: DrawnUi.Draw.FluentExtensions.WithRight*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithRight
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRight_Microsoft_Maui_Thickness_System_Double_
  name: WithRight
  nameWithType: FluentExtensions.WithRight
  fullName: DrawnUi.Draw.FluentExtensions.WithRight
- uid: DrawnUi.Draw.FluentExtensions.Assign*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.Assign
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Assign__1___0___0__
  name: Assign
  nameWithType: FluentExtensions.Assign
  fullName: DrawnUi.Draw.FluentExtensions.Assign
- uid: DrawnUi.Draw.FluentExtensions.AssignParent*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.AssignParent
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_AssignParent__1___0_DrawnUi_Draw_SkiaControl_
  name: AssignParent
  nameWithType: FluentExtensions.AssignParent
  fullName: DrawnUi.Draw.FluentExtensions.AssignParent
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw.FluentExtensions.Adapt*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.Adapt
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Adapt__1___0_System_Action___0__
  name: Adapt
  nameWithType: FluentExtensions.Adapt
  fullName: DrawnUi.Draw.FluentExtensions.Adapt
- uid: System.Action{{T}}
  commentId: T:System.Action{``0}
  parent: System
  definition: System.Action`1
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<T>
  nameWithType: Action<T>
  fullName: System.Action<T>
  nameWithType.vb: Action(Of T)
  fullName.vb: System.Action(Of T)
  name.vb: Action(Of T)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Action`1
  commentId: T:System.Action`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<T>
  nameWithType: Action<T>
  fullName: System.Action<T>
  nameWithType.vb: Action(Of T)
  fullName.vb: System.Action(Of T)
  name.vb: Action(Of T)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.WithGestures*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithGestures
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithGestures__1___0_System_Func___0_DrawnUi_Draw_SkiaGesturesParameters_DrawnUi_Draw_GestureEventProcessingInfo_DrawnUi_Draw_ISkiaGestureListener__
  name: WithGestures
  nameWithType: FluentExtensions.WithGestures
  fullName: DrawnUi.Draw.FluentExtensions.WithGestures
- uid: System.Func{{T},DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo,DrawnUi.Draw.ISkiaGestureListener}
  commentId: T:System.Func{``0,DrawnUi.Draw.SkiaGesturesParameters,DrawnUi.Draw.GestureEventProcessingInfo,DrawnUi.Draw.ISkiaGestureListener}
  parent: System
  definition: System.Func`4
  href: https://learn.microsoft.com/dotnet/api/system.func-4
  name: Func<T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener>
  nameWithType: Func<T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener>
  fullName: System.Func<T, DrawnUi.Draw.SkiaGesturesParameters, DrawnUi.Draw.GestureEventProcessingInfo, DrawnUi.Draw.ISkiaGestureListener>
  nameWithType.vb: Func(Of T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener)
  fullName.vb: System.Func(Of T, DrawnUi.Draw.SkiaGesturesParameters, DrawnUi.Draw.GestureEventProcessingInfo, DrawnUi.Draw.ISkiaGestureListener)
  name.vb: Func(Of T, SkiaGesturesParameters, GestureEventProcessingInfo, ISkiaGestureListener)
  spec.csharp:
  - uid: System.Func`4
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-4
  - name: <
  - name: T
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaGesturesParameters
    name: SkiaGesturesParameters
    href: DrawnUi.Draw.SkiaGesturesParameters.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.GestureEventProcessingInfo
    name: GestureEventProcessingInfo
    href: DrawnUi.Draw.GestureEventProcessingInfo.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: '>'
  spec.vb:
  - uid: System.Func`4
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-4
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.SkiaGesturesParameters
    name: SkiaGesturesParameters
    href: DrawnUi.Draw.SkiaGesturesParameters.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.GestureEventProcessingInfo
    name: GestureEventProcessingInfo
    href: DrawnUi.Draw.GestureEventProcessingInfo.html
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.ISkiaGestureListener
    name: ISkiaGestureListener
    href: DrawnUi.Draw.ISkiaGestureListener.html
  - name: )
- uid: System.Func`4
  commentId: T:System.Func`4
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.func-4
  name: Func<T1, T2, T3, TResult>
  nameWithType: Func<T1, T2, T3, TResult>
  fullName: System.Func<T1, T2, T3, TResult>
  nameWithType.vb: Func(Of T1, T2, T3, TResult)
  fullName.vb: System.Func(Of T1, T2, T3, TResult)
  name.vb: Func(Of T1, T2, T3, TResult)
  spec.csharp:
  - uid: System.Func`4
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-4
  - name: <
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: ','
  - name: " "
  - name: T3
  - name: ','
  - name: " "
  - name: TResult
  - name: '>'
  spec.vb:
  - uid: System.Func`4
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-4
  - name: (
  - name: Of
  - name: " "
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: ','
  - name: " "
  - name: T3
  - name: ','
  - name: " "
  - name: TResult
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.Initialize*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.Initialize
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Initialize__1___0_System_Action___0__
  name: Initialize
  nameWithType: FluentExtensions.Initialize
  fullName: DrawnUi.Draw.FluentExtensions.Initialize
- uid: DrawnUi.Draw.FluentExtensions.OnPaint*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.OnPaint
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnPaint__1___0_System_Action___0_DrawnUi_Draw_DrawingContext__
  name: OnPaint
  nameWithType: FluentExtensions.OnPaint
  fullName: DrawnUi.Draw.FluentExtensions.OnPaint
- uid: System.Action{{T},DrawnUi.Draw.DrawingContext}
  commentId: T:System.Action{``0,DrawnUi.Draw.DrawingContext}
  parent: System
  definition: System.Action`2
  href: https://learn.microsoft.com/dotnet/api/system.action-2
  name: Action<T, DrawingContext>
  nameWithType: Action<T, DrawingContext>
  fullName: System.Action<T, DrawnUi.Draw.DrawingContext>
  nameWithType.vb: Action(Of T, DrawingContext)
  fullName.vb: System.Action(Of T, DrawnUi.Draw.DrawingContext)
  name.vb: Action(Of T, DrawingContext)
  spec.csharp:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: <
  - name: T
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: '>'
  spec.vb:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: ','
  - name: " "
  - uid: DrawnUi.Draw.DrawingContext
    name: DrawingContext
    href: DrawnUi.Draw.DrawingContext.html
  - name: )
- uid: System.Action`2
  commentId: T:System.Action`2
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-2
  name: Action<T1, T2>
  nameWithType: Action<T1, T2>
  fullName: System.Action<T1, T2>
  nameWithType.vb: Action(Of T1, T2)
  fullName.vb: System.Action(Of T1, T2)
  name.vb: Action(Of T1, T2)
  spec.csharp:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: <
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: '>'
  spec.vb:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: (
  - name: Of
  - name: " "
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.OnBindingContextSet*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.OnBindingContextSet
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnBindingContextSet__1___0_System_Action___0_System_Object__System_String___
  name: OnBindingContextSet
  nameWithType: FluentExtensions.OnBindingContextSet
  fullName: DrawnUi.Draw.FluentExtensions.OnBindingContextSet
- uid: System.Action{{T},System.Object}
  commentId: T:System.Action{``0,System.Object}
  parent: System
  definition: System.Action`2
  href: https://learn.microsoft.com/dotnet/api/system.action-2
  name: Action<T, object>
  nameWithType: Action<T, object>
  fullName: System.Action<T, object>
  nameWithType.vb: Action(Of T, Object)
  fullName.vb: System.Action(Of T, Object)
  name.vb: Action(Of T, Object)
  spec.csharp:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: <
  - name: T
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: '>'
  spec.vb:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.String[]
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string[]
  nameWithType: string[]
  fullName: string[]
  nameWithType.vb: String()
  fullName.vb: String()
  name.vb: String()
  spec.csharp:
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '['
  - name: ']'
  spec.vb:
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: (
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.ObserveSelf*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.ObserveSelf
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveSelf__1___0_System_Action___0_System_String__
  name: ObserveSelf
  nameWithType: FluentExtensions.ObserveSelf
  fullName: DrawnUi.Draw.FluentExtensions.ObserveSelf
- uid: System.Action{{T},System.String}
  commentId: T:System.Action{``0,System.String}
  parent: System
  definition: System.Action`2
  href: https://learn.microsoft.com/dotnet/api/system.action-2
  name: Action<T, string>
  nameWithType: Action<T, string>
  fullName: System.Action<T, string>
  nameWithType.vb: Action(Of T, String)
  fullName.vb: System.Action(Of T, String)
  name.vb: Action(Of T, String)
  spec.csharp:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: <
  - name: T
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '>'
  spec.vb:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.Observe*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.Observe
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Observe__2___0___1_System_Action___0_System_String__System_String___
  name: Observe
  nameWithType: FluentExtensions.Observe
  fullName: DrawnUi.Draw.FluentExtensions.Observe
- uid: '{TSource}'
  commentId: '!:TSource'
  definition: TSource
  name: TSource
  nameWithType: TSource
  fullName: TSource
- uid: TSource
  name: TSource
  nameWithType: TSource
  fullName: TSource
- uid: DrawnUi.Draw.FluentExtensions.ObservePropertiesOn*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.ObservePropertiesOn
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObservePropertiesOn__3___0___1_System_Func___2__System_String_System_Collections_Generic_IEnumerable_System_String__System_Action___0__
  name: ObservePropertiesOn
  nameWithType: FluentExtensions.ObservePropertiesOn
  fullName: DrawnUi.Draw.FluentExtensions.ObservePropertiesOn
- uid: '{TParent}'
  commentId: '!:TParent'
  definition: TParent
  name: TParent
  nameWithType: TParent
  fullName: TParent
- uid: System.Func{{TTarget}}
  commentId: T:System.Func{``2}
  parent: System
  definition: System.Func`1
  href: https://learn.microsoft.com/dotnet/api/system.func-1
  name: Func<TTarget>
  nameWithType: Func<TTarget>
  fullName: System.Func<TTarget>
  nameWithType.vb: Func(Of TTarget)
  fullName.vb: System.Func(Of TTarget)
  name.vb: Func(Of TTarget)
  spec.csharp:
  - uid: System.Func`1
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-1
  - name: <
  - name: TTarget
  - name: '>'
  spec.vb:
  - uid: System.Func`1
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-1
  - name: (
  - name: Of
  - name: " "
  - name: TTarget
  - name: )
- uid: System.Collections.Generic.IEnumerable{System.String}
  commentId: T:System.Collections.Generic.IEnumerable{System.String}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IEnumerable`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<string>
  nameWithType: IEnumerable<string>
  fullName: System.Collections.Generic.IEnumerable<string>
  nameWithType.vb: IEnumerable(Of String)
  fullName.vb: System.Collections.Generic.IEnumerable(Of String)
  name.vb: IEnumerable(Of String)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: TParent
  name: TParent
  nameWithType: TParent
  fullName: TParent
- uid: System.Func`1
  commentId: T:System.Func`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.func-1
  name: Func<TResult>
  nameWithType: Func<TResult>
  fullName: System.Func<TResult>
  nameWithType.vb: Func(Of TResult)
  fullName.vb: System.Func(Of TResult)
  name.vb: Func(Of TResult)
  spec.csharp:
  - uid: System.Func`1
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-1
  - name: <
  - name: TResult
  - name: '>'
  spec.vb:
  - uid: System.Func`1
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-1
  - name: (
  - name: Of
  - name: " "
  - name: TResult
  - name: )
- uid: System.Collections.Generic.IEnumerable`1
  commentId: T:System.Collections.Generic.IEnumerable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  name: IEnumerable<T>
  nameWithType: IEnumerable<T>
  fullName: System.Collections.Generic.IEnumerable<T>
  nameWithType.vb: IEnumerable(Of T)
  fullName.vb: System.Collections.Generic.IEnumerable(Of T)
  name.vb: IEnumerable(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerable`1
    name: IEnumerable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Draw.FluentExtensions.ObservePropertyOn*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.ObservePropertyOn
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObservePropertyOn__3___0___1_System_Func___2__System_String_System_String_System_Action___0__
  name: ObservePropertyOn
  nameWithType: FluentExtensions.ObservePropertyOn
  fullName: DrawnUi.Draw.FluentExtensions.ObservePropertyOn
- uid: DrawnUi.Draw.FluentExtensions.ObserveOn*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.ObserveOn
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveOn__3___0___1_System_Func___2__System_String_System_Action___0_System_String__System_String___
  name: ObserveOn
  nameWithType: FluentExtensions.ObserveOn
  fullName: DrawnUi.Draw.FluentExtensions.ObserveOn
- uid: DrawnUi.Draw.FluentExtensions.ObserveProperty*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.ObserveProperty
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveProperty__2___0___1_System_String_System_Action___0__
  name: ObserveProperty
  nameWithType: FluentExtensions.ObserveProperty
  fullName: DrawnUi.Draw.FluentExtensions.ObserveProperty
- uid: System.Func{{TSource}}
  commentId: T:System.Func{``1}
  parent: System
  definition: System.Func`1
  href: https://learn.microsoft.com/dotnet/api/system.func-1
  name: Func<TSource>
  nameWithType: Func<TSource>
  fullName: System.Func<TSource>
  nameWithType.vb: Func(Of TSource)
  fullName.vb: System.Func(Of TSource)
  name.vb: Func(Of TSource)
  spec.csharp:
  - uid: System.Func`1
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-1
  - name: <
  - name: TSource
  - name: '>'
  spec.vb:
  - uid: System.Func`1
    name: Func
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.func-1
  - name: (
  - name: Of
  - name: " "
  - name: TSource
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.ObserveProperties*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.ObserveProperties
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveProperties__2___0___1_System_Collections_Generic_IEnumerable_System_String__System_Action___0__
  name: ObserveProperties
  nameWithType: FluentExtensions.ObserveProperties
  fullName: DrawnUi.Draw.FluentExtensions.ObserveProperties
- uid: DrawnUi.Draw.FluentExtensions.ObserveBindingContext*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.ObserveBindingContext
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveBindingContext__2___0_System_Action___0___1_System_String__System_Boolean_
  name: ObserveBindingContext
  nameWithType: FluentExtensions.ObserveBindingContext
  fullName: DrawnUi.Draw.FluentExtensions.ObserveBindingContext
- uid: System.Action{{T},{TSource},System.String}
  commentId: T:System.Action{``0,``1,System.String}
  parent: System
  definition: System.Action`3
  href: https://learn.microsoft.com/dotnet/api/system.action-3
  name: Action<T, TSource, string>
  nameWithType: Action<T, TSource, string>
  fullName: System.Action<T, TSource, string>
  nameWithType.vb: Action(Of T, TSource, String)
  fullName.vb: System.Action(Of T, TSource, String)
  name.vb: Action(Of T, TSource, String)
  spec.csharp:
  - uid: System.Action`3
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-3
  - name: <
  - name: T
  - name: ','
  - name: " "
  - name: TSource
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '>'
  spec.vb:
  - uid: System.Action`3
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-3
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: ','
  - name: " "
  - name: TSource
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: System.Action`3
  commentId: T:System.Action`3
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-3
  name: Action<T1, T2, T3>
  nameWithType: Action<T1, T2, T3>
  fullName: System.Action<T1, T2, T3>
  nameWithType.vb: Action(Of T1, T2, T3)
  fullName.vb: System.Action(Of T1, T2, T3)
  name.vb: Action(Of T1, T2, T3)
  spec.csharp:
  - uid: System.Action`3
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-3
  - name: <
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: ','
  - name: " "
  - name: T3
  - name: '>'
  spec.vb:
  - uid: System.Action`3
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-3
  - name: (
  - name: Of
  - name: " "
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: ','
  - name: " "
  - name: T3
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.ObserveBindingContextOn*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.ObserveBindingContextOn
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_ObserveBindingContextOn__3___0___1_System_Action___0___1___2_System_String__System_Boolean_
  name: ObserveBindingContextOn
  nameWithType: FluentExtensions.ObserveBindingContextOn
  fullName: DrawnUi.Draw.FluentExtensions.ObserveBindingContextOn
- uid: '{TTarget}'
  commentId: '!:TTarget'
  definition: TTarget
  name: TTarget
  nameWithType: TTarget
  fullName: TTarget
- uid: System.Action{{T},{TTarget},{TSource},System.String}
  commentId: T:System.Action{``0,``1,``2,System.String}
  parent: System
  definition: System.Action`4
  href: https://learn.microsoft.com/dotnet/api/system.action-4
  name: Action<T, TTarget, TSource, string>
  nameWithType: Action<T, TTarget, TSource, string>
  fullName: System.Action<T, TTarget, TSource, string>
  nameWithType.vb: Action(Of T, TTarget, TSource, String)
  fullName.vb: System.Action(Of T, TTarget, TSource, String)
  name.vb: Action(Of T, TTarget, TSource, String)
  spec.csharp:
  - uid: System.Action`4
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-4
  - name: <
  - name: T
  - name: ','
  - name: " "
  - name: TTarget
  - name: ','
  - name: " "
  - name: TSource
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '>'
  spec.vb:
  - uid: System.Action`4
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-4
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: ','
  - name: " "
  - name: TTarget
  - name: ','
  - name: " "
  - name: TSource
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: TTarget
  commentId: '!:TTarget'
  name: TTarget
  nameWithType: TTarget
  fullName: TTarget
- uid: System.Action`4
  commentId: T:System.Action`4
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-4
  name: Action<T1, T2, T3, T4>
  nameWithType: Action<T1, T2, T3, T4>
  fullName: System.Action<T1, T2, T3, T4>
  nameWithType.vb: Action(Of T1, T2, T3, T4)
  fullName.vb: System.Action(Of T1, T2, T3, T4)
  name.vb: Action(Of T1, T2, T3, T4)
  spec.csharp:
  - uid: System.Action`4
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-4
  - name: <
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: ','
  - name: " "
  - name: T3
  - name: ','
  - name: " "
  - name: T4
  - name: '>'
  spec.vb:
  - uid: System.Action`4
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-4
  - name: (
  - name: Of
  - name: " "
  - name: T1
  - name: ','
  - name: " "
  - name: T2
  - name: ','
  - name: " "
  - name: T3
  - name: ','
  - name: " "
  - name: T4
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.OnToggled*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.OnToggled
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnToggled__1___0_System_Action___0_System_Boolean__
  name: OnToggled
  nameWithType: FluentExtensions.OnToggled
  fullName: DrawnUi.Draw.FluentExtensions.OnToggled
- uid: System.Action{{T},System.Boolean}
  commentId: T:System.Action{``0,System.Boolean}
  parent: System
  definition: System.Action`2
  href: https://learn.microsoft.com/dotnet/api/system.action-2
  name: Action<T, bool>
  nameWithType: Action<T, bool>
  fullName: System.Action<T, bool>
  nameWithType.vb: Action(Of T, Boolean)
  fullName.vb: System.Action(Of T, Boolean)
  name.vb: Action(Of T, Boolean)
  spec.csharp:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: <
  - name: T
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: '>'
  spec.vb:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.OnTapped*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.OnTapped
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnTapped__1___0_System_Action___0__
  name: OnTapped
  nameWithType: FluentExtensions.OnTapped
  fullName: DrawnUi.Draw.FluentExtensions.OnTapped
- uid: DrawnUi.Draw.FluentExtensions.OnLongPressing*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.OnLongPressing
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnLongPressing__1___0_System_Action___0__
  name: OnLongPressing
  nameWithType: FluentExtensions.OnLongPressing
  fullName: DrawnUi.Draw.FluentExtensions.OnLongPressing
- uid: DrawnUi.Draw.FluentExtensions.WithChildren*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithChildren
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithChildren__1___0_DrawnUi_Draw_SkiaControl___
  name: WithChildren
  nameWithType: FluentExtensions.WithChildren
  fullName: DrawnUi.Draw.FluentExtensions.WithChildren
- uid: DrawnUi.Draw.SkiaControl[]
  isExternal: true
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl[]
  nameWithType: SkiaControl[]
  fullName: DrawnUi.Draw.SkiaControl[]
  nameWithType.vb: SkiaControl()
  fullName.vb: DrawnUi.Draw.SkiaControl()
  name.vb: SkiaControl()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '['
  - name: ']'
  spec.vb:
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: (
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.WithContent*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithContent
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithContent__1___0_DrawnUi_Draw_SkiaControl_
  name: WithContent
  nameWithType: FluentExtensions.WithContent
  fullName: DrawnUi.Draw.FluentExtensions.WithContent
- uid: DrawnUi.Draw.FluentExtensions.WithParent*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithParent
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithParent__1___0_DrawnUi_Draw_IDrawnBase_
  name: WithParent
  nameWithType: FluentExtensions.WithParent
  fullName: DrawnUi.Draw.FluentExtensions.WithParent
- uid: DrawnUi.Draw.IDrawnBase
  commentId: T:DrawnUi.Draw.IDrawnBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnBase.html
  name: IDrawnBase
  nameWithType: IDrawnBase
  fullName: DrawnUi.Draw.IDrawnBase
- uid: DrawnUi.Draw.FluentExtensions.CenterX*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.CenterX
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_CenterX__1___0_
  name: CenterX
  nameWithType: FluentExtensions.CenterX
  fullName: DrawnUi.Draw.FluentExtensions.CenterX
- uid: DrawnUi.Draw.FluentExtensions.CenterY*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.CenterY
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_CenterY__1___0_
  name: CenterY
  nameWithType: FluentExtensions.CenterY
  fullName: DrawnUi.Draw.FluentExtensions.CenterY
- uid: DrawnUi.Draw.FluentExtensions.Fill*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.Fill
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Fill__1___0_
  name: Fill
  nameWithType: FluentExtensions.Fill
  fullName: DrawnUi.Draw.FluentExtensions.Fill
- uid: DrawnUi.Draw.FluentExtensions.FillX*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.FillX
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_FillX__1___0_
  name: FillX
  nameWithType: FluentExtensions.FillX
  fullName: DrawnUi.Draw.FluentExtensions.FillX
- uid: DrawnUi.Draw.FluentExtensions.EndX*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.EndX
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_EndX__1___0_
  name: EndX
  nameWithType: FluentExtensions.EndX
  fullName: DrawnUi.Draw.FluentExtensions.EndX
- uid: DrawnUi.Draw.FluentExtensions.EndY*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.EndY
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_EndY__1___0_
  name: EndY
  nameWithType: FluentExtensions.EndY
  fullName: DrawnUi.Draw.FluentExtensions.EndY
- uid: DrawnUi.Draw.FluentExtensions.StartX*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.StartX
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_StartX__1___0_
  name: StartX
  nameWithType: FluentExtensions.StartX
  fullName: DrawnUi.Draw.FluentExtensions.StartX
- uid: DrawnUi.Draw.FluentExtensions.StartY*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.StartY
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_StartY__1___0_
  name: StartY
  nameWithType: FluentExtensions.StartY
  fullName: DrawnUi.Draw.FluentExtensions.StartY
- uid: DrawnUi.Draw.FluentExtensions.FillY*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.FillY
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_FillY__1___0_
  name: FillY
  nameWithType: FluentExtensions.FillY
  fullName: DrawnUi.Draw.FluentExtensions.FillY
- uid: DrawnUi.Draw.FluentExtensions.Center*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.Center
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Center__1___0_
  name: Center
  nameWithType: FluentExtensions.Center
  fullName: DrawnUi.Draw.FluentExtensions.Center
- uid: DrawnUi.Draw.FluentExtensions.WithMargin*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithMargin
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithMargin__1___0_System_Double_
  name: WithMargin
  nameWithType: FluentExtensions.WithMargin
  fullName: DrawnUi.Draw.FluentExtensions.WithMargin
- uid: DrawnUi.Draw.FluentExtensions.WithPadding*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithPadding
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithPadding__1___0_System_Double_
  name: WithPadding
  nameWithType: FluentExtensions.WithPadding
  fullName: DrawnUi.Draw.FluentExtensions.WithPadding
- uid: DrawnUi.Draw.FluentExtensions.WithTag*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithTag
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithTag__1___0_System_String_
  name: WithTag
  nameWithType: FluentExtensions.WithTag
  fullName: DrawnUi.Draw.FluentExtensions.WithTag
- uid: DrawnUi.Draw.FluentExtensions.WithCache*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithCache
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithCache__1___0_DrawnUi_Draw_SkiaCacheType_
  name: WithCache
  nameWithType: FluentExtensions.WithCache
  fullName: DrawnUi.Draw.FluentExtensions.WithCache
- uid: DrawnUi.Draw.SkiaCacheType
  commentId: T:DrawnUi.Draw.SkiaCacheType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaCacheType.html
  name: SkiaCacheType
  nameWithType: SkiaCacheType
  fullName: DrawnUi.Draw.SkiaCacheType
- uid: DrawnUi.Draw.FluentExtensions.WithBackgroundColor*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithBackgroundColor
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithBackgroundColor__1___0_Microsoft_Maui_Graphics_Color_
  name: WithBackgroundColor
  nameWithType: FluentExtensions.WithBackgroundColor
  fullName: DrawnUi.Draw.FluentExtensions.WithBackgroundColor
- uid: Microsoft.Maui.Graphics.Color
  commentId: T:Microsoft.Maui.Graphics.Color
  parent: Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  name: Color
  nameWithType: Color
  fullName: Microsoft.Maui.Graphics.Color
- uid: Microsoft.Maui.Graphics
  commentId: N:Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Graphics
  nameWithType: Microsoft.Maui.Graphics
  fullName: Microsoft.Maui.Graphics
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
- uid: DrawnUi.Draw.FluentExtensions.WithHorizontalOptions*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithHorizontalOptions
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithHorizontalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_
  name: WithHorizontalOptions
  nameWithType: FluentExtensions.WithHorizontalOptions
  fullName: DrawnUi.Draw.FluentExtensions.WithHorizontalOptions
- uid: Microsoft.Maui.Controls.LayoutOptions
  commentId: T:Microsoft.Maui.Controls.LayoutOptions
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.layoutoptions
  name: LayoutOptions
  nameWithType: LayoutOptions
  fullName: Microsoft.Maui.Controls.LayoutOptions
- uid: DrawnUi.Draw.FluentExtensions.WithVerticalOptions*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithVerticalOptions
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithVerticalOptions__1___0_Microsoft_Maui_Controls_LayoutOptions_
  name: WithVerticalOptions
  nameWithType: FluentExtensions.WithVerticalOptions
  fullName: DrawnUi.Draw.FluentExtensions.WithVerticalOptions
- uid: DrawnUi.Draw.FluentExtensions.WithHeight*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithHeight
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithHeight__1___0_System_Double_
  name: WithHeight
  nameWithType: FluentExtensions.WithHeight
  fullName: DrawnUi.Draw.FluentExtensions.WithHeight
- uid: DrawnUi.Draw.FluentExtensions.WithWidth*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithWidth
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithWidth__1___0_System_Double_
  name: WithWidth
  nameWithType: FluentExtensions.WithWidth
  fullName: DrawnUi.Draw.FluentExtensions.WithWidth
- uid: DrawnUi.Draw.FluentExtensions.WithVisibility*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithVisibility
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithVisibility__1___0_System_Boolean_
  name: WithVisibility
  nameWithType: FluentExtensions.WithVisibility
  fullName: DrawnUi.Draw.FluentExtensions.WithVisibility
- uid: DrawnUi.Draw.FluentExtensions.WithShapeType*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithShapeType
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithShapeType__1___0_DrawnUi_Draw_ShapeType_
  name: WithShapeType
  nameWithType: FluentExtensions.WithShapeType
  fullName: DrawnUi.Draw.FluentExtensions.WithShapeType
- uid: DrawnUi.Draw.ShapeType
  commentId: T:DrawnUi.Draw.ShapeType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ShapeType.html
  name: ShapeType
  nameWithType: ShapeType
  fullName: DrawnUi.Draw.ShapeType
- uid: DrawnUi.Draw.FluentExtensions.Shape*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.Shape
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_Shape__1___0_DrawnUi_Draw_ShapeType_
  name: Shape
  nameWithType: FluentExtensions.Shape
  fullName: DrawnUi.Draw.FluentExtensions.Shape
- uid: DrawnUi.Draw.FluentExtensions.WithAspect*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithAspect
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithAspect__1___0_DrawnUi_Draw_TransformAspect_
  name: WithAspect
  nameWithType: FluentExtensions.WithAspect
  fullName: DrawnUi.Draw.FluentExtensions.WithAspect
- uid: DrawnUi.Draw.TransformAspect
  commentId: T:DrawnUi.Draw.TransformAspect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TransformAspect.html
  name: TransformAspect
  nameWithType: TransformAspect
  fullName: DrawnUi.Draw.TransformAspect
- uid: DrawnUi.Draw.FluentExtensions.WithFontSize*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithFontSize
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithFontSize__1___0_System_Double_
  name: WithFontSize
  nameWithType: FluentExtensions.WithFontSize
  fullName: DrawnUi.Draw.FluentExtensions.WithFontSize
- uid: DrawnUi.Draw.FluentExtensions.WithTextColor*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithTextColor
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithTextColor__1___0_Microsoft_Maui_Graphics_Color_
  name: WithTextColor
  nameWithType: FluentExtensions.WithTextColor
  fullName: DrawnUi.Draw.FluentExtensions.WithTextColor
- uid: DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithHorizontalTextAlignment__1___0_DrawnUi_Draw_DrawTextAlignment_
  name: WithHorizontalTextAlignment
  nameWithType: FluentExtensions.WithHorizontalTextAlignment
  fullName: DrawnUi.Draw.FluentExtensions.WithHorizontalTextAlignment
- uid: DrawnUi.Draw.DrawTextAlignment
  commentId: T:DrawnUi.Draw.DrawTextAlignment
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawTextAlignment.html
  name: DrawTextAlignment
  nameWithType: DrawTextAlignment
  fullName: DrawnUi.Draw.DrawTextAlignment
- uid: DrawnUi.Draw.FluentExtensions.OnTextChanged*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.OnTextChanged
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_OnTextChanged_DrawnUi_Controls_SkiaMauiEntry_System_Action_DrawnUi_Controls_SkiaMauiEntry_System_String__
  name: OnTextChanged
  nameWithType: FluentExtensions.OnTextChanged
  fullName: DrawnUi.Draw.FluentExtensions.OnTextChanged
- uid: DrawnUi.Controls.SkiaMauiEntry
  commentId: T:DrawnUi.Controls.SkiaMauiEntry
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaMauiEntry.html
  name: SkiaMauiEntry
  nameWithType: SkiaMauiEntry
  fullName: DrawnUi.Controls.SkiaMauiEntry
- uid: System.Action{DrawnUi.Controls.SkiaMauiEntry,System.String}
  commentId: T:System.Action{DrawnUi.Controls.SkiaMauiEntry,System.String}
  parent: System
  definition: System.Action`2
  href: https://learn.microsoft.com/dotnet/api/system.action-2
  name: Action<SkiaMauiEntry, string>
  nameWithType: Action<SkiaMauiEntry, string>
  fullName: System.Action<DrawnUi.Controls.SkiaMauiEntry, string>
  nameWithType.vb: Action(Of SkiaMauiEntry, String)
  fullName.vb: System.Action(Of DrawnUi.Controls.SkiaMauiEntry, String)
  name.vb: Action(Of SkiaMauiEntry, String)
  spec.csharp:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: <
  - uid: DrawnUi.Controls.SkiaMauiEntry
    name: SkiaMauiEntry
    href: DrawnUi.Controls.SkiaMauiEntry.html
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '>'
  spec.vb:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Controls.SkiaMauiEntry
    name: SkiaMauiEntry
    href: DrawnUi.Controls.SkiaMauiEntry.html
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: DrawnUi.Controls
  commentId: N:DrawnUi.Controls
  href: DrawnUi.html
  name: DrawnUi.Controls
  nameWithType: DrawnUi.Controls
  fullName: DrawnUi.Controls
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Controls
    name: Controls
    href: DrawnUi.Controls.html
- uid: DrawnUi.Controls.SkiaMauiEditor
  commentId: T:DrawnUi.Controls.SkiaMauiEditor
  parent: DrawnUi.Controls
  href: DrawnUi.Controls.SkiaMauiEditor.html
  name: SkiaMauiEditor
  nameWithType: SkiaMauiEditor
  fullName: DrawnUi.Controls.SkiaMauiEditor
- uid: System.Action{DrawnUi.Controls.SkiaMauiEditor,System.String}
  commentId: T:System.Action{DrawnUi.Controls.SkiaMauiEditor,System.String}
  parent: System
  definition: System.Action`2
  href: https://learn.microsoft.com/dotnet/api/system.action-2
  name: Action<SkiaMauiEditor, string>
  nameWithType: Action<SkiaMauiEditor, string>
  fullName: System.Action<DrawnUi.Controls.SkiaMauiEditor, string>
  nameWithType.vb: Action(Of SkiaMauiEditor, String)
  fullName.vb: System.Action(Of DrawnUi.Controls.SkiaMauiEditor, String)
  name.vb: Action(Of SkiaMauiEditor, String)
  spec.csharp:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: <
  - uid: DrawnUi.Controls.SkiaMauiEditor
    name: SkiaMauiEditor
    href: DrawnUi.Controls.SkiaMauiEditor.html
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '>'
  spec.vb:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Controls.SkiaMauiEditor
    name: SkiaMauiEditor
    href: DrawnUi.Controls.SkiaMauiEditor.html
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: DrawnUi.Draw.SkiaLabel
  commentId: T:DrawnUi.Draw.SkiaLabel
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLabel.html
  name: SkiaLabel
  nameWithType: SkiaLabel
  fullName: DrawnUi.Draw.SkiaLabel
- uid: System.Action{DrawnUi.Draw.SkiaLabel,System.String}
  commentId: T:System.Action{DrawnUi.Draw.SkiaLabel,System.String}
  parent: System
  definition: System.Action`2
  href: https://learn.microsoft.com/dotnet/api/system.action-2
  name: Action<SkiaLabel, string>
  nameWithType: Action<SkiaLabel, string>
  fullName: System.Action<DrawnUi.Draw.SkiaLabel, string>
  nameWithType.vb: Action(Of SkiaLabel, String)
  fullName.vb: System.Action(Of DrawnUi.Draw.SkiaLabel, String)
  name.vb: Action(Of SkiaLabel, String)
  spec.csharp:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: <
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: '>'
  spec.vb:
  - uid: System.Action`2
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-2
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaLabel
    name: SkiaLabel
    href: DrawnUi.Draw.SkiaLabel.html
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: DrawnUi.Draw.FluentExtensions.WithText*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithText
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithText__1___0_System_String_
  name: WithText
  nameWithType: FluentExtensions.WithText
  fullName: DrawnUi.Draw.FluentExtensions.WithText
- uid: DrawnUi.Draw.FluentExtensions.WithItemsSource*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithItemsSource
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithItemsSource__1___0_System_Collections_IList_
  name: WithItemsSource
  nameWithType: FluentExtensions.WithItemsSource
  fullName: DrawnUi.Draw.FluentExtensions.WithItemsSource
- uid: System.Collections.IList
  commentId: T:System.Collections.IList
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ilist
  name: IList
  nameWithType: IList
  fullName: System.Collections.IList
- uid: System.Collections
  commentId: N:System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections
  nameWithType: System.Collections
  fullName: System.Collections
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
- uid: DrawnUi.Draw.FluentExtensions.WithEnabled*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithEnabled
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithEnabled__1___0_System_Boolean_
  name: WithEnabled
  nameWithType: FluentExtensions.WithEnabled
  fullName: DrawnUi.Draw.FluentExtensions.WithEnabled
- uid: DrawnUi.Draw.FluentExtensions.WithOpacity*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithOpacity
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithOpacity__1___0_System_Double_
  name: WithOpacity
  nameWithType: FluentExtensions.WithOpacity
  fullName: DrawnUi.Draw.FluentExtensions.WithOpacity
- uid: DrawnUi.Draw.FluentExtensions.WithRotation*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithRotation
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRotation__1___0_System_Double_
  name: WithRotation
  nameWithType: FluentExtensions.WithRotation
  fullName: DrawnUi.Draw.FluentExtensions.WithRotation
- uid: DrawnUi.Draw.FluentExtensions.WithScale*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithScale
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithScale__1___0_System_Double_
  name: WithScale
  nameWithType: FluentExtensions.WithScale
  fullName: DrawnUi.Draw.FluentExtensions.WithScale
- uid: DrawnUi.Draw.FluentExtensions.WithType*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithType
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithType__1___0_DrawnUi_Draw_LayoutType_
  name: WithType
  nameWithType: FluentExtensions.WithType
  fullName: DrawnUi.Draw.FluentExtensions.WithType
- uid: DrawnUi.Draw.LayoutType
  commentId: T:DrawnUi.Draw.LayoutType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.LayoutType.html
  name: LayoutType
  nameWithType: LayoutType
  fullName: DrawnUi.Draw.LayoutType
- uid: DrawnUi.Draw.FluentExtensions.WithSpacing*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithSpacing
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithSpacing__1___0_System_Double_
  name: WithSpacing
  nameWithType: FluentExtensions.WithSpacing
  fullName: DrawnUi.Draw.FluentExtensions.WithSpacing
- uid: DrawnUi.Draw.FluentExtensions.WithColumnSpacing*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithColumnSpacing
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithColumnSpacing__1___0_System_Double_
  name: WithColumnSpacing
  nameWithType: FluentExtensions.WithColumnSpacing
  fullName: DrawnUi.Draw.FluentExtensions.WithColumnSpacing
- uid: DrawnUi.Draw.FluentExtensions.WithRowSpacing*
  commentId: Overload:DrawnUi.Draw.FluentExtensions.WithRowSpacing
  href: DrawnUi.Draw.FluentExtensions.html#DrawnUi_Draw_FluentExtensions_WithRowSpacing__1___0_System_Double_
  name: WithRowSpacing
  nameWithType: FluentExtensions.WithRowSpacing
  fullName: DrawnUi.Draw.FluentExtensions.WithRowSpacing
