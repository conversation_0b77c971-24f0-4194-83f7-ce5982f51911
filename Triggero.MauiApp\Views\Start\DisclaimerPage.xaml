﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Start.DisclaimerPage"
             xmlns:app="clr-namespace:Triggero"
              
             xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls" 
             x:Name="this">
    <ContentPage.Content>
        <Grid>


            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"/>

            <StackLayout
                Spacing="0"
                Margin="{x:OnPlatform Android='20,25,20,0',iOS='20,65,20,0'}"
                VerticalOptions="Start">


                <Grid
                    x:Name="pageBackNavigationGrid"
                    VerticalOptions="Start"
                    Margin="0,0,0,30">

                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="44"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ImageButton
                        Command="{Binding Source={x:Reference this},Path=GoBack}"
                        Grid.Column="0"
                        Source="buttonBackBordered.png"
                        WidthRequest="44"
                        HeightRequest="44"
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        BackgroundColor="Transparent"
                        CornerRadius="0"/>

                    <Label 
                        Grid.Column="1"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="17"
                        FontAttributes="Bold"
                        Margin="20,0,0,0"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        WidthRequest="200"
                        Text="Уведомление о политике конфиденциальности"/>

                </Grid>



                <Grid 
                    x:Name="animatedLayout"
                    WidthRequest="100"
                    HeightRequest="74"
                    VerticalOptions="Start"
                    HorizontalOptions="Center">

                    <Image
                        x:Name="notAnimatedImg"
                        WidthRequest="100"
                        HeightRequest="74"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        Source="animationEyeInfo.png"/>

                </Grid>


                <StackLayout
                    Margin="0,30,0,0"
                    Spacing="12">

                    <pancakeview:PancakeView
                        StrokeShape="RoundRectangle 0,16,16,0"
                        VerticalOptions="Start"
                        HeightRequest="126"
                        BackgroundColor="#EBF4FA">
                        <Grid Padding="12,0,20,0">
                            <StackLayout
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                Spacing="8">

                                <Label
                                    Text="Triggero не ставит диагнозы и не является заменой терапии"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontAttributes="Bold"
                                    FontSize="17"/>
                                <Label
                                    Text="Приложение не предназначено для медицинского использования"
                                    TextColor="{x:StaticResource greyColor}"
                                    FontSize="14"/>

                            </StackLayout>
                        </Grid>
                    </pancakeview:PancakeView>

                    <pancakeview:PancakeView
                        StrokeShape="RoundRectangle 0,16,16,0"
                        VerticalOptions="Start"
                        HeightRequest="126"
                        BackgroundColor="#EBF4FA">
                        <Grid Padding="12,0,20,0">
                            <StackLayout
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                Spacing="8">

                                <Label
                                    Text="Все ваши действия конфиденциальны"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontAttributes="Bold"
                                    FontSize="17"/>
                                <Label
                                    Text="Данные полностью зашифрованы и хранятся у нас в безопасности"
                                    TextColor="{x:StaticResource greyColor}"
                                    FontSize="14"/>

                            </StackLayout>
                        </Grid>
                    </pancakeview:PancakeView>

                    <pancakeview:PancakeView
                        StrokeShape="RoundRectangle 0,16,16,0"
                        VerticalOptions="Start"
                        HeightRequest="126"
                        BackgroundColor="#EBF4FA">
                        <Grid Padding="12,0,20,0">
                            <StackLayout
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                Spacing="8">

                                <Label
                                    Text="Приложение не имеет доступа к вашей личной информации"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontAttributes="Bold"
                                    FontSize="17"/>
                                <Label
                                    Text="Данные зашифрованы и анонимны"
                                    TextColor="{x:StaticResource greyColor}"
                                    FontSize="14"/>

                            </StackLayout>
                        </Grid>
                    </pancakeview:PancakeView>

                </StackLayout>


                <Button
                    Command="{Binding Source={x:Reference this},Path=GoNext}"
                    Text="Принимаю"
                    Margin="0,40,0,0"
                    Style="{x:StaticResource yellow_btn}"
                    VerticalOptions="Start"
                    HeightRequest="56"/>

                <StackLayout
                    Spacing="0"
                    Margin="0,32,0,0">

                    <Label
                        HorizontalOptions="Center"
                        Text="Нажимая кнопку “Принимаю” вы подтверждаете, что"
                        TextColor="#4D4D4D"
                        Opacity="0.5"
                        FontSize="8"/>
                    <Label
                        HorizontalOptions="Center"
                        FontSize="8">
                        <Label.FormattedText>
                            <FormattedString>
                                <FormattedString.Spans>
                                    <Span 
                                        TextColor="#A6A6A6"
                                        Text="ознакомились с "/>
                                    <Span
                                        TextColor="#4D4D4D"
                                        TextDecorations="Underline"
                                        Text="условиями использования">
                                        <Span.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToTerms}"/>
                                        </Span.GestureRecognizers>
                                    </Span>
                                    <Span 
                                        TextColor="#A6A6A6"
                                        Text=" и "/>
                                    <Span
                                        TextColor="#4D4D4D"
                                        TextDecorations="Underline"
                                        Text="политикой конфиденциальности">
                                        <Span.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=GoToEULA}"/>
                                        </Span.GestureRecognizers>
                                    </Span>
                                </FormattedString.Spans>
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>

                </StackLayout>

            </StackLayout>
            
            
            
            

        </Grid>
    </ContentPage.Content>
</ContentPage>