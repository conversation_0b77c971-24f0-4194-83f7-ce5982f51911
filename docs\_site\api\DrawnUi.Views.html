<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
      <title>Namespace DrawnUi.Views | DrawnUI for .NET MAUI </title>
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <meta name="title" content="Namespace DrawnUi.Views | DrawnUI for .NET MAUI ">
      
      <meta name="description" content="DrawnUI for .NET MAUI - Rendering engine built on SkiaSharp. Create pixel-perfect cross-platform apps for iOS, Android, Windows, MacCatalyst with advanced animations, gestures, and visual effects.">
      <link rel="icon" href="../favicon.ico">
      <link rel="stylesheet" href="../public/docfx.min.css">
      <link rel="stylesheet" href="../public/main.css">
      <meta name="docfx:navrel" content="../toc.html">
      <meta name="docfx:tocrel" content="toc.html">
      
      <meta name="docfx:rel" content="../">
      
      
      
      <meta name="loc:inThisArticle" content="In this article">
      <meta name="loc:searchResultsCount" content="{count} results for &quot;{query}&quot;">
      <meta name="loc:searchNoResults" content="No results for &quot;{query}&quot;">
      <meta name="loc:tocFilter" content="Filter by title">
      <meta name="loc:nextArticle" content="Next">
      <meta name="loc:prevArticle" content="Previous">
      <meta name="loc:themeLight" content="Light">
      <meta name="loc:themeDark" content="Dark">
      <meta name="loc:themeAuto" content="Auto">
      <meta name="loc:changeTheme" content="Change theme">
      <meta name="loc:copy" content="Copy">
      <meta name="loc:downloadPdf" content="Download PDF">

      <script type="module" src="./../public/docfx.min.js"></script>

      <script>
        const theme = localStorage.getItem('theme') || 'auto'
        document.documentElement.setAttribute('data-bs-theme', theme === 'auto' ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light') : theme)
      </script>

  </head>

  <body class="tex2jax_ignore" data-layout="" data-yaml-mime="ManagedReference">
    <header class="bg-body border-bottom">
      <nav id="autocollapse" class="navbar navbar-expand-md" role="navigation">
        <div class="container-xxl flex-nowrap">
          <a class="navbar-brand" href="../index.html">
            <img id="logo" class="svg" src="../images/drawnuint38.png" alt="">
            
          </a>
          <button class="btn btn-lg d-md-none border-0" type="button" data-bs-toggle="collapse" data-bs-target="#navpanel" aria-controls="navpanel" aria-expanded="false" aria-label="Toggle navigation">
            <i class="bi bi-three-dots"></i>
          </button>
          <div class="collapse navbar-collapse" id="navpanel">
            <div id="navbar">
              <form class="search" role="search" id="search">
                <i class="bi bi-search"></i>
                <input class="form-control" id="search-query" type="search" disabled placeholder="Search" autocomplete="off" aria-label="Search">
              </form>
            </div>
          </div>
        </div>
      </nav>
    </header>

    <main class="container-xxl">
      <div class="toc-offcanvas">
        <div class="offcanvas-md offcanvas-start" tabindex="-1" id="tocOffcanvas" aria-labelledby="tocOffcanvasLabel">
          <div class="offcanvas-header">
            <h5 class="offcanvas-title" id="tocOffcanvasLabel">Table of Contents</h5>
            <button type="button" class="btn-close" data-bs-dismiss="offcanvas" data-bs-target="#tocOffcanvas" aria-label="Close"></button>
          </div>
          <div class="offcanvas-body">
            <nav class="toc" id="toc"></nav>
          </div>
        </div>
      </div>

      <div class="content">
        <div class="actionbar">
          <button class="btn btn-lg border-0 d-md-none" type="button" data-bs-toggle="offcanvas" data-bs-target="#tocOffcanvas" aria-controls="tocOffcanvas" aria-expanded="false" aria-label="Show table of contents">
            <i class="bi bi-list"></i>
          </button>

          <nav id="breadcrumb"></nav>
        </div>

        <article data-uid="DrawnUi.Views">

  <h1 id="DrawnUi_Views" data-uid="DrawnUi.Views" class="text-break">Namespace DrawnUi.Views</h1>
  <div class="markdown level0 summary"></div>
  <div class="markdown level0 conceptual"></div>
  <div class="markdown level0 remarks"></div>

    <h3 id="classes">
Classes
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.BasePageReloadable.html">BasePageReloadable</a></dt>
      <dd><p>Base class for a page with canvas, supports C# HotReload for building UI with code (not XAML).
Override <code>Build()</code>, see examples.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.Canvas.html">Canvas</a></dt>
      <dd><p>Optimized DrawnView having only one child inside Content property. Can autosize to to children size.
For all drawn app put this directly inside the ContentPage as root view.
If you put this inside some Maui control like Grid whatever expect more GC collections during animations making them somewhat less fluid.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.DisposableManager.html">DisposableManager</a></dt>
      <dd><p>Manages delayed disposal of IDisposable objects based on frame count</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.DrawnUiBasePage.html">DrawnUiBasePage</a></dt>
      <dd><p>Actually used to: respond to keyboard resizing on mobile and keyboard key presses on Mac. Other than for that this
is not needed at all.</p>
</dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.DrawnView.html">DrawnView</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.DrawnView.DiagnosticData.html">DrawnView.DiagnosticData</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.DrawnView.FocusedItemChangedArgs.html">DrawnView.FocusedItemChangedArgs</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.DrawnView.OffscreenCommand.html">DrawnView.OffscreenCommand</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.SkiaView.html">SkiaView</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.SkiaViewAccelerated.html">SkiaViewAccelerated</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.SurfaceCacheManager.html">SurfaceCacheManager</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.SurfacePool.html">SurfacePool</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.SurfaceStats.html">SurfaceStats</a></dt>
      <dd></dd>
    </dl>
    <h3 id="structs">
Structs
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.DrawnView.TimedDisposable.html">DrawnView.TimedDisposable</a></dt>
      <dd></dd>
    </dl>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.SurfaceKey.html">SurfaceKey</a></dt>
      <dd></dd>
    </dl>
    <h3 id="interfaces">
Interfaces
</h3>
    <dl class="jumplist">
      <dt><a class="xref" href="DrawnUi.Views.IDisposeManager.html">IDisposeManager</a></dt>
      <dd></dd>
    </dl>


</article>

        <div class="contribution d-print-none">
        </div>


      </div>

      <div class="affix">
        <nav id="affix"></nav>
      </div>
    </main>

    <div class="container-xxl search-results" id="search-results"></div>

    <footer class="border-top text-secondary">
      <div class="container-xxl">
        <div class="flex-fill">
          Made by <a href="https://taublast.github.io/about/">Nick Kovalsky aka AppoMobi (@taublast)</a>
        </div>
      </div>
    </footer>
  </body>
</html>
