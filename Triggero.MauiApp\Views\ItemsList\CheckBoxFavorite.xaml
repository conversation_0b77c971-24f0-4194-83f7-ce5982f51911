﻿<?xml version="1.0" encoding="UTF-8" ?>
<draw:SkiaCheckbox
    x:Class="Triggero.MauiMobileApp.Controls.Cards.TasksForToday.CheckBoxFavorite"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    HorizontalOptions="Fill"
    UseCache="Image"
    VerticalOptions="Fill">

    <draw:SkiaImage
        TranslationY="-24"
        Aspect="AspectFit"
        HeightRequest="20"
        HorizontalOptions="Center"
        Source="likeUnset.png"
        Tag="FrameOff"
        VerticalOptions="Center"
        WidthRequest="20" />

    <draw:SkiaImage
        TranslationY="-24"
        Aspect="AspectFit"
        BackgroundColor="Transparent"
        HeightRequest="20"
        HorizontalOptions="Center"
        Source="likeSet.png"
        Tag="FrameOn"
        VerticalOptions="Center"
        WidthRequest="20" />

</draw:SkiaCheckbox>