﻿<?xml version="1.0" encoding="UTF-8"?>
<models:BaseQuestionView
             
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:models="clr-namespace:Triggero.MauiPort.Models"
             x:Class="Triggero.Controls.Cards.Tests.Questions.PicturesQuestionView">
    <models:BaseQuestionView.Content>

        <StackLayout Spacing="15">

            <Label 
                x:Name="titleLabel"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                TextColor="{x:StaticResource greyTextColor}"
                FontSize="22"/>

            <Label 
                Margin="0,-7,0,0"
                x:Name="questiontypeLabel"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                TextColor="{x:StaticResource greyTextColor}"
                Opacity="0.5"
                FontSize="14"/>

            <FlexLayout
                Direction="Row"
                Margin="0,40,0,0"
                x:Name="optionsLayout">



            </FlexLayout>

        </StackLayout>
        
       
    </models:BaseQuestionView.Content>
</models:BaseQuestionView>