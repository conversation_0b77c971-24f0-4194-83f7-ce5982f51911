﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Chat.ChatLeftTextMessageWithAdvice">
  <ContentView.Content>
      <Frame
          CornerRadius="12"
          BackgroundColor="#EEF5FB"
          HasShadow="False"
          Padding="0">
          
            <StackLayout>
                <Label 
                    TextColor="{x:StaticResource greyTextColor}"
                    FontSize="17"
                    Margin="20,8,0,0"
                    VerticalOptions="Start"
                    HorizontalOptions="Start"
                    Text="Привет, Кристина!"/>

                <StackLayout x:Name="adviceStackLayout">

                </StackLayout>
            </StackLayout>
      </Frame>
  </ContentView.Content>
</ContentView>