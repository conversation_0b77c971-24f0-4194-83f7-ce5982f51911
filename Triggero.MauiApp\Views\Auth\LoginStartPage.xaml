﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.LoginStartPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this">
    <ContentPage.Content>
        <Grid>

            <Image
                Aspect="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"
                VerticalOptions="Fill" />


            <StackLayout
                Spacing="0"
                VerticalOptions="Center">

                <Image
                    HeightRequest="160"
                    HorizontalOptions="Center"
                    Source="mainLogo.png"
                    VerticalOptions="Start"
                    WidthRequest="160" />


                <!--<Label
                    IsVisible="False"
                    x:Name="hiName"
                    Margin="0,30,0,0"
                    TextColor="{x:StaticResource greyTextColor}"
                    FontSize="17"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    Text="Привет, Кристина"/>


                <Label
                    IsVisible="False"
                    Margin="0,12,0,0"
                    TextColor="{x:StaticResource greyTextColor}"
                    FontSize="22"
                    FontAttributes="Bold"
                    VerticalOptions="Start"
                    HorizontalOptions="Center"
                    Text="{Binding Source={x:Static app:App.This},Path=Interface.Auth.LoginMainPage.Welcome}"/>-->


                <StackLayout
                    Margin="20,60,20,0"
                    HorizontalOptions="Fill"
                    Spacing="16">

                    <Button
                        Command="{Binding Source={x:Reference this}, Path=GoToEmail}"
                        HeightRequest="56"
                        Style="{x:StaticResource yellow_btn}"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginMainPage.SignInViaEmail}"
                        VerticalOptions="Start" />

                    <Button
                        Command="{Binding Source={x:Reference this}, Path=GoToPhone}"
                        HeightRequest="56"
                        Style="{x:StaticResource grey_cornered_btn}"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginMainPage.SignInViaPhone}"
                        VerticalOptions="Start" />

                </StackLayout>


                <StackLayout
                    Margin="0,140,0,0"
                    HorizontalOptions="Center"
                    Orientation="Horizontal"
                    Spacing="12">


                    <Label
                        FontSize="14"

                        Opacity="0.5"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginMainPage.DontHaveAccount}"
                        TextColor="{x:StaticResource greyTextColor}" />

                    <Label
                        FontAttributes="Bold"
                        FontSize="14"

                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.LoginMainPage.SignUp}"
                        TextColor="{x:StaticResource greyTextColor}"
                        TextDecorations="Underline">
                        <Label.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToRegistration}" />
                        </Label.GestureRecognizers>
                    </Label>

                </StackLayout>

            </StackLayout>


        </Grid>
    </ContentPage.Content>
</ContentPage>