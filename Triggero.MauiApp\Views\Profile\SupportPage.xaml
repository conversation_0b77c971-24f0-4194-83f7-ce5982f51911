﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             BackgroundColor="White"
             xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             xmlns:androidSpecific="clr-namespace:Microsoft.Maui.Controls.PlatformConfiguration.AndroidSpecific;assembly=Microsoft.Maui.Controls"
             androidSpecific:Application.WindowSoftInputModeAdjust="Resize"
             x:Class="Triggero.MauiMobileApp.Views.Pages.SupportPage">
    <ContentPage.Content>
        
        <Grid Margin="20,0,20,0">
            
            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="90"/>
                <RowDefinition Height="*"/>
                <RowDefinition x:Name="sendMsgRowDefinition" Height="100"/>
            </Grid.RowDefinitions>

            <Grid Grid.Row="1">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="50"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <Image
                        WidthRequest="47"
                        HeightRequest="47"
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        Source="chatBotAvatar.png"/>
                </Grid>

                <Grid Grid.Column="1">
                    <StackLayout 
                        Spacing="0"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        Margin="15,0,0,0">

                        <Label 
                            TextColor="{x:StaticResource greyTextColor}"
                            FontSize="17"
                            FontAttributes="Bold"
                            Margin="0,0,0,0"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Support.TriggeroSupport}"/>
                       

                        <StackLayout
                            Spacing="4"
                            VerticalOptions="Center"
                            HorizontalOptions="Start"
                            Orientation="Horizontal">
                            <Frame 
                                WidthRequest="8"
                                HeightRequest="8"
                                CornerRadius="4"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Padding="0"
                                HasShadow="False"
                                Background="#34C759"/>

                            <Label 
                                TextColor="{x:StaticResource greyTextColor}"
                                Opacity="0.5"
                                FontSize="12"
                                Margin="0,0,0,0"
                                VerticalOptions="Center"
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Support.Online}"/>
                        </StackLayout>

                    </StackLayout>
                </Grid>

                <ImageButton 
                    Command="{Binding Source={x:Reference this},Path=Close}"
                    Grid.Column="1"
                    CornerRadius="0"
                    WidthRequest="14"
                    HeightRequest="14"
                    Source="close.png"
                    Margin="0,0,0,21"
                    HorizontalOptions="End"
                    VerticalOptions="Center"
                    BackgroundColor="Transparent"/>

            </Grid>

            <Grid Grid.Row="2">
                <ScrollView x:Name="messagesScrollView">
                    <StackLayout x:Name="messagesLayout">

                    </StackLayout>
                </ScrollView>
            </Grid>

            <Grid Grid.Row="3">

                <Grid
                    VerticalOptions="Start"
                    HeightRequest="100">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="45"/>
                    </Grid.ColumnDefinitions>
                    
                    <Editor
                        Focused="textEditFocused"
                        Unfocused="textEditUnfocused"
                        Text="{Binding Source={x:Reference this},Path=Message,Mode=TwoWay}"
                        Style="{x:StaticResource grayTextEditor}"
                        HeightRequest="40"
                        VerticalOptions="Center"
                        HorizontalOptions="Fill"
                        Placeholder="{Binding Source={x:Static mobile:App.This},Path=Interface.Support.MessagePlaceholder}">
                    </Editor>

                    <ImageButton
                        Command="{Binding Source={x:Reference this},Path=SendMessage}"
                        Grid.Column="1"
                        Source="chatSendBtn.png"
                        BackgroundColor="Transparent"
                        CornerRadius="0"
                        HeightRequest="39"
                        WidthRequest="39"/>
                </Grid>

            </Grid>

        </Grid>
        
    </ContentPage.Content>
</ContentPage>