﻿using MobileAPIWrapper.Helpers;
using Newtonsoft.Json;
using MobileAPIWrapper.Helpers;
using Triggero.Domain.Enums;
using Triggero.Models;
using Triggero.Models.General.UserData;
using Triggero.Models.Practices;
using Triggero.Models.Tests;
 
namespace MobileAPIWrapper.Methods.General.Users
{
    public class UserFavoritesMethods
    {
        private string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("UserFavorites/");

        #region Получение избранных

        public async Task<UserFavorites> GetUserFavoritesAll(int id)
        {
            string url = BASE_HOST + $"GetUserFavoritesAll?id={id}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<UserFavorites>(response.Content);
            return obj;
        }
        public async Task<List<Test>> GetUserFavoriteTests(int id)
        {
            string url = BASE_HOST + $"GetUserFavoriteTests?id={id}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<Test>>(str);
            return obj;
        }
        public async Task<List<Topic>> GetUserFavoriteTopics(int id)
        {
            string url = BASE_HOST + $"GetUserFavoriteTopics?id={id}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Topic>>(response.Content);
            return obj;
        }
        public async Task<List<Practice>> GetUserFavoritePractices(int id)
        {
            string url = BASE_HOST + $"GetUserFavoritePractices?id={id}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Practice>>(response.Content);
            return obj;
        }
        public async Task<List<Exercise>> GetUserFavoriteExercises(int id)
        {
            string url = BASE_HOST + $"GetUserFavoriteExercises?id={id}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            try
            {
                var obj = JsonConvert.DeserializeObject<List<Exercise>>(response.Content);
                return obj;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
                throw;
            }

        }
        #endregion

        #region Добавление/удаление из избранных

        public async Task<bool> SetFavorite(int userId, AppContentType content, int id, bool value)
        {
            string url = BASE_HOST + $"SetFavorite?userId={userId}&id={id}&content={content}&value={value}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Post);
            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }

        public async Task<bool> ToggleFavoriteExercise(int userId, int exerciseId)
        {
            string url = BASE_HOST + $"ToggleFavoriteExercise?userId={userId}&exerciseId={exerciseId}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Put);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
        public async Task<bool> ToggleFavoritePractice(int userId, int practiceId)
        {
            string url = BASE_HOST + $"ToggleFavoritePractice?userId={userId}&practiceId={practiceId}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Put);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
        public async Task<bool> ToggleFavoriteTopic(int userId, int topicId)
        {
            string url = BASE_HOST + $"ToggleFavoriteTopic?userId={userId}&topicId={topicId}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Put);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
        public async Task<bool> ToggleFavoriteTest(int userId, int testId)
        {
            string url = BASE_HOST + $"ToggleFavoriteTest?userId={userId}&testId={testId}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Put);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
        #endregion

        #region Проверка наличия в избранных
        public async Task<bool> HasFavoriteExercise(int userId, int exerciseId)
        {
            string url = BASE_HOST + $"HasFavoriteExercise?userId={userId}&exerciseId={exerciseId}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
        public async Task<bool> HasFavoritePractice(int userId, int practiceId)
        {
            string url = BASE_HOST + $"HasFavoritePractice?userId={userId}&practiceId={practiceId}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
        public async Task<bool> HasFavoriteTopic(int userId, int topicId)
        {
            string url = BASE_HOST + $"HasFavoriteTopic?userId={userId}&topicId={topicId}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
        public async Task<bool> HasFavoriteTest(int userId, int testId)
        {
            string url = BASE_HOST + $"HasFavoriteTest?userId={userId}&testId={testId}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
        #endregion
    }
}
