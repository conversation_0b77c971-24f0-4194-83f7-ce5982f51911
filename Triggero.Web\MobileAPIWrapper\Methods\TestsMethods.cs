﻿using Triggero.Domain.Models;
using Newtonsoft.Json;
using MobileAPIWrapper.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.Tests;

namespace MobileAPIWrapper.Methods
{
    public class TestsMethods
    {
        public static string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("Tests/");
        public async Task<List<TestCategory>> GetTestCategories()
        {
            string url = BASE_HOST + "GetTestCategories";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<TestCategory>>(response.Content);
            return obj;
        }
        public async Task<TestsSelectionModel> GetTests(int count, int offset)
        {
            string url = BASE_HOST + $"GetTests?count={count}&offset={offset}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<TestsSelectionModel>(response.Content);
            return obj;
        }


        public async Task<List<Test>> GetRandomTests(int count)
        {
            string url = BASE_HOST + $"GetRandomTests?count={count}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<Test>>(str);
            return obj;
        }


        public async Task<List<Test>> GetTestsByCategory(int categoryId)
        {
            string url = BASE_HOST + $"GetTestsByCategory?categoryId={categoryId}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<Test>>(str);
            return obj;
        }
        public async Task<List<Test>> GetTestsByCategoryChunk(int categoryId, int count, int offset)
        {
            string url = BASE_HOST + $"GetTestsByCategoryChunk?categoryId={categoryId}&count={count}&offset={offset}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<Test>>(str);
            return obj;
        }


        public async Task<List<Test>> GetTestsByTag(string tag)
        {
            string url = BASE_HOST + $"GetTestsByTag?tag={tag}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<Test>>(str);
            return obj;
        }












        public async Task<Test> GetTest(int id)
        {
            string url = BASE_HOST + $"GetTest?id={id}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<Test>(str);
            return obj;
        }
        public async Task<Test> GetTutorialTest()
        {
            string url = BASE_HOST + $"GetTutorialTest";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<Test>(str);
            return obj;
        }



        public async Task AddWatch(int testId)
        {
            string url = BASE_HOST + $"AddWatch?testId={testId}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Put);

        }
    }
}
