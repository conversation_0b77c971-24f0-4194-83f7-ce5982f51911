﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             
             xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
             x:Class="Triggero.Controls.Cards.Tracker.Influence.InfluenceCard">
  <ContentView.Content>

        <Frame
               Padding="0"
               HasShadow="False"
               CornerRadius="15"
               BackgroundColor="White">
            <Frame.Shadow>
                <Shadow Brush="#27527A"
                        Offset="2,2"
                        Radius="12"
                        Opacity="0.06" />
            </Frame.Shadow>
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"/>
                        <ColumnDefinition Width="3*"/>
                    </Grid.ColumnDefinitions>

                    <Grid Grid.Column="0">

                        <Frame
                            HorizontalOptions="Start"
                            VerticalOptions="Start"
                            HasShadow="False"
                            Padding="0"
                            HeightRequest="88"
                            WidthRequest="80"
                            Background="#F8F9FC"
                            CornerRadius="10"
                            Margin="10,10,0,0">
                            <Grid>
                                <StackLayout
                                    Spacing="7"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center">
                                    <Image 
                                        x:Name="img"
                                        WidthRequest="48"
                                        HeightRequest="48"
                                        HorizontalOptions="Center"
                                        VerticalOptions="Center"/>
                                    <Label 
                                        x:Name="titleLabel"
                                        TextColor="#000000"
                                        FontSize="{x:OnPlatform Android=12,iOS=14}"
                                        FontFamily="FontTextLight"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Center"
                                        HorizontalTextAlignment="Center"/>
                                </StackLayout>
                            </Grid>
                        </Frame>

                    </Grid>

                    <Grid Grid.Column="1">

                        <StackLayout
                            Spacing="6"
                            Margin="5,10,0,0"
                            x:Name="layout">


                        </StackLayout>

                    </Grid>

                </Grid>
        </Frame>
    </ContentView.Content>
</ContentView>