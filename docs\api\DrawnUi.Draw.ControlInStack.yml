### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ControlInStack
  commentId: T:DrawnUi.Draw.ControlInStack
  id: ControlInStack
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ControlInStack.#ctor
  - DrawnUi.Draw.ControlInStack.Area
  - DrawnUi.Draw.ControlInStack.Column
  - DrawnUi.Draw.ControlInStack.ControlIndex
  - DrawnUi.Draw.ControlInStack.Destination
  - DrawnUi.Draw.ControlInStack.Drawn
  - DrawnUi.Draw.ControlInStack.IsCollapsed
  - DrawnUi.Draw.ControlInStack.IsVisible
  - DrawnUi.Draw.ControlInStack.Layout
  - DrawnUi.Draw.ControlInStack.Measured
  - DrawnUi.Draw.ControlInStack.Offset
  - DrawnUi.Draw.ControlInStack.OffsetOthers
  - DrawnUi.Draw.ControlInStack.Row
  - DrawnUi.Draw.ControlInStack.View
  - DrawnUi.Draw.ControlInStack.WasLastDrawn
  - DrawnUi.Draw.ControlInStack.WasMeasured
  - DrawnUi.Draw.ControlInStack.ZIndex
  langs:
  - csharp
  - vb
  name: ControlInStack
  nameWithType: ControlInStack
  fullName: DrawnUi.Draw.ControlInStack
  type: Class
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ControlInStack
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class ControlInStack
    content.vb: Public Class ControlInStack
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ControlInStack.#ctor
  commentId: M:DrawnUi.Draw.ControlInStack.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: ControlInStack()
  nameWithType: ControlInStack.ControlInStack()
  fullName: DrawnUi.Draw.ControlInStack.ControlInStack()
  type: Constructor
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 9
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ControlInStack()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.ControlInStack.#ctor*
  nameWithType.vb: ControlInStack.New()
  fullName.vb: DrawnUi.Draw.ControlInStack.New()
  name.vb: New()
- uid: DrawnUi.Draw.ControlInStack.ControlIndex
  commentId: P:DrawnUi.Draw.ControlInStack.ControlIndex
  id: ControlIndex
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: ControlIndex
  nameWithType: ControlInStack.ControlIndex
  fullName: DrawnUi.Draw.ControlInStack.ControlIndex
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ControlIndex
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 19
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Index inside enumerator that was passed for measurement OR index inside ItemsSource
  example: []
  syntax:
    content: public int ControlIndex { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property ControlIndex As Integer
  overload: DrawnUi.Draw.ControlInStack.ControlIndex*
- uid: DrawnUi.Draw.ControlInStack.Measured
  commentId: P:DrawnUi.Draw.ControlInStack.Measured
  id: Measured
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: Measured
  nameWithType: ControlInStack.Measured
  fullName: DrawnUi.Draw.ControlInStack.Measured
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Measured
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Measure result
  example: []
  syntax:
    content: public ScaledSize Measured { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.ScaledSize
    content.vb: Public Property Measured As ScaledSize
  overload: DrawnUi.Draw.ControlInStack.Measured*
- uid: DrawnUi.Draw.ControlInStack.Layout
  commentId: P:DrawnUi.Draw.ControlInStack.Layout
  id: Layout
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: Layout
  nameWithType: ControlInStack.Layout
  fullName: DrawnUi.Draw.ControlInStack.Layout
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Layout
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 26
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKRect Layout { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property Layout As SKRect
  overload: DrawnUi.Draw.ControlInStack.Layout*
- uid: DrawnUi.Draw.ControlInStack.Area
  commentId: P:DrawnUi.Draw.ControlInStack.Area
  id: Area
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: Area
  nameWithType: ControlInStack.Area
  fullName: DrawnUi.Draw.ControlInStack.Area
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Area
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 31
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Available area for Arrange
  example: []
  syntax:
    content: public SKRect Area { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property Area As SKRect
  overload: DrawnUi.Draw.ControlInStack.Area*
- uid: DrawnUi.Draw.ControlInStack.Destination
  commentId: P:DrawnUi.Draw.ControlInStack.Destination
  id: Destination
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: Destination
  nameWithType: ControlInStack.Destination
  fullName: DrawnUi.Draw.ControlInStack.Destination
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Destination
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 36
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: PIXELS, this is to hold our arranged layout
  example: []
  syntax:
    content: public SKRect Destination { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKRect
    content.vb: Public Property Destination As SKRect
  overload: DrawnUi.Draw.ControlInStack.Destination*
- uid: DrawnUi.Draw.ControlInStack.OffsetOthers
  commentId: P:DrawnUi.Draw.ControlInStack.OffsetOthers
  id: OffsetOthers
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: OffsetOthers
  nameWithType: ControlInStack.OffsetOthers
  fullName: DrawnUi.Draw.ControlInStack.OffsetOthers
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OffsetOthers
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 38
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector2 OffsetOthers { get; set; }
    parameters: []
    return:
      type: System.Numerics.Vector2
    content.vb: Public Property OffsetOthers As Vector2
  overload: DrawnUi.Draw.ControlInStack.OffsetOthers*
- uid: DrawnUi.Draw.ControlInStack.WasLastDrawn
  commentId: P:DrawnUi.Draw.ControlInStack.WasLastDrawn
  id: WasLastDrawn
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: WasLastDrawn
  nameWithType: ControlInStack.WasLastDrawn
  fullName: DrawnUi.Draw.ControlInStack.WasLastDrawn
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WasLastDrawn
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 43
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Was drawn during the last frame
  example: []
  syntax:
    content: public bool WasLastDrawn { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property WasLastDrawn As Boolean
  overload: DrawnUi.Draw.ControlInStack.WasLastDrawn*
- uid: DrawnUi.Draw.ControlInStack.View
  commentId: P:DrawnUi.Draw.ControlInStack.View
  id: View
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: View
  nameWithType: ControlInStack.View
  fullName: DrawnUi.Draw.ControlInStack.View
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: View
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 48
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: This will be null for recycled views
  example: []
  syntax:
    content: public SkiaControl View { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Property View As SkiaControl
  overload: DrawnUi.Draw.ControlInStack.View*
- uid: DrawnUi.Draw.ControlInStack.Drawn
  commentId: P:DrawnUi.Draw.ControlInStack.Drawn
  id: Drawn
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: Drawn
  nameWithType: ControlInStack.Drawn
  fullName: DrawnUi.Draw.ControlInStack.Drawn
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Drawn
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 53
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Was used for actual drawing
  example: []
  syntax:
    content: public DrawingRect Drawn { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.DrawingRect
    content.vb: Public Property Drawn As DrawingRect
  overload: DrawnUi.Draw.ControlInStack.Drawn*
- uid: DrawnUi.Draw.ControlInStack.Offset
  commentId: P:DrawnUi.Draw.ControlInStack.Offset
  id: Offset
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: Offset
  nameWithType: ControlInStack.Offset
  fullName: DrawnUi.Draw.ControlInStack.Offset
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Offset
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 58
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: For internal use by your custom controls
  example: []
  syntax:
    content: public Vector2 Offset { get; set; }
    parameters: []
    return:
      type: System.Numerics.Vector2
    content.vb: Public Property Offset As Vector2
  overload: DrawnUi.Draw.ControlInStack.Offset*
- uid: DrawnUi.Draw.ControlInStack.WasMeasured
  commentId: P:DrawnUi.Draw.ControlInStack.WasMeasured
  id: WasMeasured
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: WasMeasured
  nameWithType: ControlInStack.WasMeasured
  fullName: DrawnUi.Draw.ControlInStack.WasMeasured
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: WasMeasured
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 60
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool WasMeasured { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property WasMeasured As Boolean
  overload: DrawnUi.Draw.ControlInStack.WasMeasured*
- uid: DrawnUi.Draw.ControlInStack.IsVisible
  commentId: P:DrawnUi.Draw.ControlInStack.IsVisible
  id: IsVisible
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: IsVisible
  nameWithType: ControlInStack.IsVisible
  fullName: DrawnUi.Draw.ControlInStack.IsVisible
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsVisible
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 62
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsVisible { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsVisible As Boolean
  overload: DrawnUi.Draw.ControlInStack.IsVisible*
- uid: DrawnUi.Draw.ControlInStack.ZIndex
  commentId: P:DrawnUi.Draw.ControlInStack.ZIndex
  id: ZIndex
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: ZIndex
  nameWithType: ControlInStack.ZIndex
  fullName: DrawnUi.Draw.ControlInStack.ZIndex
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ZIndex
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 75
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int ZIndex { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property ZIndex As Integer
  overload: DrawnUi.Draw.ControlInStack.ZIndex*
- uid: DrawnUi.Draw.ControlInStack.Column
  commentId: P:DrawnUi.Draw.ControlInStack.Column
  id: Column
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: Column
  nameWithType: ControlInStack.Column
  fullName: DrawnUi.Draw.ControlInStack.Column
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Column
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 77
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int Column { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Column As Integer
  overload: DrawnUi.Draw.ControlInStack.Column*
- uid: DrawnUi.Draw.ControlInStack.Row
  commentId: P:DrawnUi.Draw.ControlInStack.Row
  id: Row
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: Row
  nameWithType: ControlInStack.Row
  fullName: DrawnUi.Draw.ControlInStack.Row
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Row
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 79
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int Row { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Row As Integer
  overload: DrawnUi.Draw.ControlInStack.Row*
- uid: DrawnUi.Draw.ControlInStack.IsCollapsed
  commentId: P:DrawnUi.Draw.ControlInStack.IsCollapsed
  id: IsCollapsed
  parent: DrawnUi.Draw.ControlInStack
  langs:
  - csharp
  - vb
  name: IsCollapsed
  nameWithType: ControlInStack.IsCollapsed
  fullName: DrawnUi.Draw.ControlInStack.IsCollapsed
  type: Property
  source:
    remote:
      path: src/Shared/Draw/Internals/Models/ControlInStack.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsCollapsed
    path: ../src/Shared/Draw/Internals/Models/ControlInStack.cs
    startLine: 84
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Cell's own visibility state (independent of viewport visibility)
  example: []
  syntax:
    content: public bool IsCollapsed { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsCollapsed As Boolean
  overload: DrawnUi.Draw.ControlInStack.IsCollapsed*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ControlInStack.#ctor*
  commentId: Overload:DrawnUi.Draw.ControlInStack.#ctor
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack__ctor
  name: ControlInStack
  nameWithType: ControlInStack.ControlInStack
  fullName: DrawnUi.Draw.ControlInStack.ControlInStack
  nameWithType.vb: ControlInStack.New
  fullName.vb: DrawnUi.Draw.ControlInStack.New
  name.vb: New
- uid: DrawnUi.Draw.ControlInStack.ControlIndex*
  commentId: Overload:DrawnUi.Draw.ControlInStack.ControlIndex
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_ControlIndex
  name: ControlIndex
  nameWithType: ControlInStack.ControlIndex
  fullName: DrawnUi.Draw.ControlInStack.ControlIndex
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.ControlInStack.Measured*
  commentId: Overload:DrawnUi.Draw.ControlInStack.Measured
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_Measured
  name: Measured
  nameWithType: ControlInStack.Measured
  fullName: DrawnUi.Draw.ControlInStack.Measured
- uid: DrawnUi.Draw.ScaledSize
  commentId: T:DrawnUi.Draw.ScaledSize
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ScaledSize.html
  name: ScaledSize
  nameWithType: ScaledSize
  fullName: DrawnUi.Draw.ScaledSize
- uid: DrawnUi.Draw.ControlInStack.Layout*
  commentId: Overload:DrawnUi.Draw.ControlInStack.Layout
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_Layout
  name: Layout
  nameWithType: ControlInStack.Layout
  fullName: DrawnUi.Draw.ControlInStack.Layout
- uid: SkiaSharp.SKRect
  commentId: T:SkiaSharp.SKRect
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  name: SKRect
  nameWithType: SKRect
  fullName: SkiaSharp.SKRect
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.ControlInStack.Area*
  commentId: Overload:DrawnUi.Draw.ControlInStack.Area
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_Area
  name: Area
  nameWithType: ControlInStack.Area
  fullName: DrawnUi.Draw.ControlInStack.Area
- uid: DrawnUi.Draw.ControlInStack.Destination*
  commentId: Overload:DrawnUi.Draw.ControlInStack.Destination
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_Destination
  name: Destination
  nameWithType: ControlInStack.Destination
  fullName: DrawnUi.Draw.ControlInStack.Destination
- uid: DrawnUi.Draw.ControlInStack.OffsetOthers*
  commentId: Overload:DrawnUi.Draw.ControlInStack.OffsetOthers
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_OffsetOthers
  name: OffsetOthers
  nameWithType: ControlInStack.OffsetOthers
  fullName: DrawnUi.Draw.ControlInStack.OffsetOthers
- uid: System.Numerics.Vector2
  commentId: T:System.Numerics.Vector2
  parent: System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  name: Vector2
  nameWithType: Vector2
  fullName: System.Numerics.Vector2
- uid: System.Numerics
  commentId: N:System.Numerics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Numerics
  nameWithType: System.Numerics
  fullName: System.Numerics
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Numerics
    name: Numerics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics
- uid: DrawnUi.Draw.ControlInStack.WasLastDrawn*
  commentId: Overload:DrawnUi.Draw.ControlInStack.WasLastDrawn
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_WasLastDrawn
  name: WasLastDrawn
  nameWithType: ControlInStack.WasLastDrawn
  fullName: DrawnUi.Draw.ControlInStack.WasLastDrawn
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.ControlInStack.View*
  commentId: Overload:DrawnUi.Draw.ControlInStack.View
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_View
  name: View
  nameWithType: ControlInStack.View
  fullName: DrawnUi.Draw.ControlInStack.View
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: DrawnUi.Draw.ControlInStack.Drawn*
  commentId: Overload:DrawnUi.Draw.ControlInStack.Drawn
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_Drawn
  name: Drawn
  nameWithType: ControlInStack.Drawn
  fullName: DrawnUi.Draw.ControlInStack.Drawn
- uid: DrawnUi.Draw.DrawingRect
  commentId: T:DrawnUi.Draw.DrawingRect
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.DrawingRect.html
  name: DrawingRect
  nameWithType: DrawingRect
  fullName: DrawnUi.Draw.DrawingRect
- uid: DrawnUi.Draw.ControlInStack.Offset*
  commentId: Overload:DrawnUi.Draw.ControlInStack.Offset
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_Offset
  name: Offset
  nameWithType: ControlInStack.Offset
  fullName: DrawnUi.Draw.ControlInStack.Offset
- uid: DrawnUi.Draw.ControlInStack.WasMeasured*
  commentId: Overload:DrawnUi.Draw.ControlInStack.WasMeasured
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_WasMeasured
  name: WasMeasured
  nameWithType: ControlInStack.WasMeasured
  fullName: DrawnUi.Draw.ControlInStack.WasMeasured
- uid: DrawnUi.Draw.ControlInStack.IsVisible*
  commentId: Overload:DrawnUi.Draw.ControlInStack.IsVisible
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_IsVisible
  name: IsVisible
  nameWithType: ControlInStack.IsVisible
  fullName: DrawnUi.Draw.ControlInStack.IsVisible
- uid: DrawnUi.Draw.ControlInStack.ZIndex*
  commentId: Overload:DrawnUi.Draw.ControlInStack.ZIndex
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_ZIndex
  name: ZIndex
  nameWithType: ControlInStack.ZIndex
  fullName: DrawnUi.Draw.ControlInStack.ZIndex
- uid: DrawnUi.Draw.ControlInStack.Column*
  commentId: Overload:DrawnUi.Draw.ControlInStack.Column
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_Column
  name: Column
  nameWithType: ControlInStack.Column
  fullName: DrawnUi.Draw.ControlInStack.Column
- uid: DrawnUi.Draw.ControlInStack.Row*
  commentId: Overload:DrawnUi.Draw.ControlInStack.Row
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_Row
  name: Row
  nameWithType: ControlInStack.Row
  fullName: DrawnUi.Draw.ControlInStack.Row
- uid: DrawnUi.Draw.ControlInStack.IsCollapsed*
  commentId: Overload:DrawnUi.Draw.ControlInStack.IsCollapsed
  href: DrawnUi.Draw.ControlInStack.html#DrawnUi_Draw_ControlInStack_IsCollapsed
  name: IsCollapsed
  nameWithType: ControlInStack.IsCollapsed
  fullName: DrawnUi.Draw.ControlInStack.IsCollapsed
