﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:gauge="clr-namespace:Syncfusion.Maui.Gauges;assembly=Syncfusion.Maui.Gauges"
             
             x:Name="this"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Class="Triggero.Controls.MoodTracker.TrackerMoodGauge">
    <ContentView.Content>

        <StackLayout>
            <Label
                            Margin="0,13,0,0"
                            TextColor="{x:StaticResource greyTextColor}"
                            HorizontalOptions="Start"
                            FontSize="17"
                            FontAttributes="Bold"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerMainPage.MoodGauge}"/>
            <Label
                            TextColor="{x:StaticResource ColorTextGray}"
                            HorizontalOptions="Start"
                            FontSize="12"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerMainPage.MoodGaugeDescription}"/>

            <gauge:SfRadialGauge
                            Margin="0,25,0,0"
                            HeightRequest="330"
                            VerticalOptions="Start"
                            HorizontalOptions="Fill">
                <gauge:SfRadialGauge.Axes>
                    <gauge:RadialAxis 
                                    Minimum="0" 
                                    Maximum="5" 
                                    StartAngle="180" 
                                    EndAngle="360" 
                                    Interval="1"
			                        ShowLabels="False" 
                                    ShowTicks="False"
                                    ShowAxisLine="False" 
                                    RadiusFactor="1">
                        <gauge:RadialAxis.Pointers>
                            <gauge:NeedlePointer  
                                            x:Name="arrow"
                                            Value="2.5" 
                                            KnobStroke="#007DD1"           
                                            KnobStrokeThickness="5" 
                                            KnobFill="White" 
                                            NeedleLengthUnit="Factor"
                                            NeedleLength="0.66"
                                            NeedleStartWidth="13"
                                            NeedleEndWidth="13"
                                            NeedleFill="#007DD1"   
                                            TailWidth="10"
                                            KnobRadius="0.6"
                                            KnobSizeUnit ="Factor"/>
                        </gauge:RadialAxis.Pointers>
                        <gauge:RadialAxis.Ranges>
                            <gauge:RadialRange 
                                            StartValue="0" 
                                            EndValue="5"
                                            StartWidth="30"
                                            EndWidth="30" 
                                            OffsetUnit="Factor"
                                            RangeOffset="1.0">
                                <gauge:RadialRange.GradientStops>
                                    <gauge:GaugeGradientStop Value="0" Color="#02C0CD"/>
                                    <gauge:GaugeGradientStop Value="1" Color="#7AD1E2"/>
                                    <gauge:GaugeGradientStop Value="2" Color="#AEDCEE"/>
                                    <gauge:GaugeGradientStop Value="3" Color="#CFE4F7"/>
                                    <gauge:GaugeGradientStop Value="4" Color="#F3D7A8"/>
                                    <gauge:GaugeGradientStop Value="5" Color="#FFCD62"/>
                                </gauge:RadialRange.GradientStops>
                            </gauge:RadialRange>
                        </gauge:RadialAxis.Ranges>
                    </gauge:RadialAxis>
                </gauge:SfRadialGauge.Axes>
            </gauge:SfRadialGauge>

            <Grid
                            Margin="0,-145,0,0"
                            VerticalOptions="Start"
                            HeightRequest="40">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                    <ColumnDefinition Width="1*"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <Image
                                    Source="dayMood6.png"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    WidthRequest="35"
                                    HeightRequest="35"/>
                </Grid>

                <Grid Grid.Column="1">
                    <Image
                                    Source="dayMood5.png"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    WidthRequest="35"
                                    HeightRequest="35"/>
                </Grid>

                <Grid Grid.Column="2">
                    <Image
                                    Source="dayMood4.png"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    WidthRequest="35"
                                    HeightRequest="35"/>
                </Grid>

                <Grid Grid.Column="3">
                    <Image
                                    Source="dayMood3.png"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    WidthRequest="35"
                                    HeightRequest="35"/>
                </Grid>

                <Grid Grid.Column="4">
                    <Image
                                    Source="dayMood2.png"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    WidthRequest="35"
                                    HeightRequest="35"/>
                </Grid>

                <Grid Grid.Column="5">
                    <Image
                                    Source="dayMood1.png"
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    WidthRequest="35"
                                    HeightRequest="35"/>
                </Grid>

            </Grid>

            <Label 
                            Margin="0,10,0,0"
                            VerticalOptions="Start"
                            FontSize="17"
                            HorizontalOptions="Center">
                <Label.FormattedText>
                    <FormattedString>
                        <FormattedString.Spans>
                            <Span
                                            TextColor="Black" 
                                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerMainPage.Mood}"/>
                            <Span 
                                            FontAttributes="Bold"
                                            Text=" "/>
                            <Span 
                                            x:Name="moodTitleLabel"
                                            FontAttributes="Bold"
                                            TextColor="{x:StaticResource blueColor}"
                                            Text="Настроение: "/>
                        </FormattedString.Spans>
                    </FormattedString>
                </Label.FormattedText>
            </Label>

        </StackLayout>

    </ContentView.Content>
</ContentView>