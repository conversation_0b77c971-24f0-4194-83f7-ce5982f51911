﻿using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows.Input;
using DrawnUi.Models;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;
using Triggero.MauiMobileApp.Services;
using Triggero.Models.Tests;

namespace Triggero.MauiMobileApp.Views.Pages.Tests
{

    public partial class TestPage : ContentPage
    {

        public ICommand CommandTest => new Command((ctx) =>
                    {
                        MainThread.BeginInvokeOnMainThread(async () =>
                        {


                        });
                    });


        private Test test;
        public Test Test
        {
            get { return test; }
            set { test = value; OnPropertyChanged(nameof(Test)); }
        }
        public TestPage(Test test)
        {
            Test = test;

            InitializeComponent();

            img.Source = App.GetFullImageUrl(Test.ImgPath, ThumbnailSize.Large, ThumbnailType.Jpg);

            Load();
        }

        private void Load()
        {

            MainThread.BeginInvokeOnMainThread(async () =>
            {
                //Единственное и множественное число слова    вопросы
                if (test.Questions.Count > 5 || test.Questions.Count == 0)
                {
                    testQuestionTextLabel.Text = App.This.Interface.Tests.QuestionsLowercasePlural;
                }
                else if (test.Questions.Count > 1)
                {
                    testQuestionTextLabel.Text = App.This.Interface.Tests.QuestionsLowercaseMiddle;
                }
                else
                {
                    testQuestionTextLabel.Text = App.This.Interface.Tests.QuestionsLowercaseSingular;
                }

                titleLabel.Text = Test.GetLocalizedTitle(LanguageHelper.LangCode);

                favoriteRb.IsToggled = ApplicationState.UserFavorites.HasFavoriteTest(Test.Id);

                StatsHelper.AddTestWatch(Test.Id);
            });

        }

        bool once;
        protected override void OnAppearing()
        {
            base.OnAppearing();

            PlatformUi.Instance.HideStatusBar();

            if (!once)
            {
                once = true;
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    descLabel.Text = Test.GetLocalizedDescription(LanguageHelper.LangCode);

                    var pause = 700; //on iOS this will be triggered BEFORE the page is shown, so we delay a bit
                    if (Device.RuntimePlatform == Device.Android)
                        pause = 10;
                    await Task.Delay(pause);

                    await ContentStack.FadeTo(1, 700);
                });
            }
        }



        private void toggleFavorite(object? sender, bool b)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                //favoriteRb.IsChecked = !favoriteRb.IsChecked;
                //SaveFavsToRemoteDelayed(1000);

                favoriteRb.IsToggled = await ApplicationState.UserFavorites.ToggleTest(Test.Id, test);
                App.Messager.All("FavChanged", new FavChangedObject(Test.Id, favoriteRb.IsToggled));
            });
        }

        #region save on timer

        protected RestartingTimer<object> TimerUpdateLocked;

        protected async Task SaveFavsToRemote()
        {


            MainThread.BeginInvokeOnMainThread(async () =>
            {
                try
                {
                    favoriteRb.IsToggled = await ApplicationState.UserFavorites.ToggleTest(Test.Id, test);
                    //favoriteRb.IsChecked = await ApplicationState.UserFavorites.ToggleTopic(Topic.Id, topic);
                    //favoriteRb.IsChecked = await ApplicationState.UserFavorites.ToggleExercise(Exercise.Id, exercise);
                    //favoriteRb.IsChecked = await ApplicationState.UserFavorites.TogglePractice(Practice.Id, practice);

                    App.Messager.All("FavChanged", new FavChangedObject(Test.Id, favoriteRb.IsToggled));
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                }
            });


        }

        protected void SaveFavsToRemoteDelayed(int ms)
        {
            if (TimerUpdateLocked == null)
            {
                TimerUpdateLocked = new RestartingTimer<object>(TimeSpan.FromMilliseconds(ms), async (context) =>
                {
                    await SaveFavsToRemote();
                });
                TimerUpdateLocked.Start(null);
            }
            else
            {
                TimerUpdateLocked.Restart(null);
            }
        }

        #endregion

        private RelayCommand startTest;
        public RelayCommand StartTest
        {
            get => startTest ??= new RelayCommand(async obj =>
            {
                if (Test.Type == TestType.Simple)
                {
                    App.OpenPage(new TestQuestionPage(Test, 1, 0));
                }
                else if (Test.Type == TestType.Scaled || Test.Type == TestType.ScaledWithConditions)
                {
                    var scoresGroup = new Dictionary<TestScale, int>();
                    foreach (var scale in Test.Scales)
                    {
                        scoresGroup.Add(scale, 0);
                    }
                    App.OpenPage(new TestQuestionPage(Test, 1, scoresGroup));
                }
                else
                {
                    App.OpenPage(new TestQuestionPage(Test, 1, 0));
                }


            });
        }
        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                App.ClosePage(this);
            });
        }

    }
}