### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.MeasuringStrategy
  commentId: T:DrawnUi.Draw.MeasuringStrategy
  id: MeasuringStrategy
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.MeasuringStrategy.MeasureAll
  - DrawnUi.Draw.MeasuringStrategy.MeasureFirst
  - DrawnUi.Draw.MeasuringStrategy.MeasureVisible
  langs:
  - csharp
  - vb
  name: MeasuringStrategy
  nameWithType: MeasuringStrategy
  fullName: DrawnUi.Draw.MeasuringStrategy
  type: Enum
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/MeasuringStrategy.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasuringStrategy
    path: ../src/Shared/Draw/Internals/Enums/MeasuringStrategy.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public enum MeasuringStrategy
    content.vb: Public Enum MeasuringStrategy
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.MeasuringStrategy.MeasureAll
  commentId: F:DrawnUi.Draw.MeasuringStrategy.MeasureAll
  id: MeasureAll
  parent: DrawnUi.Draw.MeasuringStrategy
  langs:
  - csharp
  - vb
  name: MeasureAll
  nameWithType: MeasuringStrategy.MeasureAll
  fullName: DrawnUi.Draw.MeasuringStrategy.MeasureAll
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/MeasuringStrategy.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasureAll
    path: ../src/Shared/Draw/Internals/Enums/MeasuringStrategy.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: For different children sizes
  example: []
  syntax:
    content: MeasureAll = 0
    return:
      type: DrawnUi.Draw.MeasuringStrategy
- uid: DrawnUi.Draw.MeasuringStrategy.MeasureFirst
  commentId: F:DrawnUi.Draw.MeasuringStrategy.MeasureFirst
  id: MeasureFirst
  parent: DrawnUi.Draw.MeasuringStrategy
  langs:
  - csharp
  - vb
  name: MeasureFirst
  nameWithType: MeasuringStrategy.MeasureFirst
  fullName: DrawnUi.Draw.MeasuringStrategy.MeasureFirst
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/MeasuringStrategy.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasureFirst
    path: ../src/Shared/Draw/Internals/Enums/MeasuringStrategy.cs
    startLine: 12
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Best for equal item sizes
  example: []
  syntax:
    content: MeasureFirst = 1
    return:
      type: DrawnUi.Draw.MeasuringStrategy
- uid: DrawnUi.Draw.MeasuringStrategy.MeasureVisible
  commentId: F:DrawnUi.Draw.MeasuringStrategy.MeasureVisible
  id: MeasureVisible
  parent: DrawnUi.Draw.MeasuringStrategy
  langs:
  - csharp
  - vb
  name: MeasureVisible
  nameWithType: MeasuringStrategy.MeasureVisible
  fullName: DrawnUi.Draw.MeasuringStrategy.MeasureVisible
  type: Field
  source:
    remote:
      path: src/Shared/Draw/Internals/Enums/MeasuringStrategy.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasureVisible
    path: ../src/Shared/Draw/Internals/Enums/MeasuringStrategy.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: EXPERIMENTAL PREVIEW!!! Acts like MeasureAll but measures by chunks in background.
  example: []
  syntax:
    content: MeasureVisible = 2
    return:
      type: DrawnUi.Draw.MeasuringStrategy
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.MeasuringStrategy
  commentId: T:DrawnUi.Draw.MeasuringStrategy
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.MeasuringStrategy.html
  name: MeasuringStrategy
  nameWithType: MeasuringStrategy
  fullName: DrawnUi.Draw.MeasuringStrategy
