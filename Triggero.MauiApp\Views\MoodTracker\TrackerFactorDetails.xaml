﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             BackgroundColor="White"
             Background="White"
             x:Class="Triggero.MauiMobileApp.Views.MoodTracker.TrackerFactorDetails">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="152"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="110"/>
            </Grid.RowDefinitions>

            <Image
                Aspect="Fill"
                Source="lightBlueGradientBg.png"
                Grid.RowSpan="3"/>

            <Grid Grid.Row="0">

                <StackLayout
                    HorizontalOptions="Fill"
                    VerticalOptions="End"
                    Spacing="8">
                    <Label 
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="17"
                        FontAttributes="Bold"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerFactorsDetails.Header}"/>
                    <Label 
                        TextColor="{x:StaticResource ColorTextGray}"

                        FontSize="14"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerFactorsDetails.Description}"/>
                </StackLayout>
                
            </Grid>

            <Grid Grid.Row="1">

                <ScrollView 
                    x:Name="scrollView"
                    VerticalScrollBarVisibility="Never"
                    Margin="20,20,20,20">
                    <StackLayout 
                        x:Name="layout"
                        Spacing="32">
                        
                        
                    </StackLayout>
                </ScrollView>
                
            </Grid>

            <Grid 
                Grid.Row="2">
                <Grid
                    Margin="20,0,20,0"
                    VerticalOptions="Start"
                    ColumnSpacing="16"
                    HeightRequest="56">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="56"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <ImageButton
                        Grid.Column="0"
                        Command="{Binding Source={x:Reference this},Path=GoBack}"
                        Source="buttonBackBordered.png"
                        WidthRequest="56"
                        HeightRequest="56"
                        HorizontalOptions="Center"
                        VerticalOptions="Center"
                        BackgroundColor="Transparent"
                        CornerRadius="0"/>

                    <Button 
                        Grid.Column="1"
                        x:Name="goNextBtn"
                        Command="{Binding Source={x:Reference this},Path=GoNext}"
                        VerticalOptions="Fill"
                        HeightRequest="56"
                        Style="{x:StaticResource yellow_btn}"
                        Text="Далее 4/5"/>


                </Grid>
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>