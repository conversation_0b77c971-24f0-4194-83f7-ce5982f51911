### YamlMime:ManagedReference
items:
- uid: DrawnUi.Views.DisposableManager
  commentId: T:DrawnUi.Views.DisposableManager
  id: DisposableManager
  parent: DrawnUi.Views
  children:
  - DrawnUi.Views.DisposableManager.#ctor(System.Int32)
  - DrawnUi.Views.DisposableManager.Dispose
  - DrawnUi.Views.DisposableManager.DisposeDisposables(System.Int64)
  - DrawnUi.Views.DisposableManager.EnqueueDisposable(System.IDisposable,System.Int64)
  - DrawnUi.Views.DisposableManager.PendingDisposalCount
  langs:
  - csharp
  - vb
  name: DisposableManager
  nameWithType: DisposableManager
  fullName: DrawnUi.Views.DisposableManager
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DisposableManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DisposableManager
    path: ../src/Maui/DrawnUi/Views/DisposableManager.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Manages delayed disposal of IDisposable objects based on frame count
  example: []
  syntax:
    content: 'public class DisposableManager : IDisposable'
    content.vb: Public Class DisposableManager Implements IDisposable
  inheritance:
  - System.Object
  implements:
  - System.IDisposable
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Views.DisposableManager.#ctor(System.Int32)
  commentId: M:DrawnUi.Views.DisposableManager.#ctor(System.Int32)
  id: '#ctor(System.Int32)'
  parent: DrawnUi.Views.DisposableManager
  langs:
  - csharp
  - vb
  name: DisposableManager(int)
  nameWithType: DisposableManager.DisposableManager(int)
  fullName: DrawnUi.Views.DisposableManager.DisposableManager(int)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DisposableManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Views/DisposableManager.cs
    startLine: 43
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Initializes a new instance of the DisposableManager class.
  example: []
  syntax:
    content: public DisposableManager(int framesToHold = 3)
    parameters:
    - id: framesToHold
      type: System.Int32
      description: The number of frames to hold before disposing. Default is 3 frames.
    content.vb: Public Sub New(framesToHold As Integer = 3)
  overload: DrawnUi.Views.DisposableManager.#ctor*
  nameWithType.vb: DisposableManager.New(Integer)
  fullName.vb: DrawnUi.Views.DisposableManager.New(Integer)
  name.vb: New(Integer)
- uid: DrawnUi.Views.DisposableManager.EnqueueDisposable(System.IDisposable,System.Int64)
  commentId: M:DrawnUi.Views.DisposableManager.EnqueueDisposable(System.IDisposable,System.Int64)
  id: EnqueueDisposable(System.IDisposable,System.Int64)
  parent: DrawnUi.Views.DisposableManager
  langs:
  - csharp
  - vb
  name: EnqueueDisposable(IDisposable, long)
  nameWithType: DisposableManager.EnqueueDisposable(IDisposable, long)
  fullName: DrawnUi.Views.DisposableManager.EnqueueDisposable(System.IDisposable, long)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DisposableManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: EnqueueDisposable
    path: ../src/Maui/DrawnUi/Views/DisposableManager.cs
    startLine: 57
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Enqueues an IDisposable object with the specified frame number.
  example: []
  syntax:
    content: public void EnqueueDisposable(IDisposable disposable, long frameNumber)
    parameters:
    - id: disposable
      type: System.IDisposable
      description: The IDisposable object to enqueue.
    - id: frameNumber
      type: System.Int64
      description: The frame number when this resource was created/last used.
    content.vb: Public Sub EnqueueDisposable(disposable As IDisposable, frameNumber As Long)
  overload: DrawnUi.Views.DisposableManager.EnqueueDisposable*
  nameWithType.vb: DisposableManager.EnqueueDisposable(IDisposable, Long)
  fullName.vb: DrawnUi.Views.DisposableManager.EnqueueDisposable(System.IDisposable, Long)
  name.vb: EnqueueDisposable(IDisposable, Long)
- uid: DrawnUi.Views.DisposableManager.DisposeDisposables(System.Int64)
  commentId: M:DrawnUi.Views.DisposableManager.DisposeDisposables(System.Int64)
  id: DisposeDisposables(System.Int64)
  parent: DrawnUi.Views.DisposableManager
  langs:
  - csharp
  - vb
  name: DisposeDisposables(long)
  nameWithType: DisposableManager.DisposeDisposables(long)
  fullName: DrawnUi.Views.DisposableManager.DisposeDisposables(long)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DisposableManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DisposeDisposables
    path: ../src/Maui/DrawnUi/Views/DisposableManager.cs
    startLine: 71
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: >-
    Disposes of all IDisposable objects that are old enough based on frame count.

    Call this before every frame start.
  example: []
  syntax:
    content: public void DisposeDisposables(long currentFrameNumber)
    parameters:
    - id: currentFrameNumber
      type: System.Int64
      description: The current frame number.
    content.vb: Public Sub DisposeDisposables(currentFrameNumber As Long)
  overload: DrawnUi.Views.DisposableManager.DisposeDisposables*
  nameWithType.vb: DisposableManager.DisposeDisposables(Long)
  fullName.vb: DrawnUi.Views.DisposableManager.DisposeDisposables(Long)
  name.vb: DisposeDisposables(Long)
- uid: DrawnUi.Views.DisposableManager.Dispose
  commentId: M:DrawnUi.Views.DisposableManager.Dispose
  id: Dispose
  parent: DrawnUi.Views.DisposableManager
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: DisposableManager.Dispose()
  fullName: DrawnUi.Views.DisposableManager.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DisposableManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Views/DisposableManager.cs
    startLine: 107
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Disposes all remaining disposables immediately.
  example: []
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Views.DisposableManager.Dispose*
  implements:
  - System.IDisposable.Dispose
- uid: DrawnUi.Views.DisposableManager.PendingDisposalCount
  commentId: P:DrawnUi.Views.DisposableManager.PendingDisposalCount
  id: PendingDisposalCount
  parent: DrawnUi.Views.DisposableManager
  langs:
  - csharp
  - vb
  name: PendingDisposalCount
  nameWithType: DisposableManager.PendingDisposalCount
  fullName: DrawnUi.Views.DisposableManager.PendingDisposalCount
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Views/DisposableManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: PendingDisposalCount
    path: ../src/Maui/DrawnUi/Views/DisposableManager.cs
    startLine: 147
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Gets the number of items waiting for disposal.
  example: []
  syntax:
    content: public int PendingDisposalCount { get; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public ReadOnly Property PendingDisposalCount As Integer
  overload: DrawnUi.Views.DisposableManager.PendingDisposalCount*
references:
- uid: DrawnUi.Views
  commentId: N:DrawnUi.Views
  href: DrawnUi.html
  name: DrawnUi.Views
  nameWithType: DrawnUi.Views
  fullName: DrawnUi.Views
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Views.DisposableManager.#ctor*
  commentId: Overload:DrawnUi.Views.DisposableManager.#ctor
  href: DrawnUi.Views.DisposableManager.html#DrawnUi_Views_DisposableManager__ctor_System_Int32_
  name: DisposableManager
  nameWithType: DisposableManager.DisposableManager
  fullName: DrawnUi.Views.DisposableManager.DisposableManager
  nameWithType.vb: DisposableManager.New
  fullName.vb: DrawnUi.Views.DisposableManager.New
  name.vb: New
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Views.DisposableManager.EnqueueDisposable*
  commentId: Overload:DrawnUi.Views.DisposableManager.EnqueueDisposable
  href: DrawnUi.Views.DisposableManager.html#DrawnUi_Views_DisposableManager_EnqueueDisposable_System_IDisposable_System_Int64_
  name: EnqueueDisposable
  nameWithType: DisposableManager.EnqueueDisposable
  fullName: DrawnUi.Views.DisposableManager.EnqueueDisposable
- uid: System.Int64
  commentId: T:System.Int64
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int64
  name: long
  nameWithType: long
  fullName: long
  nameWithType.vb: Long
  fullName.vb: Long
  name.vb: Long
- uid: DrawnUi.Views.DisposableManager.DisposeDisposables*
  commentId: Overload:DrawnUi.Views.DisposableManager.DisposeDisposables
  href: DrawnUi.Views.DisposableManager.html#DrawnUi_Views_DisposableManager_DisposeDisposables_System_Int64_
  name: DisposeDisposables
  nameWithType: DisposableManager.DisposeDisposables
  fullName: DrawnUi.Views.DisposableManager.DisposeDisposables
- uid: DrawnUi.Views.DisposableManager.Dispose*
  commentId: Overload:DrawnUi.Views.DisposableManager.Dispose
  href: DrawnUi.Views.DisposableManager.html#DrawnUi_Views_DisposableManager_Dispose
  name: Dispose
  nameWithType: DisposableManager.Dispose
  fullName: DrawnUi.Views.DisposableManager.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
- uid: DrawnUi.Views.DisposableManager.PendingDisposalCount*
  commentId: Overload:DrawnUi.Views.DisposableManager.PendingDisposalCount
  href: DrawnUi.Views.DisposableManager.html#DrawnUi_Views_DisposableManager_PendingDisposalCount
  name: PendingDisposalCount
  nameWithType: DisposableManager.PendingDisposalCount
  fullName: DrawnUi.Views.DisposableManager.PendingDisposalCount
