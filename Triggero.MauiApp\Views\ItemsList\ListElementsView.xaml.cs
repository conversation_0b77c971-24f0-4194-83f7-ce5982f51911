﻿
using System.Linq;
using System.Windows.Input;
using Triggero.MauiMobileApp.Services;
using Triggero.MauiMobileApp.ViewModels;
using Triggero.MauiMobileApp.Views.Pages;
using Triggero.Models.Practices.Categories;
using Triggero.Models.Tests;

using ViewExtensions = Triggero.MauiMobileApp.Extensions.ViewExtensions;

namespace Triggero.MauiMobileApp.Views
{

    /// <summary>
    /// For all the items lists except tests, they use ListTestsView Why? I don't recall.
    /// </summary>
    public partial class ListElementsView : ContentView, IDisposable
    {
        private readonly ElementsListViewModel _vm;

        public void Dispose()
        {
            DrawnCanvas.DisableUpdates();
            DrawnCanvas?.Dispose();
            if (BindingContext is IDisposable dispose)
            {
                dispose.Dispose();
            }
            App.Messager.Unsubscribe(this, "FavChanged");
        }

        public ListElementsView(AbstractCategory category)
        {

            App.Messager.Subscribe<FavChangedObject>(this, "FavChanged", async (sender, arg) =>
            {
                var existing = _vm.Items.FirstOrDefault(x => x.Id == arg.Id);
                if (existing != null)
                {
                    try
                    {
                        //for favorites to be refreshed.. just rebuild cells
                        var save = StackCells.BindingContext;
                        StackCells.BindingContext = null;
                        StackCells.BindingContext = save;
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }
                }
            });

            if (category is ExerciseCategory exerciseCategory)
            {
                _vm = new ListExercisesViewModel(exerciseCategory);
            }
            else
            if (category is PracticeCategory practiceCategory)
            {
                _vm = new ListPracticesViewModel(practiceCategory);
            }
            else
            if (category is TopicCategory topicCategory)
            {
                _vm = new ListTopicsViewModel(topicCategory);
            }
            else
            if (category is TestCategory testCategory)
            {
                _vm = new ListTestsViewModel(testCategory);
            }
            else
            {
                throw new ApplicationException("Unknown category");
            }

            InitializeComponent();

            BindingContext = _vm;

            SetStateExpanded(false);


        }

        #region Группировка

        private void filterByNewCheckedChanged(object sender, CheckedChangedEventArgs e)
        {
            if (e.Value)
            {
                _vm.CommandOrderA.Execute(true);
            }
        }
        private void filterByWatchesCheckedChanged(object sender, CheckedChangedEventArgs e)
        {
            if (e.Value)
            {
                _vm.CommandOrderB.Execute(true);
            }
        }

        #endregion

        private ICommand goBack;
        public ICommand GoBack
        {
            get => goBack ??= new RelayCommand(async obj =>
            {
                var mainPage = App.Current.MainPage.Navigation.NavigationStack.FirstOrDefault(o => o is MainPage) as MainPage;
                if (mainPage != null)
                {
                    mainPage.GoBack();
                }
            });
        }

        private bool isCollapsed = false;

        void SetStateCollapsed(bool animate)
        {
            isCollapsed = true;

            if (animate)
            {

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    pageBackNavigationGrid.FadeTo(0, 500);
                    arrowBackBtn.FadeTo(1, 500);

                    categoryLabelCollapsed.Opacity = 0.01;
                    categoryLabelCollapsed.IsVisible = true;
                    categoryLabelCollapsed.FadeTo(1, 250);

                    categoryLabel.FadeTo(0, 250).ContinueWith(task =>
                    {
                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            categoryLabel.IsVisible = false;
                        });
                    });

                    ViewExtensions.AnimateRow(this, mainGridDef1, 60);
                    ViewExtensions.AnimateRow(this, rowDefPancake1, 60);

                    //mainGrid.ColorTo(_vm.ThemeColorB, 500, Easing.Linear);

                });
            }
            else
            {
                pageBackNavigationGrid.Opacity = 0;
                arrowBackBtn.Opacity = 1;

                categoryLabel.Opacity = 0;
                categoryLabel.IsVisible = false;

                categoryLabelCollapsed.Opacity = 1;
                categoryLabelCollapsed.IsVisible = true;

                mainGridDef1.Height = new GridLength(50, GridUnitType.Absolute);
                rowDefPancake1.Height = new GridLength(60, GridUnitType.Absolute);

                mainGrid.BackgroundColor = _vm.ThemeColorB;
            }

        }

        void SetStateExpanded(bool animate)
        {
            isCollapsed = false;

            if (animate)
            {

                MainThread.BeginInvokeOnMainThread(() =>
                {
                    pageBackNavigationGrid.FadeTo(1, 500);
                    arrowBackBtn.FadeTo(0, 500);

                    categoryLabelCollapsed.IsVisible = false;

                    categoryLabel.Opacity = 0.01;
                    categoryLabel.IsVisible = true;
                    categoryLabel.FadeTo(1, 250);

                    ViewExtensions.AnimateRow(this, mainGridDef1, 110);
                    ViewExtensions.AnimateRow(this, rowDefPancake1, 22);

                    //mainGrid.ColorTo(_vm.ThemeColor, 500, Easing.Linear);

                });

            }
            else
            {
                pageBackNavigationGrid.Opacity = 1;
                arrowBackBtn.Opacity = 0;

                categoryLabelCollapsed.IsVisible = false;

                categoryLabel.Opacity = 1;
                categoryLabel.IsVisible = true;

                mainGridDef1.Height = new GridLength(110, GridUnitType.Absolute);
                rowDefPancake1.Height = new GridLength(22, GridUnitType.Absolute);

                mainGrid.BackgroundColor = _vm.ThemeColor;
            }

        }

        private void SkiaScroll_Scrolled(object sender, ScaledPoint e)
        {
            if (e.Units.Y < -20)
            {
                if (!isCollapsed)
                {
                    SetStateCollapsed(true);
                }
            }
            else
            {
                //if (isCollapsed)
                //{
                //    SetStateExpanded(true);
                //}
            }

        }



    }


}