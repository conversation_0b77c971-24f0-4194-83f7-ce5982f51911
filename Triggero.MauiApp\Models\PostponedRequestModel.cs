﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using System.Net.Http;

namespace Triggero.Models
{
    public class PostponedRequestModel
    {
        public PostponedRequestModel(string url, HttpMethod method)
        {
            URL = url;
            Method = method;
        }
        public PostponedRequestModel(string url, HttpMethod method, object body) : this(url, method)
        {
            Body = body;
        }


        public string URL { get; set; }
        public HttpMethod Method { get; set; }
        public object Body { get; set; }



        public async Task ExecuteRequest()
        {
            // Use pure HttpClient - no RestSharp dependency
            using var httpClient = new HttpClient();
            using var request = new HttpRequestMessage(Method, URL);

            if (Body != null)
            {
                var json = System.Text.Json.JsonSerializer.Serialize(Body);
                request.Content = new StringContent(json, System.Text.Encoding.UTF8, "application/json");
            }

            var response = await httpClient.SendAsync(request);
        }
    }
}
