﻿using Odintcovo.API.Helpers;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Helpers;

namespace Triggero.Models
{
    public class PostponedRequestModel
    {
        public PostponedRequestModel(string url,Method method)
        {
            URL = url;
            Method = method;
        }
        public PostponedRequestModel(string url, Method method, object body) : this(url, method)
        {
            Body = body;
        }


        public string URL { get; set; }
        public Method Method { get; set; }
        public object Body { get; set; }



        public async Task ExecuteRequest()
        {
            // Use mobile-optimized HTTP client instead of RestSharp
            var response = await MobileApiRequestHelper.ExecuteRequestAsync(URL, Method, Body);
        }
    }
}
