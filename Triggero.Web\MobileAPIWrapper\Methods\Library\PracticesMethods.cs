﻿using Newtonsoft.Json;
using MobileAPIWrapper.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;

namespace MobileAPIWrapper.Methods.Library
{
    public class PracticesMethods
    {
        public static string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("Practices/");


        public async Task<List<PracticeCategory>> GetPracticeCategories()
        {
            string url = BASE_HOST + "GetPracticeCategories";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<PracticeCategory>>(response.Content);
            return obj;
        }



        public async Task<List<Practice>> GetPractices()
        {
            string url = BASE_HOST + "GetPractices";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Practice>>(response.Content);
            return obj;
        }
        public async Task<List<Practice>> GetPracticesChunk(int count, int offset)
        {
            string url = BASE_HOST + $"GetPracticesChunk?count={count}&offset={offset}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Practice>>(response.Content);
            return obj;
        }








        public async Task<List<Practice>> GetPracticesByCategory(int categoryId)
        {
            string url = BASE_HOST + $"GetPracticesByCategory?categoryId={categoryId}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Practice>>(response.Content);
            return obj;
        }
        public async Task<List<Practice>> GetPracticesByCategoryChunk(int categoryId, int count, int offset)
        {
            string url = BASE_HOST + $"GetPracticesByCategory?categoryId={categoryId}&count={count}&offset={offset}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Practice>>(response.Content);
            return obj;
        }





        public async Task<List<Practice>> GetPracticesByTag(string tag)
        {
            string url = BASE_HOST + $"GetPracticesByTag?tag={tag}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Practice>>(response.Content);
            return obj;
        }




        public async Task<Practice> GetPractice(int id)
        {
            string url = BASE_HOST + $"GetPractice?id={id}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<Practice>(response.Content);
            return obj;
        }


        public async Task<bool> AddWatch(int practiceId)
        {
            string url = BASE_HOST + $"AddWatch?practiceId={practiceId}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Put);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
    }
}
