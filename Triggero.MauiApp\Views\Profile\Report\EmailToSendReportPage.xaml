﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             
             x:Class="Triggero.MauiMobileApp.Views.Pages.Profile.Report.EmailToSendReportPage"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
             x:Name="this">
    <ContentPage.Content>
        <Grid>
            
            <Image
                Aspect="Fill"
                Source="trackerBgGradient.png"/>

            <StackLayout
                Margin="{x:OnPlatform Android='20,30,20,0',iOS='20,60,20,0'}">


                <Grid
                    BackgroundColor="Transparent"
                    Background="Transparent"
                    WidthRequest="40"
                    HeightRequest="25"
                    HorizontalOptions="Start"
                    VerticalOptions="Start">
                    <Grid.GestureRecognizers>
                        <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SetEmail}"/>
                    </Grid.GestureRecognizers>
                    <ImageButton 
                        InputTransparent="True"
                        Command="{Binding Source={x:Reference this},Path=SetEmail}"
                        CornerRadius="0"
                        WidthRequest="8"
                        HeightRequest="16"
                        Source="arrowBack.png"
                        Margin="0,0,0,0"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        BackgroundColor="Transparent"/>

                </Grid>

                <Label 
                    Margin="20,30,0,0"
                    TextColor="{x:StaticResource greyTextColor}"
                    FontSize="17"
                    FontAttributes="Bold"
                    FontFamily="FontTextLight"
                    VerticalOptions="Center"
                    HorizontalOptions="Start"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Profile.ProfileEnterEmail.EnterYourEmail}"/>


                <Frame HasShadow="False"
                       Margin="0,4,0,0"
                       HeightRequest="56"
                       VerticalOptions="Start"
                       BackgroundColor="Transparent"
                       Padding="0">
                    <Frame.Shadow>
                        <Shadow Brush="#27527A" Offset="2,2" Radius="12" Opacity="0.06" />
                    </Frame.Shadow>

                    <!--todo BorderThickness="0"
                    FocusedBorderThickness="0"-->

                    <Entry
                        Margin="0,4,0,0"
                        Text="{Binding Source={x:Reference this},Path=Email,Mode=TwoWay}"
                        BackgroundColor="White"
                        Style="{x:StaticResource grayTextEdit}"
                        HeightRequest="56"
                        VerticalOptions="Start"
                        HorizontalOptions="Fill"/>

                </Frame>
            </StackLayout>
            

        </Grid>
    </ContentPage.Content>
</ContentPage>