﻿namespace Triggero.MauiMobileApp.Views.Drawn;

public class FavCheck : SkiaCheckbox
{
    protected override void CreateDefaultContent()
    {
        if (Views.Count == 0)
        {
            CreateContent();
        }
    }

    protected void CreateContent()
    {
        SetDefaultContentSize(50, 50);

        this.AddSubView(new SkiaImage
        {
            Tag = "FrameOff",
            Source = "likeUnset.png",
            Aspect = TransformAspect.AspectFit,
            WidthRequest = 20,
            LockRatio = 1,
            HorizontalOptions = LayoutOptions.Center,
            VerticalOptions = LayoutOptions.Center,
        });

        this.AddSubView(new SkiaImage
        {
            Tag = "ViewCheckOn",
            Source = "likeSet.png",
            Aspect = TransformAspect.AspectFit,
            WidthRequest = 20,
            LockRatio = 1,
            HorizontalOptions = LayoutOptions.Center,
            VerticalOptions = LayoutOptions.Center,
        });
    }
}