### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.DataContextIterator
  commentId: T:DrawnUi.Draw.DataContextIterator
  id: DataContextIterator
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.DataContextIterator.#ctor(DrawnUi.Draw.ViewsIterator,System.Nullable{DrawnUi.Draw.LayoutType})
  - DrawnUi.Draw.DataContextIterator.Current
  - DrawnUi.Draw.DataContextIterator.Dispose
  - DrawnUi.Draw.DataContextIterator.MoveNext
  - DrawnUi.Draw.DataContextIterator.Reset
  langs:
  - csharp
  - vb
  name: DataContextIterator
  nameWithType: DataContextIterator
  fullName: DrawnUi.Draw.DataContextIterator
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DataContextIterator
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1435
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class DataContextIterator : IEnumerator<SkiaControl>, IEnumerator, IDisposable'
    content.vb: Public Class DataContextIterator Implements IEnumerator(Of SkiaControl), IEnumerator, IDisposable
  inheritance:
  - System.Object
  implements:
  - System.Collections.Generic.IEnumerator{DrawnUi.Draw.SkiaControl}
  - System.Collections.IEnumerator
  - System.IDisposable
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.DataContextIterator.#ctor(DrawnUi.Draw.ViewsIterator,System.Nullable{DrawnUi.Draw.LayoutType})
  commentId: M:DrawnUi.Draw.DataContextIterator.#ctor(DrawnUi.Draw.ViewsIterator,System.Nullable{DrawnUi.Draw.LayoutType})
  id: '#ctor(DrawnUi.Draw.ViewsIterator,System.Nullable{DrawnUi.Draw.LayoutType})'
  parent: DrawnUi.Draw.DataContextIterator
  langs:
  - csharp
  - vb
  name: DataContextIterator(ViewsIterator, LayoutType?)
  nameWithType: DataContextIterator.DataContextIterator(ViewsIterator, LayoutType?)
  fullName: DrawnUi.Draw.DataContextIterator.DataContextIterator(DrawnUi.Draw.ViewsIterator, DrawnUi.Draw.LayoutType?)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1466
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public DataContextIterator(ViewsIterator viewsProvider, LayoutType? layoutType)
    parameters:
    - id: viewsProvider
      type: DrawnUi.Draw.ViewsIterator
    - id: layoutType
      type: System.Nullable{DrawnUi.Draw.LayoutType}
    content.vb: Public Sub New(viewsProvider As ViewsIterator, layoutType As LayoutType?)
  overload: DrawnUi.Draw.DataContextIterator.#ctor*
  nameWithType.vb: DataContextIterator.New(ViewsIterator, LayoutType?)
  fullName.vb: DrawnUi.Draw.DataContextIterator.New(DrawnUi.Draw.ViewsIterator, DrawnUi.Draw.LayoutType?)
  name.vb: New(ViewsIterator, LayoutType?)
- uid: DrawnUi.Draw.DataContextIterator.Current
  commentId: P:DrawnUi.Draw.DataContextIterator.Current
  id: Current
  parent: DrawnUi.Draw.DataContextIterator
  langs:
  - csharp
  - vb
  name: Current
  nameWithType: DataContextIterator.Current
  fullName: DrawnUi.Draw.DataContextIterator.Current
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Current
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1481
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Gets the element in the collection at the current position of the enumerator.
  example: []
  syntax:
    content: public SkiaControl Current { get; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
      description: The element in the collection at the current position of the enumerator.
    content.vb: Public ReadOnly Property Current As SkiaControl
  overload: DrawnUi.Draw.DataContextIterator.Current*
  implements:
  - System.Collections.Generic.IEnumerator{DrawnUi.Draw.SkiaControl}.Current
- uid: DrawnUi.Draw.DataContextIterator.MoveNext
  commentId: M:DrawnUi.Draw.DataContextIterator.MoveNext
  id: MoveNext
  parent: DrawnUi.Draw.DataContextIterator
  langs:
  - csharp
  - vb
  name: MoveNext()
  nameWithType: DataContextIterator.MoveNext()
  fullName: DrawnUi.Draw.DataContextIterator.MoveNext()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MoveNext
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1485
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Advances the enumerator to the next element of the collection.
  example: []
  syntax:
    content: public bool MoveNext()
    return:
      type: System.Boolean
      description: <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">true</a> if the enumerator was successfully advanced to the next element; <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">false</a> if the enumerator has passed the end of the collection.
    content.vb: Public Function MoveNext() As Boolean
  overload: DrawnUi.Draw.DataContextIterator.MoveNext*
  exceptions:
  - type: System.InvalidOperationException
    commentId: T:System.InvalidOperationException
    description: The collection was modified after the enumerator was created.
  implements:
  - System.Collections.IEnumerator.MoveNext
- uid: DrawnUi.Draw.DataContextIterator.Reset
  commentId: M:DrawnUi.Draw.DataContextIterator.Reset
  id: Reset
  parent: DrawnUi.Draw.DataContextIterator
  langs:
  - csharp
  - vb
  name: Reset()
  nameWithType: DataContextIterator.Reset()
  fullName: DrawnUi.Draw.DataContextIterator.Reset()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Reset
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1525
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Sets the enumerator to its initial position, which is before the first element in the collection.
  example: []
  syntax:
    content: public void Reset()
    content.vb: Public Sub Reset()
  overload: DrawnUi.Draw.DataContextIterator.Reset*
  exceptions:
  - type: System.InvalidOperationException
    commentId: T:System.InvalidOperationException
    description: The collection was modified after the enumerator was created.
  - type: System.NotSupportedException
    commentId: T:System.NotSupportedException
    description: The enumerator does not support being reset.
  implements:
  - System.Collections.IEnumerator.Reset
- uid: DrawnUi.Draw.DataContextIterator.Dispose
  commentId: M:DrawnUi.Draw.DataContextIterator.Dispose
  id: Dispose
  parent: DrawnUi.Draw.DataContextIterator
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: DataContextIterator.Dispose()
  fullName: DrawnUi.Draw.DataContextIterator.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ViewsAdapter.cs
    startLine: 1542
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Draw.DataContextIterator.Dispose*
  implements:
  - System.IDisposable.Dispose
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Collections.Generic.IEnumerator{DrawnUi.Draw.SkiaControl}
  commentId: T:System.Collections.Generic.IEnumerator{DrawnUi.Draw.SkiaControl}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.IEnumerator`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  name: IEnumerator<SkiaControl>
  nameWithType: IEnumerator<SkiaControl>
  fullName: System.Collections.Generic.IEnumerator<DrawnUi.Draw.SkiaControl>
  nameWithType.vb: IEnumerator(Of SkiaControl)
  fullName.vb: System.Collections.Generic.IEnumerator(Of DrawnUi.Draw.SkiaControl)
  name.vb: IEnumerator(Of SkiaControl)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerator`1
    name: IEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerator`1
    name: IEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: )
- uid: System.Collections.IEnumerator
  commentId: T:System.Collections.IEnumerator
  parent: System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerator
  name: IEnumerator
  nameWithType: IEnumerator
  fullName: System.Collections.IEnumerator
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.Collections.Generic.IEnumerator`1
  commentId: T:System.Collections.Generic.IEnumerator`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  name: IEnumerator<T>
  nameWithType: IEnumerator<T>
  fullName: System.Collections.Generic.IEnumerator<T>
  nameWithType.vb: IEnumerator(Of T)
  fullName.vb: System.Collections.Generic.IEnumerator(Of T)
  name.vb: IEnumerator(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.IEnumerator`1
    name: IEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.IEnumerator`1
    name: IEnumerator
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: System.Collections
  commentId: N:System.Collections
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections
  nameWithType: System.Collections
  fullName: System.Collections
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.DataContextIterator.#ctor*
  commentId: Overload:DrawnUi.Draw.DataContextIterator.#ctor
  href: DrawnUi.Draw.DataContextIterator.html#DrawnUi_Draw_DataContextIterator__ctor_DrawnUi_Draw_ViewsIterator_System_Nullable_DrawnUi_Draw_LayoutType__
  name: DataContextIterator
  nameWithType: DataContextIterator.DataContextIterator
  fullName: DrawnUi.Draw.DataContextIterator.DataContextIterator
  nameWithType.vb: DataContextIterator.New
  fullName.vb: DrawnUi.Draw.DataContextIterator.New
  name.vb: New
- uid: DrawnUi.Draw.ViewsIterator
  commentId: T:DrawnUi.Draw.ViewsIterator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ViewsIterator.html
  name: ViewsIterator
  nameWithType: ViewsIterator
  fullName: DrawnUi.Draw.ViewsIterator
- uid: System.Nullable{DrawnUi.Draw.LayoutType}
  commentId: T:System.Nullable{DrawnUi.Draw.LayoutType}
  parent: System
  definition: System.Nullable`1
  href: DrawnUi.Draw.LayoutType.html
  name: LayoutType?
  nameWithType: LayoutType?
  fullName: DrawnUi.Draw.LayoutType?
  spec.csharp:
  - uid: DrawnUi.Draw.LayoutType
    name: LayoutType
    href: DrawnUi.Draw.LayoutType.html
  - name: '?'
  spec.vb:
  - uid: DrawnUi.Draw.LayoutType
    name: LayoutType
    href: DrawnUi.Draw.LayoutType.html
  - name: '?'
- uid: System.Nullable`1
  commentId: T:System.Nullable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  name: Nullable<T>
  nameWithType: Nullable<T>
  fullName: System.Nullable<T>
  nameWithType.vb: Nullable(Of T)
  fullName.vb: System.Nullable(Of T)
  name.vb: Nullable(Of T)
  spec.csharp:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.DataContextIterator.Current*
  commentId: Overload:DrawnUi.Draw.DataContextIterator.Current
  href: DrawnUi.Draw.DataContextIterator.html#DrawnUi_Draw_DataContextIterator_Current
  name: Current
  nameWithType: DataContextIterator.Current
  fullName: DrawnUi.Draw.DataContextIterator.Current
- uid: System.Collections.Generic.IEnumerator{DrawnUi.Draw.SkiaControl}.Current
  commentId: P:System.Collections.Generic.IEnumerator{DrawnUi.Draw.SkiaControl}.Current
  parent: System.Collections.Generic.IEnumerator{DrawnUi.Draw.SkiaControl}
  definition: System.Collections.Generic.IEnumerator`1.Current
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1.current
  name: Current
  nameWithType: IEnumerator<SkiaControl>.Current
  fullName: System.Collections.Generic.IEnumerator<DrawnUi.Draw.SkiaControl>.Current
  nameWithType.vb: IEnumerator(Of SkiaControl).Current
  fullName.vb: System.Collections.Generic.IEnumerator(Of DrawnUi.Draw.SkiaControl).Current
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: System.Collections.Generic.IEnumerator`1.Current
  commentId: P:System.Collections.Generic.IEnumerator`1.Current
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.ienumerator-1.current
  name: Current
  nameWithType: IEnumerator<T>.Current
  fullName: System.Collections.Generic.IEnumerator<T>.Current
  nameWithType.vb: IEnumerator(Of T).Current
  fullName.vb: System.Collections.Generic.IEnumerator(Of T).Current
- uid: System.InvalidOperationException
  commentId: T:System.InvalidOperationException
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.invalidoperationexception
  name: InvalidOperationException
  nameWithType: InvalidOperationException
  fullName: System.InvalidOperationException
- uid: DrawnUi.Draw.DataContextIterator.MoveNext*
  commentId: Overload:DrawnUi.Draw.DataContextIterator.MoveNext
  href: DrawnUi.Draw.DataContextIterator.html#DrawnUi_Draw_DataContextIterator_MoveNext
  name: MoveNext
  nameWithType: DataContextIterator.MoveNext
  fullName: DrawnUi.Draw.DataContextIterator.MoveNext
- uid: System.Collections.IEnumerator.MoveNext
  commentId: M:System.Collections.IEnumerator.MoveNext
  parent: System.Collections.IEnumerator
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerator.movenext
  name: MoveNext()
  nameWithType: IEnumerator.MoveNext()
  fullName: System.Collections.IEnumerator.MoveNext()
  spec.csharp:
  - uid: System.Collections.IEnumerator.MoveNext
    name: MoveNext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerator.movenext
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.IEnumerator.MoveNext
    name: MoveNext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerator.movenext
  - name: (
  - name: )
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: System.NotSupportedException
  commentId: T:System.NotSupportedException
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.notsupportedexception
  name: NotSupportedException
  nameWithType: NotSupportedException
  fullName: System.NotSupportedException
- uid: DrawnUi.Draw.DataContextIterator.Reset*
  commentId: Overload:DrawnUi.Draw.DataContextIterator.Reset
  href: DrawnUi.Draw.DataContextIterator.html#DrawnUi_Draw_DataContextIterator_Reset
  name: Reset
  nameWithType: DataContextIterator.Reset
  fullName: DrawnUi.Draw.DataContextIterator.Reset
- uid: System.Collections.IEnumerator.Reset
  commentId: M:System.Collections.IEnumerator.Reset
  parent: System.Collections.IEnumerator
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerator.reset
  name: Reset()
  nameWithType: IEnumerator.Reset()
  fullName: System.Collections.IEnumerator.Reset()
  spec.csharp:
  - uid: System.Collections.IEnumerator.Reset
    name: Reset
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerator.reset
  - name: (
  - name: )
  spec.vb:
  - uid: System.Collections.IEnumerator.Reset
    name: Reset
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.ienumerator.reset
  - name: (
  - name: )
- uid: DrawnUi.Draw.DataContextIterator.Dispose*
  commentId: Overload:DrawnUi.Draw.DataContextIterator.Dispose
  href: DrawnUi.Draw.DataContextIterator.html#DrawnUi_Draw_DataContextIterator_Dispose
  name: Dispose
  nameWithType: DataContextIterator.Dispose
  fullName: DrawnUi.Draw.DataContextIterator.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
