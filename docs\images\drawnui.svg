<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1280" height="1024" viewBox="0 0 1280 1024" xml:space="preserve">
<desc>Created with Fabric.js 5.3.0</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 640 512)" id="background-logo"  >
<rect style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,255,255); fill-opacity: 0; fill-rule: nonzero; opacity: 1;"  paint-order="stroke"  x="-640" y="-512" rx="0" ry="0" width="1280" height="1024" />
</g>
<g transform="matrix(1.921153846153846 0 0 1.921153846153846 639.9999999999999 408.57468269230765)" id="logo-logo"  >
<g style=""  paint-order="stroke"   >
		<g transform="matrix(0.6078773318342079 0 0 0.6078773318342079 -0.5215298570330447 0)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(16,40,58); fill-rule: nonzero; opacity: 1;"  paint-order="stroke"  transform=" translate(-499.14208627274667, -499.9995394508727)" d="M 671.5499878 361.2199707 C 669.8499756000001 341.0599976 657.039978 322.72998049999995 637.789978 315.6400146 C 606.8699951 304.45001219999995 568.3900146 312.70001219999995 537.8400268 321.09997549999997 C 477.33001700000005 339.32000719999996 419.7199706 367.6400146 370.01000970000007 406.830017 C 371.9999999000001 406.869995 373.83001700000005 406.9099731 375.22998040000004 406.9400024 C 380.52001950000005 407.0300293 389.09002680000003 411.71997070000003 392.59002680000003 421.92999260000005 C 417.66998290000004 402.60998530000006 445.41998290000004 386.3200072000001 473.77001950000005 372.40002430000004 C 510.09002680000003 355.01000970000007 548.4600219 340.20001210000004 588.4799804 335.05999740000004 C 598.1900024 333.9299925 607.9000243 333.55999740000004 616.8800048 334.69000230000006 C 623.3499755 335.53997790000005 629.2399902000001 336.95001210000004 634.5900268 340.0700072000001 C 660.1099853000001 356.5200194000001 645.5100097000001 395.1500243000001 635.0599975 417.6799926000001 C 612.7499999 463.5300292000001 580.2000121 504.0300292000001 546.5200195 542.2000121000001 C 532.8099975 557.7199706000001 518.8400268 571.7000121000001 502.98999019999997 584.8800048 C 471.8499756 610.9999999 438.03002929999997 635.3800048 402.0900268 654.2199706 C 393.15002439999995 658.8499755 384.0300292 663.0599975 374.8800048 666.1799926 C 367.3599853 668.5999755 352.2899779 673.2299804 346.1099853 667.6099853 C 340.9899902 661.2299803999999 355.0999755 637.330017 359.24999990000003 630.0999754999999 C 372.08001700000005 607.9699705999999 387.9699706 585.9000242999999 398.2100219 561.6199949999999 C 404.7800292 545.9600218999999 408.6099853 524.5900267 385.9899902 521.7899778999999 C 388.4400024 522.7299803 390.8099975 523.8300169999999 392.84997560000005 525.3099973999999 C 407.7899780000001 535.7700193999999 389.59997560000005 560.0599973999999 382.54998780000005 570.8599852 C 367.90002440000006 592.0300291 350.25000000000006 612.3200072 336.7899780000001 635.0800169 C 329.59997560000005 647.8499754000001 320.9799804000001 664.9299925 330.8300171000001 678.9000242000001 C 348.8699951000001 701.8400266000001 390.5200195000001 682.9299925 411.5300293000001 673.1099851000001 C 459.3499756000001 648.8800046000001 502.8099976000001 617.6900022000001 541.7000122000001 580.9400022000001 C 564.289978 558.3400266000001 584.7399902000001 533.9999998000001 604.2700195000001 508.7299802000001 C 629.9600219000001 474.3699948000001 655.2500000000001 438.2000119000001 667.6699829000002 396.4099729000001 C 670.7999878000002 385.05999730000013 672.7000122000002 373.29998750000016 671.5499878000002 361.21997050000016 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.6078773318342079 0 0 0.6078773318342079 12.423108948537504 -65.96507235031092)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(120,84,158); fill-rule: nonzero; opacity: 1;"  paint-order="stroke"  transform=" translate(-520.436907, -391.48246059999997)" d="M 533.0831262 404.9482253 C 518.8168796 418.34620789999997 501.5898965 423.1785906 494.6055825 415.7416541 C 487.62126850000004 408.3047176 493.5244412 391.4146785 507.7906878 378.0166959 C 522.0569344 364.6187133 539.2839175 359.7863306 546.2682315 367.2232671 C 553.2525455 374.6602036 547.3493728 391.5502427 533.0831261999999 404.9482253 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.6078773318342079 0 0 0.6078773318342079 61.89903784092763 -69.61233634131617)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(75,142,249); fill-rule: nonzero; opacity: 1;"  paint-order="stroke"  transform=" translate(-601.8282113, -385.48246059999997)" d="M 614.4744305 398.9482253 C 600.2081839 412.34620789999997 582.9812008 417.1785906 575.9968868000001 409.7416541 C 569.0125728 402.3047176 574.9157455000001 385.4146785 589.1819921000001 372.0166959 C 603.4482387000002 358.6187133 620.6752218000001 353.7863306 627.6595358000001 361.2232671 C 634.6438498000001 368.6602036 628.7406771000001 385.5502427 614.4744305 398.9482253 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.6078773318342079 0 0 0.6078773318342079 38.746840346022054 -20.187856848962355)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(228,161,48); fill-rule: nonzero; opacity: 1;"  paint-order="stroke"  transform=" translate(-563.7412548, -466.7891271)" d="M 576.387474 480.2548918 C 562.1212274 493.6528744 544.8942443 498.4852571 537.9099303 491.0483206 C 530.9256163 483.6113841 536.828789 466.721345 551.0950356000001 453.3233624 C 565.3612822000001 439.92537980000003 582.5882653000001 435.0929971 589.5725793 442.5299336 C 596.5568933000001 449.9668701 590.6537206 466.8569092 576.387474 480.2548918 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.6078773318342079 0 0 0.6078773318342079 2.825891139447606 21.317552584841508)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(16,40,58); fill-rule: nonzero; opacity: 1;"  paint-order="stroke"  transform=" translate(-504.6488239337483, -535.0683789493254)" d="M 671.5232007 645.1868587 L 526.7797315 538.7549739 C 533.9137647 528.1300348999999 535.8223951 517.4294734 530.6431959 511.9169002 C 524.6197584 505.505096 510.9638746 508.22964429999996 498.1633985 517.7065731 L 408.5932081 451.8468929 L 393.5632398 473.3868709 L 483.36426520000003 532.8528743 C 475.81031010000004 543.7227473 473.6887891 554.8010555000001 478.98322270000006 560.4368587 C 485.35901620000004 567.2328792000001 500.28614020000003 563.7865291 513.6987378 552.9268489 L 665.6732251 653.5568538 C 666.5432202 654.1268611 667.5232007 654.4068904 668.4932324 654.4068904 C 670.1032178 654.4068904 671.6931836 653.6468805999999 672.6832348 652.2268977 C 674.2732006 649.9468684 673.7532421000001 646.8268733 671.5232006 645.1868586 Z M 483.4732129 554.6168513 C 480.41321530000005 551.3678889 481.58625 543.6567073 487.09326910000004 535.3222346 L 500.79328130000005 544.3943171 C 501.81061530000005 545.0680232 503.1361158 545.0337824 504.11884280000004 544.3105769 C 505.6633985 543.1739802 507.22864500000003 541.8760676 508.78321050000005 540.4169001 C 511.13495610000007 538.2078547 513.1987989 535.880279 514.9068677 533.5232844 C 515.8288648 532.2510066 515.5143506000001 530.4645075 514.2484815 529.5337214 L 501.87976810000004 520.4391779 C 507.79657720000006 516.3965754 513.8681104 514.0368952 518.7732007000001 514.0368952 C 521.3632276000001 514.0368952 523.3032300000001 514.7068781 524.5532300000001 516.0368952 C 526.5932080000001 518.2068781 526.7631909000002 522.3168635 525.0131909000002 527.3068537 C 522.9732129000001 533.1568903 518.6632153000002 539.3668513 512.8931958000002 544.7868952 C 505.1232373000002 552.0868830000001 496.06323970000017 556.6168513 489.2432324000002 556.6168513 C 486.66321530000016 556.6168513 484.71320310000016 555.9468684 483.4732129000002 554.6168513 Z M 377.3532178 460.9869075 C 377.3532178 460.9869075 365.21320319999995 465.8168636 352.9932324 457.28689529999997 C 340.7631909 448.75686599999995 344.2132031 429.40689039999995 335.6931836 416.966888 C 350.3132398 420.67690999999996 367.2832105 410.77688559999996 379.5032422 419.3068538 C 391.72321289999996 427.8368831 391.37323729999997 440.9068904 391.37323729999997 440.9068904 L 377.3532178 460.98690750000003 Z M 408.0332105 443.8168636 L 385.8231885 475.6168514 L 375.30323000000004 472.6568905 L 401.6232373 434.95687829999997 L 408.03321040000003 443.8168637 Z" stroke-linecap="round" />
</g>
		<g transform="matrix(0.6078773318342079 0 0 0.6078773318342079 -44.646736447588125 60.05195092275926)"  >
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(242,85,54); fill-rule: nonzero; opacity: 1;"  paint-order="stroke"  transform=" translate(-426.5530880000001, -598.7891270499999)" d="M 438.2603502 613.0787629 C 423.1212569 625.4819715 405.6070771 629.1390709 399.1413347 621.2471229 C 392.6755923 613.355175 399.70673250000004 596.9026998 414.8458258 584.4994912 C 429.9849191 572.0962826 447.4990989 568.4391832 453.9648413 576.3311312 C 460.4305837 584.2230791 453.39944349999996 600.6755542999999 438.2603502 613.0787628999999 Z" stroke-linecap="round" />
</g>
</g>
</g>
<g transform="matrix(1.9211538461538462 0 0 1.9211538461538462 639.9433259615383 719.293447011848)" id="text-logo"  >
<g style=""  paint-order="stroke"   >
		<g transform="matrix(1 0 0 1 0 0)" id="text-logo-path-0"  >
<path style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(20,44,60); fill-rule: nonzero; opacity: 1;"  paint-order="stroke"  transform=" translate(-135.23, 20.475)" d="M 3.66 -41.42 L 19.18 -41.42 Q 25.72 -41.42 30.62 -38.82 Q 35.52 -36.23 38.2 -31.54 Q 40.89 -26.85 40.89 -20.71 L 40.89 -20.71 Q 40.89 -14.63 38.2 -9.91 Q 35.52 -5.19 30.59 -2.6 Q 25.67 0 19.18 0 L 19.18 0 L 3.66 0 L 3.66 -41.42 Z M 13.75 -8.73 L 18.53 -8.73 Q 24.25 -8.73 27.44 -11.86 Q 30.62 -14.99 30.62 -20.71 L 30.62 -20.71 Q 30.62 -26.43 27.44 -29.62 Q 24.25 -32.8 18.53 -32.8 L 18.53 -32.8 L 13.75 -32.8 L 13.75 -8.73 Z M 58.7 -32.92 L 58.7 -27.44 Q 60.48 -30.15 63.13 -31.71 Q 65.78 -33.28 69.03 -33.28 L 69.03 -33.28 L 69.03 -22.6 L 66.26 -22.6 Q 62.48 -22.6 60.59 -20.97 Q 58.7 -19.35 58.7 -15.28 L 58.7 -15.28 L 58.7 0 L 48.62 0 L 48.62 -32.92 L 58.7 -32.92 Z M 73.93 -16.52 L 73.93 -16.52 Q 73.93 -21.59 75.84 -25.43 Q 77.76 -29.26 81.07 -31.33 Q 84.37 -33.39 88.44 -33.39 L 88.44 -33.39 Q 91.92 -33.39 94.55 -31.98 Q 97.17 -30.56 98.59 -28.26 L 98.59 -28.26 L 98.59 -32.92 L 108.68 -32.92 L 108.68 0 L 98.59 0 L 98.59 -4.66 Q 97.11 -2.36 94.49 -0.94 Q 91.86 0.47 88.38 0.47 L 88.38 0.47 Q 84.37 0.47 81.07 -1.62 Q 77.76 -3.72 75.84 -7.58 Q 73.93 -11.45 73.93 -16.52 Z M 98.59 -16.46 L 98.59 -16.46 Q 98.59 -20.24 96.49 -22.42 Q 94.4 -24.6 91.39 -24.6 L 91.39 -24.6 Q 88.38 -24.6 86.29 -22.45 Q 84.19 -20.3 84.19 -16.52 L 84.19 -16.52 Q 84.19 -12.74 86.29 -10.53 Q 88.38 -8.32 91.39 -8.32 L 91.39 -8.32 Q 94.4 -8.32 96.49 -10.5 Q 98.59 -12.69 98.59 -16.46 Z M 155.82 -32.92 L 165.32 -32.92 L 156.41 0 L 145.26 0 L 140.07 -21.36 L 134.7 0 L 123.61 0 L 114.64 -32.92 L 124.73 -32.92 L 129.39 -9.38 L 134.93 -32.92 L 145.61 -32.92 L 151.22 -9.5 L 155.82 -32.92 Z M 191.28 -33.28 L 191.28 -33.28 Q 197.06 -33.28 200.51 -29.53 Q 203.96 -25.78 203.96 -19.23 L 203.96 -19.23 L 203.96 0 L 193.93 0 L 193.93 -17.88 Q 193.93 -21.18 192.22 -23.01 Q 190.51 -24.84 187.62 -24.84 L 187.62 -24.84 Q 184.73 -24.84 183.02 -23.01 Q 181.31 -21.18 181.31 -17.88 L 181.31 -17.88 L 181.31 0 L 171.22 0 L 171.22 -32.92 L 181.31 -32.92 L 181.31 -28.56 Q 182.84 -30.74 185.44 -32.01 Q 188.03 -33.28 191.28 -33.28 Z M 212.87 -41.42 L 222.96 -41.42 L 222.96 -16.64 Q 222.96 -12.92 224.79 -10.92 Q 226.62 -8.91 230.16 -8.91 L 230.16 -8.91 Q 233.7 -8.91 235.59 -10.92 Q 237.48 -12.92 237.48 -16.64 L 237.48 -16.64 L 237.48 -41.42 L 247.56 -41.42 L 247.56 -16.7 Q 247.56 -11.15 245.2 -7.32 Q 242.84 -3.48 238.86 -1.53 Q 234.88 0.41 229.98 0.41 L 229.98 0.41 Q 225.09 0.41 221.22 -1.5 Q 217.36 -3.42 215.11 -7.29 Q 212.87 -11.15 212.87 -16.7 L 212.87 -16.7 L 212.87 -41.42 Z M 256.71 -41.42 L 266.8 -41.42 L 266.8 0 L 256.71 0 L 256.71 -41.42 Z" stroke-linecap="round" />
</g>
</g>
</g>
</svg>