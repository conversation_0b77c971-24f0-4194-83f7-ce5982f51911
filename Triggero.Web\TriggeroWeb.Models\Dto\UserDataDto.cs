using Newtonsoft.Json;

namespace Triggero.Domain.Models.Dto;

public class UserDataDto : WithDomainInfoDto
{
    [JsonProperty("ava")]
    public AvatarDto Avatar { get; set; }

    [JsonProperty("name")]
    public string Name { get; set; }

    [JsonProperty("phone")]
    public string Phone { get; set; }

    [JsonProperty("email")]
    public string Email { get; set; }

    [JsonProperty("role")]
    public string Role { get; set; }

    [JsonProperty("payed")]
    public DateTime? LastPayed { get; set; }

    [JsonProperty("sub")]
    public SubscriptionDto Subscription { get; set; }
}