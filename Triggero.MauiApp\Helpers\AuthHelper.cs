using MobileAPIWrapper;
using System.Text.Json;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Extensions.Helpers.Modules;
using Triggero.MauiMobileApp.Views.Pages.Auth;
using Triggero.Models.General;
using Triggero.Models.General.Settings;
using Triggero.MauiMobileApp.Helpers;

namespace Triggero.MauiMobileApp.Extensions.Helpers
{
    public static class AuthHelper
    {

        static AuthHelper()
        {
            // Use mobile-optimized HTTP client instead of RestSharp
            MobileApiRequestHelper.OnUnauthorized += (sender, args) =>
            {
                OnUnauthorized?.Invoke(null, null);

                Logout();

                App.SetMainPage(new LoginStartPage());
            };
        }

        public static EventHandler OnUnauthorized;

        public static int UserId { get; set; }

        public static User User { get; set; }

        public static bool IsAuthorized => UserId > 0;

        [Obsolete]
        public static async Task<bool> CheckIsFreeVersionAsync()
        {
            if (Device.RuntimePlatform == Device.iOS)
            {
                return await TriggeroMobileAPI.Common.CheckHui();
            }

            return false;
        }

        public static void SetupAuthorization(string authToken)
        {
            // Use mobile-optimized HTTP client instead of RestSharp
            MobileApiRequestHelper.SetBearerToken(authToken);

            ApplicationState.ConfigData.AuthToken = authToken;
        }

        public static void Login(User user, string authToken)
        {
            SetupAuthorization(authToken);

            if (string.IsNullOrEmpty(authToken))
            {
                user = null;
            }

            ApplyUser(user);
        }

        static object lockUser = new();

        static void ApplyUser(User user)
        {
            lock (lockUser)
            {
                User = user;

                if (User == null)
                {
                    UserId = 0;
                    ApplicationState.ConfigData.AuthToken = null;
                }
                else
                {
                    UserId = User.Id;

                    if (User.UserSubscription == null)
                    {
                        User.UserSubscription = new();
                    }

                    if (Constants.IsFreeVersion)
                    {
                        User.UserSubscription.SubscriptionBefore = DateTime.MaxValue;
                        User.UserSubscription.SubscriptionType = SubscriptionType.Full;
                    }
                }

                //ApplicationState.ConfigData.AuthToken = ... must be set manually before calling ApplyUser
                ApplicationState.ConfigData.UserId = UserId;
                ApplicationState.ConfigData.User = User;
                ApplicationState.ConfigData.SaveChangesToMemory();

                GlobalEvents.OnUserPropertyChanged(user);

                Monitor.PulseAll(lockUser);
            }


        }

        public static void Logout()
        {
            SetupAuthorization(null);

            ApplyUser(null);
        }


        [System.Text.Json.Serialization.JsonIgnore]
        private static DateTime lastUserGettingTime;

        public static async Task ReloadUser()
        {
            await GetUser(true);
        }

        public static async Task<User> GetUser(bool reload = false)
        {
            User user = null;

            if (ConnectionHelper.HasInternet()
                && UserId != 0 && (lastUserGettingTime.AddMinutes(3) < DateTime.Now || reload))
            {
                lastUserGettingTime = DateTime.Now;

                try
                {
                    user = await TriggeroMobileAPI.Account.MyProfileFull();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            }

            if (user == null)
            {
                user = User;
            }

            ApplyUser(user);

            return User;
        }

        public async static Task ChangeUserName(string name)
        {
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.GeneralMethods.UserMethods.UsersMethods.SetName(UserId, name);
            }
            else
            {
                string url = MobileAPIWrapper.Methods.General.Users.UsersMethods.BASE_HOST + $"SetName?userId={UserId}&name={name}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put));
            }
            User.Name = name;
            ApplicationState.ConfigData.User.Name = name;
            ApplicationState.SaveToFile(ApplicationState.ConfigData, ConfigData.FilePath);
        }


        public async static Task<bool> SaveNotificationSettings(NotificationSettings settings)
        {
            try
            {
                if (ConnectionHelper.HasInternet())
                {
                    var remote = await TriggeroMobileAPI.GeneralMethods.UserMethods.UsersMethods.UpdateNotificationSettings(settings);
                    if (remote != null)
                    {
                        settings = remote;
                    }
                    else
                    {
                        return false; //error
                    }
                }
                else
                {
                    string url = MobileAPIWrapper.Methods.General.Users.UsersMethods.BASE_HOST + $"UpdateNotificationSettings";

                    ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put, settings));
                }

                User.NotificationSettings = settings;
                ApplicationState.ConfigData.User.NotificationSettings = settings;

                ApplicationState.SaveToFile(ApplicationState.ConfigData, ConfigData.FilePath);

                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);

                return false; //error
            }
        }

        public async static Task SetEmailToSendReports(string email)
        {
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.GeneralMethods.UserMethods.UsersMethods.SetEmailToSendReports(UserId, email);
            }
            else
            {
                string url = MobileAPIWrapper.Methods.General.Users.UsersMethods.BASE_HOST + $"SetEmailToSendReports?userId={UserId}&email={email}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put));
            }
            User.EmailToSendReports = email;
            ApplicationState.ConfigData.User.EmailToSendReports = email;
            ApplicationState.SaveToFile(ApplicationState.ConfigData, ConfigData.FilePath);
        }

        public async static Task SetUserAvatar(UserAvatar avatar)
        {
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.GeneralMethods.UserMethods.UsersMethods.SetUserAvatar(UserId, avatar.Id);
            }
            else
            {
                string url = MobileAPIWrapper.Methods.General.Users.UsersMethods.BASE_HOST + $"SetUserAvatar?userId={UserId}&avatarId={avatar.Id}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put));
            }
            User.Avatar = avatar;
            User.AvatarId = avatar.Id;
            ApplicationState.ConfigData.User = User;
            ApplicationState.SaveToFile(ApplicationState.ConfigData, ConfigData.FilePath);
        }

        public static SubscriptionType UserSubscription
        {
            get
            {
                if (User != null && User.UserSubscription != null)
                {
                    return User.UserSubscription.SubscriptionType;
                }
                return SubscriptionType.None;
            }
        }

        public static bool IsTestsActivated
        {
            get
            {
                if (User != null && User.UserSubscription != null)
                {
                    return User.UserSubscription.IsTestsActivated;
                }
                return false;
            }
        }

        public static bool IsChatBotActivated
        {
            get
            {
                if (User != null && User.UserSubscription != null)
                {
                    return User.UserSubscription.IsChatBotActivated;
                }
                return false;
            }
        }

        public static bool IsLibraryActivated
        {
            get
            {
                if (User != null && User.UserSubscription != null)
                {
                    return User.UserSubscription.IsLibraryActivated;
                }
                return false;
            }
        }

        public static bool IsSubscriptionActive
        {
            get
            {
                if (User != null && User.UserSubscription != null)
                {
                    return User.UserSubscription.IsSubscriptionActive;
                }
                return false;
            }
        }

        public static bool IsSubscriptionEnded
        {
            get
            {
                if (User != null && User.UserSubscription != null)
                {
                    return User.UserSubscription.IsSubscriptionEnded;
                }
                return false;
            }
        }

        public static bool TrialActivated
        {
            get
            {
                if (User != null && User.UserSubscription != null)
                {
                    return User.UserSubscription.TrialActivated;
                }
                return false;
            }
        }



    }
}
