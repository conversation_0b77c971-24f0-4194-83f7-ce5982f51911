﻿<?xml version="1.0" encoding="utf-8"?>

<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Auth.SMSCodePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"

    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    x:Name="this">
    <ContentPage.Content>
        <Grid BackgroundColor="#FFFFFF">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="248" />
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>


                <Grid Grid.Row="0">

                    <ImageButton
                        Margin="20,0,0,0"
                        Padding="0"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=Close}"
                        CornerRadius="0"
                        HeightRequest="56"
                        HorizontalOptions="Start"
                        Source="buttonBackBordered.png"
                        VerticalOptions="Center"
                        WidthRequest="56" />

                    <Label
                        Margin="20,0,0,20"
                        FontAttributes="Bold"
                        FontSize="22"
                        HorizontalOptions="Start"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.EnterSMSCode.EnterSMSCode}"
                        TextColor="{x:StaticResource greyTextColor}"
                        VerticalOptions="End" />

                </Grid>

                <pancakeview:PancakeView
                    Grid.Row="1"
                    Padding="0"
                    BackgroundColor="#FFFFFF"
                    StrokeShape="RoundRectangle 15,15,0,0">
                    <Grid>
                        <StackLayout
                            Margin="30,20,30,0"
                            Spacing="0">


                            <Grid
                                ColumnSpacing="20"
                                HeightRequest="62"
                                VerticalOptions="Start">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*" />
                                    <ColumnDefinition Width="1*" />
                                    <ColumnDefinition Width="1*" />
                                    <ColumnDefinition Width="1*" />
                                </Grid.ColumnDefinitions>

                                <Frame
                                    Grid.Column="0"
                                    Padding="0"
                                    BackgroundColor="#F5F6FA"
                                    CornerRadius="10"
                                    HasShadow="False"
                                    HorizontalOptions="Center"
                                    IsClippedToBounds="True"
                                    WidthRequest="64">
                                    <!--<editors:TextEdit
                                        x:Name="textEdit1"
                                        Text=""
                                        MaxCharacterCount="1"
                                        Keyboard="Numeric"
                                        TextChanged="onTextChanged"
                                        Grid.Column="0"
                                        BoxPadding="0,0,0,0"
                                        BoxMode="Outlined"
                                        BackgroundColor="Transparent"
                                        Style="{x:StaticResource smsCodeTextEdit}"
                                        VerticalOptions="Fill"
                                        TextHorizontalAlignment="Center"/>-->
                                    <Entry
                                        x:Name="textEdit1"
                                        Grid.Column="0"
                                        Margin="{x:OnPlatform Android='0,0,0,-10',
                                                            iOS='0,0,0,0'}"
                                        BackgroundColor="Transparent"
                                        IsReadOnly="{Binding Source={x:Reference this}, Path=EditLocked}"
                                        Keyboard="Numeric"
                                        Style="{x:StaticResource smsCodeEntry}"
                                        Text=""
                                        TextChanged="onTextChanged"
                                        VerticalOptions="Fill"
                                        VerticalTextAlignment="{x:OnPlatform Android='Start',
                                                                           iOS='Center'}" />

                                </Frame>


                                <Frame
                                    Grid.Column="1"
                                    Padding="0"
                                    BackgroundColor="#F5F6FA"
                                    CornerRadius="10"
                                    HasShadow="False"
                                    HorizontalOptions="Center"
                                    IsClippedToBounds="True"
                                    WidthRequest="64">
                                    <!--<editors:TextEdit
                                        x:Name="textEdit2"
                                        Text=""
                                        MaxCharacterCount="1"
                                        Keyboard="Numeric"
                                        TextChanged="onTextChanged"
                                        Grid.Column="0"
                                        BoxPadding="0,0,0,0"
                                        BoxMode="Outlined"
                                        BackgroundColor="Transparent"
                                        Style="{x:StaticResource smsCodeTextEdit}"
                                        VerticalOptions="Fill"
                                        TextHorizontalAlignment="Center"/>-->
                                    <Entry
                                        x:Name="textEdit2"
                                        Grid.Column="0"
                                        Margin="{x:OnPlatform Android='0,0,0,-10',
                                                            iOS='0,0,0,0'}"
                                        BackgroundColor="Transparent"
                                        IsReadOnly="{Binding Source={x:Reference this}, Path=EditLocked}"
                                        Keyboard="Numeric"
                                        Style="{x:StaticResource smsCodeEntry}"
                                        Text=""
                                        TextChanged="onTextChanged"
                                        VerticalOptions="Fill"
                                        VerticalTextAlignment="{x:OnPlatform Android='Start',
                                                                           iOS='Center'}" />
                                </Frame>

                                <Frame
                                    Grid.Column="2"
                                    Padding="0"
                                    BackgroundColor="#F5F6FA"
                                    CornerRadius="10"
                                    HasShadow="False"
                                    HorizontalOptions="Center"
                                    IsClippedToBounds="True"
                                    WidthRequest="64">
                                    <!--<editors:TextEdit
                                        x:Name="textEdit3"
                                        Text=""
                                        MaxCharacterCount="1"
                                        Keyboard="Numeric"
                                        TextChanged="onTextChanged"
                                        Grid.Column="0"
                                        BoxPadding="0,0,0,0"
                                        BoxMode="Outlined"
                                        BackgroundColor="Transparent"
                                        Style="{x:StaticResource smsCodeTextEdit}"
                                        VerticalOptions="Fill"
                                        TextHorizontalAlignment="Center"/>-->

                                    <Entry
                                        x:Name="textEdit3"
                                        Grid.Column="0"
                                        Margin="{x:OnPlatform Android='0,0,0,-10',
                                                            iOS='0,0,0,0'}"
                                        BackgroundColor="Transparent"
                                        IsReadOnly="{Binding Source={x:Reference this}, Path=EditLocked}"
                                        Keyboard="Numeric"
                                        Style="{x:StaticResource smsCodeEntry}"
                                        Text=""
                                        TextChanged="onTextChanged"
                                        VerticalOptions="Fill"
                                        VerticalTextAlignment="{x:OnPlatform Android='Start',
                                                                           iOS='Center'}" />

                                </Frame>

                                <Frame
                                    Grid.Column="3"
                                    Padding="0"
                                    BackgroundColor="#F5F6FA"
                                    CornerRadius="10"
                                    HasShadow="False"
                                    HorizontalOptions="Center"
                                    IsClippedToBounds="True"
                                    WidthRequest="64">

                                    <!--<editors:TextEdit
                                        x:Name="textEdit4"
                                        Text=""
                                        MaxCharacterCount="1"
                                        Keyboard="Numeric"
                                        TextChanged="onTextChanged"
                                        Grid.Column="0"
                                        BoxPadding="0,0,0,0"
                                        BoxMode="Outlined"
                                        BackgroundColor="Transparent"
                                        Style="{x:StaticResource smsCodeTextEdit}"
                                        VerticalOptions="Fill"
                                        TextHorizontalAlignment="Center"/>-->

                                    <Entry
                                        x:Name="textEdit4"
                                        Grid.Column="0"
                                        Margin="{x:OnPlatform Android='0,0,0,-10',
                                                            iOS='0,0,0,0'}"
                                        BackgroundColor="Transparent"
                                        IsReadOnly="{Binding Source={x:Reference this}, Path=EditLocked}"
                                        Keyboard="Numeric"
                                        Style="{x:StaticResource smsCodeEntry}"
                                        Text=""
                                        TextChanged="onTextChanged"
                                        VerticalOptions="Fill"
                                        VerticalTextAlignment="{x:OnPlatform Android='Start',
                                                                           iOS='Center'}" />

                                </Frame>

                            </Grid>

                            <Label
                                x:Name="errorLabel"
                                Margin="0,5,0,0"
                                FontSize="14"
                                HorizontalOptions="Center"
                                IsVisible="False"
                                Opacity="0.5"
                                Text=""
                                TextColor="{x:StaticResource blueColor}"
                                VerticalOptions="Start" />

                            <StackLayout
                                Margin="0,20,0,0"
                                HorizontalOptions="Center"
                                Orientation="Horizontal"
                                Spacing="12">
                                <Label
                                    FontSize="14"
                                    Opacity="0.5"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.EnterSMSCode.CodeNotReceived}"
                                    TextColor="{x:StaticResource greyTextColor}" />

                                <Label
                                    FontAttributes="Bold"
                                    FontSize="14"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Auth.EnterSMSCode.SendAgain}"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    TextDecorations="Underline">
                                    <Label.GestureRecognizers>
                                        <TapGestureRecognizer
                                            Command="{Binding Source={x:Reference this}, Path=SendCodeAgain}" />
                                    </Label.GestureRecognizers>
                                </Label>
                            </StackLayout>


                        </StackLayout>
                    </Grid>
                </pancakeview:PancakeView>

            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>