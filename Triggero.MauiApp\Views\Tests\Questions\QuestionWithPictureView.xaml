﻿<?xml version="1.0" encoding="UTF-8"?>
<models:BaseQuestionView
             
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:models="clr-namespace:Triggero.MauiPort.Models"
             x:Class="Triggero.Controls.Cards.Tests.Questions.QuestionWithPictureView"
             x:Name="this">
  <models:BaseQuestionView.Content>
      <StackLayout Spacing="15">

            <Label 
                x:Name="titleLabel"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                TextColor="{x:StaticResource greyTextColor}"
                FontSize="22"/>

            <Label 
                Margin="0,-7,0,0"
                x:Name="questiontypeLabel"
                HorizontalOptions="Center"
                HorizontalTextAlignment="Center"
                TextColor="{x:StaticResource greyTextColor}"
                Opacity="0.5"
                FontSize="14"/>


            <Frame
                Margin="0,25,0,0"
                CornerRadius="16"
                HasShadow="False"
                IsClippedToBounds="True"
                HeightRequest="220"
                Background="#E1EEF8"
                VerticalOptions="Start"
                Padding="0">
                <Image
                    x:Name="img"
                    Aspect="Fill"
                    VerticalOptions="Fill"
                    HorizontalOptions="Fill"/>
            </Frame>

            <StackLayout
                  x:Name="optionsLayout"
                  Spacing="15">



            </StackLayout>

        </StackLayout>
  </models:BaseQuestionView.Content>
</models:BaseQuestionView>