﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Chat.ChatBotRightTextMessage"
             x:Name="this">
  <ContentView.Content>
      <Frame
          HorizontalOptions="End"
          CornerRadius="12"
          BackgroundColor="{x:StaticResource blueColor}"
          HasShadow="False"
          Padding="5">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="60"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <Label 
                        TextColor="White"
                        FontSize="14"
                        Margin="20,5,0,3"
                        VerticalOptions="Start"
                        HorizontalOptions="End"
                        HorizontalTextAlignment="Start"
                        Text="{Binding Source={x:Reference this},Path=Message.Text}"/>
                </Grid>

                <Grid Grid.Column="1">
                    <StackLayout
                        Spacing="0"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        Orientation="Horizontal">
                        <Label 
                            x:Name="timeLabel"
                            TextColor="White"
                            Opacity="0.5"
                            FontSize="12"
                            Margin="0,8,0,0"
                            VerticalOptions="Start"
                            HorizontalOptions="Start"
                            Text=""/>
                        <Image
                            x:Name="readMsgIcon"
                            Source="chatReadMsgSign.png"  
                            Margin="7,10,0,0"
                            HorizontalOptions="Start"
                            VerticalOptions="Start"
                            WidthRequest="12"
                            HeightRequest="8"/>

                    </StackLayout>
                   
                </Grid>

            </Grid>
      </Frame>
  </ContentView.Content>
</ContentView>