### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.TextSpan
  commentId: T:DrawnUi.Draw.TextSpan
  id: TextSpan
  parent: DrawnU<PERSON>.Draw
  children:
  - DrawnUi.Draw.TextSpan.#ctor
  - DrawnUi.Draw.TextSpan.AutoFindFont
  - DrawnUi.Draw.TextSpan.BackgroundColor
  - DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered
  - DrawnUi.Draw.TextSpan.CommandTapped
  - DrawnUi.Draw.TextSpan.DebugString
  - DrawnUi.Draw.TextSpan.Default
  - DrawnUi.Draw.TextSpan.Dispose
  - DrawnUi.Draw.TextSpan.DrawingOffset
  - DrawnUi.Draw.TextSpan.FireTap
  - DrawnUi.Draw.TextSpan.FontDetectedWith
  - DrawnUi.Draw.TextSpan.FontFamily
  - DrawnUi.Draw.TextSpan.FontSize
  - DrawnUi.Draw.TextSpan.FontSizeProperty
  - DrawnUi.Draw.TextSpan.FontWeight
  - DrawnUi.Draw.TextSpan.ForceCaptureInput
  - DrawnUi.Draw.TextSpan.Glyphs
  - DrawnUi.Draw.TextSpan.HasDecorations
  - DrawnUi.Draw.TextSpan.HasSetColor
  - DrawnUi.Draw.TextSpan.HasSetFont
  - DrawnUi.Draw.TextSpan.HasSetSize
  - DrawnUi.Draw.TextSpan.HasTapHandler
  - DrawnUi.Draw.TextSpan.HitIsInside(System.Single,System.Single)
  - DrawnUi.Draw.TextSpan.IsBold
  - DrawnUi.Draw.TextSpan.IsItalic
  - DrawnUi.Draw.TextSpan.LineHeight
  - DrawnUi.Draw.TextSpan.LineSpacing
  - DrawnUi.Draw.TextSpan.NeedShape
  - DrawnUi.Draw.TextSpan.OnPropertyChanged(System.String)
  - DrawnUi.Draw.TextSpan.Paint
  - DrawnUi.Draw.TextSpan.ParagraphColor
  - DrawnUi.Draw.TextSpan.ParentControl
  - DrawnUi.Draw.TextSpan.Rects
  - DrawnUi.Draw.TextSpan.RenderingScale
  - DrawnUi.Draw.TextSpan.SetupPaint(System.Double,SkiaSharp.SKPaint)
  - DrawnUi.Draw.TextSpan.Shape
  - DrawnUi.Draw.TextSpan.Strikeout
  - DrawnUi.Draw.TextSpan.StrikeoutColor
  - DrawnUi.Draw.TextSpan.StrikeoutWidth
  - DrawnUi.Draw.TextSpan.Tag
  - DrawnUi.Draw.TextSpan.Tapped
  - DrawnUi.Draw.TextSpan.Text
  - DrawnUi.Draw.TextSpan.TextColor
  - DrawnUi.Draw.TextSpan.TextColorProperty
  - DrawnUi.Draw.TextSpan.TextFiltered
  - DrawnUi.Draw.TextSpan.TextProperty
  - DrawnUi.Draw.TextSpan.TypeFace
  - DrawnUi.Draw.TextSpan.Underline
  - DrawnUi.Draw.TextSpan.UnderlineWidth
  - DrawnUi.Draw.TextSpan.UpdateFont
  - DrawnUi.Draw.TextSpan._fontAutoSet
  langs:
  - csharp
  - vb
  name: TextSpan
  nameWithType: TextSpan
  fullName: DrawnUi.Draw.TextSpan
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TextSpan
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 7
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class TextSpan : Element, INotifyPropertyChanged, IElementController, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IElement, IDisposable'
    content.vb: Public Class TextSpan Inherits Element Implements INotifyPropertyChanged, IElementController, IVisualTreeElement, IEffectControlProvider, IToolTipElement, IContextFlyoutElement, IElement, IDisposable
  inheritance:
  - System.Object
  - Microsoft.Maui.Controls.BindableObject
  - Microsoft.Maui.Controls.Element
  derivedClasses:
  - DrawnUi.Draw.SvgSpan
  implements:
  - System.ComponentModel.INotifyPropertyChanged
  - Microsoft.Maui.Controls.IElementController
  - Microsoft.Maui.IVisualTreeElement
  - Microsoft.Maui.Controls.IEffectControlProvider
  - Microsoft.Maui.IToolTipElement
  - Microsoft.Maui.IContextFlyoutElement
  - Microsoft.Maui.IElement
  - System.IDisposable
  inheritedMembers:
  - Microsoft.Maui.Controls.Element.AutomationIdProperty
  - Microsoft.Maui.Controls.Element.ClassIdProperty
  - Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.ClearLogicalChildren
  - Microsoft.Maui.Controls.Element.FindByName(System.String)
  - Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  - Microsoft.Maui.Controls.Element.OnBindingContextChanged
  - Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  - Microsoft.Maui.Controls.Element.OnParentSet
  - Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  - Microsoft.Maui.Controls.Element.OnParentChanged
  - Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  - Microsoft.Maui.Controls.Element.OnHandlerChanged
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  - Microsoft.Maui.Controls.Element.AutomationId
  - Microsoft.Maui.Controls.Element.ClassId
  - Microsoft.Maui.Controls.Element.Effects
  - Microsoft.Maui.Controls.Element.Id
  - Microsoft.Maui.Controls.Element.StyleId
  - Microsoft.Maui.Controls.Element.Parent
  - Microsoft.Maui.Controls.Element.Handler
  - Microsoft.Maui.Controls.Element.ChildAdded
  - Microsoft.Maui.Controls.Element.ChildRemoved
  - Microsoft.Maui.Controls.Element.DescendantAdded
  - Microsoft.Maui.Controls.Element.DescendantRemoved
  - Microsoft.Maui.Controls.Element.ParentChanging
  - Microsoft.Maui.Controls.Element.ParentChanged
  - Microsoft.Maui.Controls.Element.HandlerChanging
  - Microsoft.Maui.Controls.Element.HandlerChanged
  - Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  - Microsoft.Maui.Controls.BindableObject.ApplyBindings
  - Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  - Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  - Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  - Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  - Microsoft.Maui.Controls.BindableObject.Dispatcher
  - Microsoft.Maui.Controls.BindableObject.BindingContext
  - Microsoft.Maui.Controls.BindableObject.PropertyChanged
  - Microsoft.Maui.Controls.BindableObject.PropertyChanging
  - Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - DrawnUi.Draw.TextSpan.DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  - Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  - Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.TextSpan.TextProperty
  commentId: F:DrawnUi.Draw.TextSpan.TextProperty
  id: TextProperty
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: TextProperty
  nameWithType: TextSpan.TextProperty
  fullName: DrawnUi.Draw.TextSpan.TextProperty
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TextProperty
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 13
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static readonly BindableProperty TextProperty
    return:
      type: Microsoft.Maui.Controls.BindableProperty
    content.vb: Public Shared ReadOnly TextProperty As BindableProperty
- uid: DrawnUi.Draw.TextSpan.Text
  commentId: P:DrawnUi.Draw.TextSpan.Text
  id: Text
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Text
  nameWithType: TextSpan.Text
  fullName: DrawnUi.Draw.TextSpan.Text
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Text
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string Text { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Text As String
  overload: DrawnUi.Draw.TextSpan.Text*
- uid: DrawnUi.Draw.TextSpan.Default
  commentId: P:DrawnUi.Draw.TextSpan.Default
  id: Default
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Default
  nameWithType: TextSpan.Default
  fullName: DrawnUi.Draw.TextSpan.Default
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Default
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 25
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static TextSpan Default { get; }
    parameters: []
    return:
      type: DrawnUi.Draw.TextSpan
    content.vb: Public Shared ReadOnly Property [Default] As TextSpan
  overload: DrawnUi.Draw.TextSpan.Default*
- uid: DrawnUi.Draw.TextSpan.DebugString
  commentId: P:DrawnUi.Draw.TextSpan.DebugString
  id: DebugString
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: DebugString
  nameWithType: TextSpan.DebugString
  fullName: DrawnUi.Draw.TextSpan.DebugString
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DebugString
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 33
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string DebugString { get; }
    parameters: []
    return:
      type: System.String
    content.vb: Public ReadOnly Property DebugString As String
  overload: DrawnUi.Draw.TextSpan.DebugString*
- uid: DrawnUi.Draw.TextSpan.RenderingScale
  commentId: P:DrawnUi.Draw.TextSpan.RenderingScale
  id: RenderingScale
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: RenderingScale
  nameWithType: TextSpan.RenderingScale
  fullName: DrawnUi.Draw.TextSpan.RenderingScale
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: RenderingScale
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 41
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float RenderingScale { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property RenderingScale As Single
  overload: DrawnUi.Draw.TextSpan.RenderingScale*
- uid: DrawnUi.Draw.TextSpan.Glyphs
  commentId: P:DrawnUi.Draw.TextSpan.Glyphs
  id: Glyphs
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Glyphs
  nameWithType: TextSpan.Glyphs
  fullName: DrawnUi.Draw.TextSpan.Glyphs
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Glyphs
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 46
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Ig can be drawn char by char with char spacing etc we use this
  example: []
  syntax:
    content: public List<UsedGlyph> Glyphs { get; protected set; }
    parameters: []
    return:
      type: System.Collections.Generic.List{DrawnUi.Draw.UsedGlyph}
    content.vb: Public Property Glyphs As List(Of UsedGlyph)
  overload: DrawnUi.Draw.TextSpan.Glyphs*
- uid: DrawnUi.Draw.TextSpan.Shape
  commentId: P:DrawnUi.Draw.TextSpan.Shape
  id: Shape
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Shape
  nameWithType: TextSpan.Shape
  fullName: DrawnUi.Draw.TextSpan.Shape
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Shape
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 51
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: If text can be drawn only shaped we use this
  example: []
  syntax:
    content: public string Shape { get; protected set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Shape As String
  overload: DrawnUi.Draw.TextSpan.Shape*
- uid: DrawnUi.Draw.TextSpan.TextFiltered
  commentId: P:DrawnUi.Draw.TextSpan.TextFiltered
  id: TextFiltered
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: TextFiltered
  nameWithType: TextSpan.TextFiltered
  fullName: DrawnUi.Draw.TextSpan.TextFiltered
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TextFiltered
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 54
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string TextFiltered { get; protected set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property TextFiltered As String
  overload: DrawnUi.Draw.TextSpan.TextFiltered*
- uid: DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered
  commentId: M:DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered
  id: CheckGlyphsCanBeRendered
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: CheckGlyphsCanBeRendered()
  nameWithType: TextSpan.CheckGlyphsCanBeRendered()
  fullName: DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CheckGlyphsCanBeRendered
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 59
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Parse glyphs, setup typeface, replace unrenderable glyphs with fallback character
  example: []
  syntax:
    content: public void CheckGlyphsCanBeRendered()
    content.vb: Public Sub CheckGlyphsCanBeRendered()
  overload: DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered*
- uid: DrawnUi.Draw.TextSpan.SetupPaint(System.Double,SkiaSharp.SKPaint)
  commentId: M:DrawnUi.Draw.TextSpan.SetupPaint(System.Double,SkiaSharp.SKPaint)
  id: SetupPaint(System.Double,SkiaSharp.SKPaint)
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: SetupPaint(double, SKPaint)
  nameWithType: TextSpan.SetupPaint(double, SKPaint)
  fullName: DrawnUi.Draw.TextSpan.SetupPaint(double, SkiaSharp.SKPaint)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SetupPaint
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 105
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Update the paint with current format properties
  example: []
  syntax:
    content: public SKPaint SetupPaint(double scale, SKPaint defaultPaint)
    parameters:
    - id: scale
      type: System.Double
    - id: defaultPaint
      type: SkiaSharp.SKPaint
    return:
      type: SkiaSharp.SKPaint
    content.vb: Public Function SetupPaint(scale As Double, defaultPaint As SKPaint) As SKPaint
  overload: DrawnUi.Draw.TextSpan.SetupPaint*
  nameWithType.vb: TextSpan.SetupPaint(Double, SKPaint)
  fullName.vb: DrawnUi.Draw.TextSpan.SetupPaint(Double, SkiaSharp.SKPaint)
  name.vb: SetupPaint(Double, SKPaint)
- uid: DrawnUi.Draw.TextSpan.HasSetFont
  commentId: P:DrawnUi.Draw.TextSpan.HasSetFont
  id: HasSetFont
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: HasSetFont
  nameWithType: TextSpan.HasSetFont
  fullName: DrawnUi.Draw.TextSpan.HasSetFont
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HasSetFont
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 177
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool HasSetFont { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property HasSetFont As Boolean
  overload: DrawnUi.Draw.TextSpan.HasSetFont*
- uid: DrawnUi.Draw.TextSpan.HasSetSize
  commentId: P:DrawnUi.Draw.TextSpan.HasSetSize
  id: HasSetSize
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: HasSetSize
  nameWithType: TextSpan.HasSetSize
  fullName: DrawnUi.Draw.TextSpan.HasSetSize
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HasSetSize
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 178
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool HasSetSize { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property HasSetSize As Boolean
  overload: DrawnUi.Draw.TextSpan.HasSetSize*
- uid: DrawnUi.Draw.TextSpan.HasSetColor
  commentId: P:DrawnUi.Draw.TextSpan.HasSetColor
  id: HasSetColor
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: HasSetColor
  nameWithType: TextSpan.HasSetColor
  fullName: DrawnUi.Draw.TextSpan.HasSetColor
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HasSetColor
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 179
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool HasSetColor { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property HasSetColor As Boolean
  overload: DrawnUi.Draw.TextSpan.HasSetColor*
- uid: DrawnUi.Draw.TextSpan.Paint
  commentId: P:DrawnUi.Draw.TextSpan.Paint
  id: Paint
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Paint
  nameWithType: TextSpan.Paint
  fullName: DrawnUi.Draw.TextSpan.Paint
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Paint
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 181
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKPaint Paint { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKPaint
    content.vb: Public Property Paint As SKPaint
  overload: DrawnUi.Draw.TextSpan.Paint*
- uid: DrawnUi.Draw.TextSpan.TypeFace
  commentId: P:DrawnUi.Draw.TextSpan.TypeFace
  id: TypeFace
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: TypeFace
  nameWithType: TextSpan.TypeFace
  fullName: DrawnUi.Draw.TextSpan.TypeFace
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TypeFace
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 183
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SKTypeface TypeFace { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKTypeface
    content.vb: Public Property TypeFace As SKTypeface
  overload: DrawnUi.Draw.TextSpan.TypeFace*
- uid: DrawnUi.Draw.TextSpan.NeedShape
  commentId: P:DrawnUi.Draw.TextSpan.NeedShape
  id: NeedShape
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: NeedShape
  nameWithType: TextSpan.NeedShape
  fullName: DrawnUi.Draw.TextSpan.NeedShape
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: NeedShape
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 196
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool NeedShape { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property NeedShape As Boolean
  overload: DrawnUi.Draw.TextSpan.NeedShape*
- uid: DrawnUi.Draw.TextSpan.HasDecorations
  commentId: P:DrawnUi.Draw.TextSpan.HasDecorations
  id: HasDecorations
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: HasDecorations
  nameWithType: TextSpan.HasDecorations
  fullName: DrawnUi.Draw.TextSpan.HasDecorations
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HasDecorations
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 207
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool HasDecorations { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property HasDecorations As Boolean
  overload: DrawnUi.Draw.TextSpan.HasDecorations*
- uid: DrawnUi.Draw.TextSpan.Underline
  commentId: P:DrawnUi.Draw.TextSpan.Underline
  id: Underline
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Underline
  nameWithType: TextSpan.Underline
  fullName: DrawnUi.Draw.TextSpan.Underline
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Underline
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 216
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool Underline { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property Underline As Boolean
  overload: DrawnUi.Draw.TextSpan.Underline*
- uid: DrawnUi.Draw.TextSpan.UnderlineWidth
  commentId: P:DrawnUi.Draw.TextSpan.UnderlineWidth
  id: UnderlineWidth
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: UnderlineWidth
  nameWithType: TextSpan.UnderlineWidth
  fullName: DrawnUi.Draw.TextSpan.UnderlineWidth
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UnderlineWidth
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 236
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: In points, if set to negative will be in pixels instead.
  example: []
  syntax:
    content: public double UnderlineWidth { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property UnderlineWidth As Double
  overload: DrawnUi.Draw.TextSpan.UnderlineWidth*
- uid: DrawnUi.Draw.TextSpan.Strikeout
  commentId: P:DrawnUi.Draw.TextSpan.Strikeout
  id: Strikeout
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Strikeout
  nameWithType: TextSpan.Strikeout
  fullName: DrawnUi.Draw.TextSpan.Strikeout
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Strikeout
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 253
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool Strikeout { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property Strikeout As Boolean
  overload: DrawnUi.Draw.TextSpan.Strikeout*
- uid: DrawnUi.Draw.TextSpan.StrikeoutWidth
  commentId: P:DrawnUi.Draw.TextSpan.StrikeoutWidth
  id: StrikeoutWidth
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: StrikeoutWidth
  nameWithType: TextSpan.StrikeoutWidth
  fullName: DrawnUi.Draw.TextSpan.StrikeoutWidth
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StrikeoutWidth
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 273
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: In points
  example: []
  syntax:
    content: public double StrikeoutWidth { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property StrikeoutWidth As Double
  overload: DrawnUi.Draw.TextSpan.StrikeoutWidth*
- uid: DrawnUi.Draw.TextSpan.StrikeoutColor
  commentId: P:DrawnUi.Draw.TextSpan.StrikeoutColor
  id: StrikeoutColor
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: StrikeoutColor
  nameWithType: TextSpan.StrikeoutColor
  fullName: DrawnUi.Draw.TextSpan.StrikeoutColor
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StrikeoutColor
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 290
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Color StrikeoutColor { get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Graphics.Color
    content.vb: Public Property StrikeoutColor As Color
  overload: DrawnUi.Draw.TextSpan.StrikeoutColor*
- uid: DrawnUi.Draw.TextSpan.HasTapHandler
  commentId: P:DrawnUi.Draw.TextSpan.HasTapHandler
  id: HasTapHandler
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: HasTapHandler
  nameWithType: TextSpan.HasTapHandler
  fullName: DrawnUi.Draw.TextSpan.HasTapHandler
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HasTapHandler
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 309
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Will listen to gestures
  example: []
  syntax:
    content: public bool HasTapHandler { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property HasTapHandler As Boolean
  overload: DrawnUi.Draw.TextSpan.HasTapHandler*
- uid: DrawnUi.Draw.TextSpan.ForceCaptureInput
  commentId: P:DrawnUi.Draw.TextSpan.ForceCaptureInput
  id: ForceCaptureInput
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: ForceCaptureInput
  nameWithType: TextSpan.ForceCaptureInput
  fullName: DrawnUi.Draw.TextSpan.ForceCaptureInput
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ForceCaptureInput
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 320
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: When no tap handler or command are set this forces to listen to taps anyway
  example: []
  syntax:
    content: public bool ForceCaptureInput { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property ForceCaptureInput As Boolean
  overload: DrawnUi.Draw.TextSpan.ForceCaptureInput*
- uid: DrawnUi.Draw.TextSpan.HitIsInside(System.Single,System.Single)
  commentId: M:DrawnUi.Draw.TextSpan.HitIsInside(System.Single,System.Single)
  id: HitIsInside(System.Single,System.Single)
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: HitIsInside(float, float)
  nameWithType: TextSpan.HitIsInside(float, float)
  fullName: DrawnUi.Draw.TextSpan.HitIsInside(float, float)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: HitIsInside
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 322
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public virtual bool HitIsInside(float x, float y)
    parameters:
    - id: x
      type: System.Single
    - id: y
      type: System.Single
    return:
      type: System.Boolean
    content.vb: Public Overridable Function HitIsInside(x As Single, y As Single) As Boolean
  overload: DrawnUi.Draw.TextSpan.HitIsInside*
  nameWithType.vb: TextSpan.HitIsInside(Single, Single)
  fullName.vb: DrawnUi.Draw.TextSpan.HitIsInside(Single, Single)
  name.vb: HitIsInside(Single, Single)
- uid: DrawnUi.Draw.TextSpan.FireTap
  commentId: M:DrawnUi.Draw.TextSpan.FireTap
  id: FireTap
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: FireTap()
  nameWithType: TextSpan.FireTap()
  fullName: DrawnUi.Draw.TextSpan.FireTap()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FireTap
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 332
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public virtual void FireTap()
    content.vb: Public Overridable Sub FireTap()
  overload: DrawnUi.Draw.TextSpan.FireTap*
- uid: DrawnUi.Draw.TextSpan.Dispose
  commentId: M:DrawnUi.Draw.TextSpan.Dispose
  id: Dispose
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: TextSpan.Dispose()
  fullName: DrawnUi.Draw.TextSpan.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 338
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Performs application-defined tasks associated with freeing, releasing, or resetting unmanaged resources.
  example: []
  syntax:
    content: public virtual void Dispose()
    content.vb: Public Overridable Sub Dispose()
  overload: DrawnUi.Draw.TextSpan.Dispose*
  implements:
  - System.IDisposable.Dispose
- uid: DrawnUi.Draw.TextSpan.DrawingOffset
  commentId: P:DrawnUi.Draw.TextSpan.DrawingOffset
  id: DrawingOffset
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: DrawingOffset
  nameWithType: TextSpan.DrawingOffset
  fullName: DrawnUi.Draw.TextSpan.DrawingOffset
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: DrawingOffset
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 351
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Rendering offset, set when combining spans. Ofset of the first line.
  example: []
  syntax:
    content: public SKPoint DrawingOffset { get; set; }
    parameters: []
    return:
      type: SkiaSharp.SKPoint
    content.vb: Public Property DrawingOffset As SKPoint
  overload: DrawnUi.Draw.TextSpan.DrawingOffset*
- uid: DrawnUi.Draw.TextSpan.Tag
  commentId: P:DrawnUi.Draw.TextSpan.Tag
  id: Tag
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Tag
  nameWithType: TextSpan.Tag
  fullName: DrawnUi.Draw.TextSpan.Tag
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Tag
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 353
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string Tag { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Tag As String
  overload: DrawnUi.Draw.TextSpan.Tag*
- uid: DrawnUi.Draw.TextSpan.Rects
  commentId: F:DrawnUi.Draw.TextSpan.Rects
  id: Rects
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Rects
  nameWithType: TextSpan.Rects
  fullName: DrawnUi.Draw.TextSpan.Rects
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Rects
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 358
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Relative to DrawingRect
  example: []
  syntax:
    content: public readonly List<SKRect> Rects
    return:
      type: System.Collections.Generic.List{SkiaSharp.SKRect}
    content.vb: Public ReadOnly Rects As List(Of SKRect)
- uid: DrawnUi.Draw.TextSpan.CommandTapped
  commentId: P:DrawnUi.Draw.TextSpan.CommandTapped
  id: CommandTapped
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: CommandTapped
  nameWithType: TextSpan.CommandTapped
  fullName: DrawnUi.Draw.TextSpan.CommandTapped
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: CommandTapped
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 362
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ICommand CommandTapped { get; set; }
    parameters: []
    return:
      type: System.Windows.Input.ICommand
    content.vb: Public Property CommandTapped As ICommand
  overload: DrawnUi.Draw.TextSpan.CommandTapped*
- uid: DrawnUi.Draw.TextSpan.Tapped
  commentId: E:DrawnUi.Draw.TextSpan.Tapped
  id: Tapped
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: Tapped
  nameWithType: TextSpan.Tapped
  fullName: DrawnUi.Draw.TextSpan.Tapped
  type: Event
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Tapped
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 378
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public event EventHandler<SkiaControl.ControlTappedEventArgs> Tapped
    return:
      type: System.EventHandler{DrawnUi.Draw.SkiaControl.ControlTappedEventArgs}
    content.vb: Public Event Tapped As EventHandler(Of SkiaControl.ControlTappedEventArgs)
- uid: DrawnUi.Draw.TextSpan.ParentControl
  commentId: P:DrawnUi.Draw.TextSpan.ParentControl
  id: ParentControl
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: ParentControl
  nameWithType: TextSpan.ParentControl
  fullName: DrawnUi.Draw.TextSpan.ParentControl
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ParentControl
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 380
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected SkiaControl ParentControl { get; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Protected ReadOnly Property ParentControl As SkiaControl
  overload: DrawnUi.Draw.TextSpan.ParentControl*
- uid: DrawnUi.Draw.TextSpan.OnPropertyChanged(System.String)
  commentId: M:DrawnUi.Draw.TextSpan.OnPropertyChanged(System.String)
  id: OnPropertyChanged(System.String)
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: OnPropertyChanged(string)
  nameWithType: TextSpan.OnPropertyChanged(string)
  fullName: DrawnUi.Draw.TextSpan.OnPropertyChanged(string)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnPropertyChanged
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 388
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Method that is called when a bound property is changed.
  example: []
  syntax:
    content: protected override void OnPropertyChanged(string propertyName = null)
    parameters:
    - id: propertyName
      type: System.String
      description: The name of the bound property that changed.
    content.vb: Protected Overrides Sub OnPropertyChanged(propertyName As String = Nothing)
  overridden: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  overload: DrawnUi.Draw.TextSpan.OnPropertyChanged*
  nameWithType.vb: TextSpan.OnPropertyChanged(String)
  fullName.vb: DrawnUi.Draw.TextSpan.OnPropertyChanged(String)
  name.vb: OnPropertyChanged(String)
- uid: DrawnUi.Draw.TextSpan.#ctor
  commentId: M:DrawnUi.Draw.TextSpan.#ctor
  id: '#ctor'
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: TextSpan()
  nameWithType: TextSpan.TextSpan()
  fullName: DrawnUi.Draw.TextSpan.TextSpan()
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 425
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public TextSpan()
    content.vb: Public Sub New()
  overload: DrawnUi.Draw.TextSpan.#ctor*
  nameWithType.vb: TextSpan.New()
  fullName.vb: DrawnUi.Draw.TextSpan.New()
  name.vb: New()
- uid: DrawnUi.Draw.TextSpan._fontAutoSet
  commentId: F:DrawnUi.Draw.TextSpan._fontAutoSet
  id: _fontAutoSet
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: _fontAutoSet
  nameWithType: TextSpan._fontAutoSet
  fullName: DrawnUi.Draw.TextSpan._fontAutoSet
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: _fontAutoSet
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 436
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected bool _fontAutoSet
    return:
      type: System.Boolean
    content.vb: Protected _fontAutoSet As Boolean
- uid: DrawnUi.Draw.TextSpan.UpdateFont
  commentId: M:DrawnUi.Draw.TextSpan.UpdateFont
  id: UpdateFont
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: UpdateFont()
  nameWithType: TextSpan.UpdateFont()
  fullName: DrawnUi.Draw.TextSpan.UpdateFont()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: UpdateFont
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 438
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: protected virtual void UpdateFont()
    content.vb: Protected Overridable Sub UpdateFont()
  overload: DrawnUi.Draw.TextSpan.UpdateFont*
- uid: DrawnUi.Draw.TextSpan.FontFamily
  commentId: P:DrawnUi.Draw.TextSpan.FontFamily
  id: FontFamily
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: FontFamily
  nameWithType: TextSpan.FontFamily
  fullName: DrawnUi.Draw.TextSpan.FontFamily
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FontFamily
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 474
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public string FontFamily { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property FontFamily As String
  overload: DrawnUi.Draw.TextSpan.FontFamily*
- uid: DrawnUi.Draw.TextSpan.LineSpacing
  commentId: P:DrawnUi.Draw.TextSpan.LineSpacing
  id: LineSpacing
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: LineSpacing
  nameWithType: TextSpan.LineSpacing
  fullName: DrawnUi.Draw.TextSpan.LineSpacing
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LineSpacing
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 490
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float LineSpacing { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property LineSpacing As Single
  overload: DrawnUi.Draw.TextSpan.LineSpacing*
- uid: DrawnUi.Draw.TextSpan.LineHeight
  commentId: P:DrawnUi.Draw.TextSpan.LineHeight
  id: LineHeight
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: LineHeight
  nameWithType: TextSpan.LineHeight
  fullName: DrawnUi.Draw.TextSpan.LineHeight
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: LineHeight
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 501
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float LineHeight { get; set; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property LineHeight As Single
  overload: DrawnUi.Draw.TextSpan.LineHeight*
- uid: DrawnUi.Draw.TextSpan.FontWeight
  commentId: P:DrawnUi.Draw.TextSpan.FontWeight
  id: FontWeight
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: FontWeight
  nameWithType: TextSpan.FontWeight
  fullName: DrawnUi.Draw.TextSpan.FontWeight
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FontWeight
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 512
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int FontWeight { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property FontWeight As Integer
  overload: DrawnUi.Draw.TextSpan.FontWeight*
- uid: DrawnUi.Draw.TextSpan.TextColorProperty
  commentId: F:DrawnUi.Draw.TextSpan.TextColorProperty
  id: TextColorProperty
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: TextColorProperty
  nameWithType: TextSpan.TextColorProperty
  fullName: DrawnUi.Draw.TextSpan.TextColorProperty
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TextColorProperty
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 523
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static readonly BindableProperty TextColorProperty
    return:
      type: Microsoft.Maui.Controls.BindableProperty
    content.vb: Public Shared ReadOnly TextColorProperty As BindableProperty
- uid: DrawnUi.Draw.TextSpan.TextColor
  commentId: P:DrawnUi.Draw.TextSpan.TextColor
  id: TextColor
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: TextColor
  nameWithType: TextSpan.TextColor
  fullName: DrawnUi.Draw.TextSpan.TextColor
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TextColor
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 535
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Color TextColor { get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Graphics.Color
    content.vb: Public Property TextColor As Color
  overload: DrawnUi.Draw.TextSpan.TextColor*
- uid: DrawnUi.Draw.TextSpan.BackgroundColor
  commentId: P:DrawnUi.Draw.TextSpan.BackgroundColor
  id: BackgroundColor
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: BackgroundColor
  nameWithType: TextSpan.BackgroundColor
  fullName: DrawnUi.Draw.TextSpan.BackgroundColor
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BackgroundColor
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 541
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Color BackgroundColor { get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Graphics.Color
    content.vb: Public Property BackgroundColor As Color
  overload: DrawnUi.Draw.TextSpan.BackgroundColor*
- uid: DrawnUi.Draw.TextSpan.ParagraphColor
  commentId: P:DrawnUi.Draw.TextSpan.ParagraphColor
  id: ParagraphColor
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: ParagraphColor
  nameWithType: TextSpan.ParagraphColor
  fullName: DrawnUi.Draw.TextSpan.ParagraphColor
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ParagraphColor
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 552
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Color ParagraphColor { get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Graphics.Color
    content.vb: Public Property ParagraphColor As Color
  overload: DrawnUi.Draw.TextSpan.ParagraphColor*
- uid: DrawnUi.Draw.TextSpan.FontSizeProperty
  commentId: F:DrawnUi.Draw.TextSpan.FontSizeProperty
  id: FontSizeProperty
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: FontSizeProperty
  nameWithType: TextSpan.FontSizeProperty
  fullName: DrawnUi.Draw.TextSpan.FontSizeProperty
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FontSizeProperty
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 563
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static readonly BindableProperty FontSizeProperty
    return:
      type: Microsoft.Maui.Controls.BindableProperty
    content.vb: Public Shared ReadOnly FontSizeProperty As BindableProperty
- uid: DrawnUi.Draw.TextSpan.FontSize
  commentId: P:DrawnUi.Draw.TextSpan.FontSize
  id: FontSize
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: FontSize
  nameWithType: TextSpan.FontSize
  fullName: DrawnUi.Draw.TextSpan.FontSize
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FontSize
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 578
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public double FontSize { get; set; }
    parameters: []
    return:
      type: System.Double
    content.vb: Public Property FontSize As Double
  overload: DrawnUi.Draw.TextSpan.FontSize*
- uid: DrawnUi.Draw.TextSpan.IsItalic
  commentId: P:DrawnUi.Draw.TextSpan.IsItalic
  id: IsItalic
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: IsItalic
  nameWithType: TextSpan.IsItalic
  fullName: DrawnUi.Draw.TextSpan.IsItalic
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsItalic
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 584
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsItalic { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsItalic As Boolean
  overload: DrawnUi.Draw.TextSpan.IsItalic*
- uid: DrawnUi.Draw.TextSpan.IsBold
  commentId: P:DrawnUi.Draw.TextSpan.IsBold
  id: IsBold
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: IsBold
  nameWithType: TextSpan.IsBold
  fullName: DrawnUi.Draw.TextSpan.IsBold
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsBold
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 595
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsBold { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsBold As Boolean
  overload: DrawnUi.Draw.TextSpan.IsBold*
- uid: DrawnUi.Draw.TextSpan.AutoFindFont
  commentId: P:DrawnUi.Draw.TextSpan.AutoFindFont
  id: AutoFindFont
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: AutoFindFont
  nameWithType: TextSpan.AutoFindFont
  fullName: DrawnUi.Draw.TextSpan.AutoFindFont
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AutoFindFont
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 613
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: If any glyph cannot be rendered with selected font try find system font that supports it and switch to it for the whole span
  example: []
  syntax:
    content: public bool AutoFindFont { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property AutoFindFont As Boolean
  overload: DrawnUi.Draw.TextSpan.AutoFindFont*
- uid: DrawnUi.Draw.TextSpan.FontDetectedWith
  commentId: P:DrawnUi.Draw.TextSpan.FontDetectedWith
  id: FontDetectedWith
  parent: DrawnUi.Draw.TextSpan
  langs:
  - csharp
  - vb
  name: FontDetectedWith
  nameWithType: TextSpan.FontDetectedWith
  fullName: DrawnUi.Draw.TextSpan.FontDetectedWith
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Text/TextSpan.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: FontDetectedWith
    path: ../src/Maui/DrawnUi/Draw/Text/TextSpan.cs
    startLine: 624
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int FontDetectedWith { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property FontDetectedWith As Integer
  overload: DrawnUi.Draw.TextSpan.FontDetectedWith*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: Microsoft.Maui.Controls.BindableObject
  commentId: T:Microsoft.Maui.Controls.BindableObject
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject
  name: BindableObject
  nameWithType: BindableObject
  fullName: Microsoft.Maui.Controls.BindableObject
- uid: Microsoft.Maui.Controls.Element
  commentId: T:Microsoft.Maui.Controls.Element
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  name: Element
  nameWithType: Element
  fullName: Microsoft.Maui.Controls.Element
- uid: System.ComponentModel.INotifyPropertyChanged
  commentId: T:System.ComponentModel.INotifyPropertyChanged
  parent: System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.componentmodel.inotifypropertychanged
  name: INotifyPropertyChanged
  nameWithType: INotifyPropertyChanged
  fullName: System.ComponentModel.INotifyPropertyChanged
- uid: Microsoft.Maui.Controls.IElementController
  commentId: T:Microsoft.Maui.Controls.IElementController
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ielementcontroller
  name: IElementController
  nameWithType: IElementController
  fullName: Microsoft.Maui.Controls.IElementController
- uid: Microsoft.Maui.IVisualTreeElement
  commentId: T:Microsoft.Maui.IVisualTreeElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ivisualtreeelement
  name: IVisualTreeElement
  nameWithType: IVisualTreeElement
  fullName: Microsoft.Maui.IVisualTreeElement
- uid: Microsoft.Maui.Controls.IEffectControlProvider
  commentId: T:Microsoft.Maui.Controls.IEffectControlProvider
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.ieffectcontrolprovider
  name: IEffectControlProvider
  nameWithType: IEffectControlProvider
  fullName: Microsoft.Maui.Controls.IEffectControlProvider
- uid: Microsoft.Maui.IToolTipElement
  commentId: T:Microsoft.Maui.IToolTipElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.itooltipelement
  name: IToolTipElement
  nameWithType: IToolTipElement
  fullName: Microsoft.Maui.IToolTipElement
- uid: Microsoft.Maui.IContextFlyoutElement
  commentId: T:Microsoft.Maui.IContextFlyoutElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.icontextflyoutelement
  name: IContextFlyoutElement
  nameWithType: IContextFlyoutElement
  fullName: Microsoft.Maui.IContextFlyoutElement
- uid: Microsoft.Maui.IElement
  commentId: T:Microsoft.Maui.IElement
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielement
  name: IElement
  nameWithType: IElement
  fullName: Microsoft.Maui.IElement
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: Microsoft.Maui.Controls.Element.AutomationIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.AutomationIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationidproperty
  name: AutomationIdProperty
  nameWithType: Element.AutomationIdProperty
  fullName: Microsoft.Maui.Controls.Element.AutomationIdProperty
- uid: Microsoft.Maui.Controls.Element.ClassIdProperty
  commentId: F:Microsoft.Maui.Controls.Element.ClassIdProperty
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classidproperty
  name: ClassIdProperty
  nameWithType: Element.ClassIdProperty
  fullName: Microsoft.Maui.Controls.Element.ClassIdProperty
- uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  name: InsertLogicalChild(int, Element)
  nameWithType: Element.InsertLogicalChild(int, Element)
  fullName: Microsoft.Maui.Controls.Element.InsertLogicalChild(int, Microsoft.Maui.Controls.Element)
  nameWithType.vb: Element.InsertLogicalChild(Integer, Element)
  fullName.vb: Microsoft.Maui.Controls.Element.InsertLogicalChild(Integer, Microsoft.Maui.Controls.Element)
  name.vb: InsertLogicalChild(Integer, Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.InsertLogicalChild(System.Int32,Microsoft.Maui.Controls.Element)
    name: InsertLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.insertlogicalchild
  - name: (
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  name: AddLogicalChild(Element)
  nameWithType: Element.AddLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.AddLogicalChild(Microsoft.Maui.Controls.Element)
    name: AddLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.addlogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  name: RemoveLogicalChild(Element)
  nameWithType: Element.RemoveLogicalChild(Element)
  fullName: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveLogicalChild(Microsoft.Maui.Controls.Element)
    name: RemoveLogicalChild
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removelogicalchild
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
  commentId: M:Microsoft.Maui.Controls.Element.ClearLogicalChildren
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  name: ClearLogicalChildren()
  nameWithType: Element.ClearLogicalChildren()
  fullName: Microsoft.Maui.Controls.Element.ClearLogicalChildren()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.ClearLogicalChildren
    name: ClearLogicalChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.clearlogicalchildren
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
  commentId: M:Microsoft.Maui.Controls.Element.FindByName(System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  name: FindByName(string)
  nameWithType: Element.FindByName(string)
  fullName: Microsoft.Maui.Controls.Element.FindByName(string)
  nameWithType.vb: Element.FindByName(String)
  fullName.vb: Microsoft.Maui.Controls.Element.FindByName(String)
  name.vb: FindByName(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.FindByName(System.String)
    name: FindByName
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.findbyname
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  name: RemoveDynamicResource(BindableProperty)
  nameWithType: Element.RemoveDynamicResource(BindableProperty)
  fullName: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.RemoveDynamicResource(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.removedynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  commentId: M:Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  name: SetDynamicResource(BindableProperty, string)
  nameWithType: Element.SetDynamicResource(BindableProperty, string)
  fullName: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, string)
  nameWithType.vb: Element.SetDynamicResource(BindableProperty, String)
  fullName.vb: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty, String)
  name.vb: SetDynamicResource(BindableProperty, String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.SetDynamicResource(Microsoft.Maui.Controls.BindableProperty,System.String)
    name: SetDynamicResource
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.setdynamicresource
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnBindingContextChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnBindingContextChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onbindingcontextchanged
  name: OnBindingContextChanged()
  nameWithType: Element.OnBindingContextChanged()
  fullName: Microsoft.Maui.Controls.Element.OnBindingContextChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onbindingcontextchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnBindingContextChanged
    name: OnBindingContextChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onbindingcontextchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildadded
  name: OnChildAdded(Element)
  nameWithType: Element.OnChildAdded(Element)
  fullName: Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnChildAdded(Microsoft.Maui.Controls.Element)
    name: OnChildAdded
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildadded
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  commentId: M:Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildremoved
  name: OnChildRemoved(Element, int)
  nameWithType: Element.OnChildRemoved(Element, int)
  fullName: Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element, int)
  nameWithType.vb: Element.OnChildRemoved(Element, Integer)
  fullName.vb: Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element, Integer)
  name.vb: OnChildRemoved(Element, Integer)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnChildRemoved(Microsoft.Maui.Controls.Element,System.Int32)
    name: OnChildRemoved
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onchildremoved
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentSet
  commentId: M:Microsoft.Maui.Controls.Element.OnParentSet
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentset
  name: OnParentSet()
  nameWithType: Element.OnParentSet()
  fullName: Microsoft.Maui.Controls.Element.OnParentSet()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentSet
    name: OnParentSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentset
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentSet
    name: OnParentSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentset
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  name: OnParentChanging(ParentChangingEventArgs)
  nameWithType: Element.OnParentChanging(ParentChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanging(Microsoft.Maui.Controls.ParentChangingEventArgs)
    name: OnParentChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanging
  - name: (
  - uid: Microsoft.Maui.Controls.ParentChangingEventArgs
    name: ParentChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.parentchangingeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnParentChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  name: OnParentChanged()
  nameWithType: Element.OnParentChanged()
  fullName: Microsoft.Maui.Controls.Element.OnParentChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnParentChanged
    name: OnParentChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onparentchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  commentId: M:Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  name: OnHandlerChanging(HandlerChangingEventArgs)
  nameWithType: Element.OnHandlerChanging(HandlerChangingEventArgs)
  fullName: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanging(Microsoft.Maui.Controls.HandlerChangingEventArgs)
    name: OnHandlerChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanging
  - name: (
  - uid: Microsoft.Maui.Controls.HandlerChangingEventArgs
    name: HandlerChangingEventArgs
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.handlerchangingeventargs
  - name: )
- uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
  commentId: M:Microsoft.Maui.Controls.Element.OnHandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  name: OnHandlerChanged()
  nameWithType: Element.OnHandlerChanged()
  fullName: Microsoft.Maui.Controls.Element.OnHandlerChanged()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
    name: OnHandlerChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnHandlerChanged
    name: OnHandlerChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onhandlerchanged
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  name: MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesIsInAccessibleTree(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesIsInAccessibleTree(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesIsInAccessibleTree
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesisinaccessibletree
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  commentId: M:Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  name: MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  nameWithType: Element.MapAutomationPropertiesExcludedWithChildren(IElementHandler, Element)
  fullName: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler, Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.MapAutomationPropertiesExcludedWithChildren(Microsoft.Maui.IElementHandler,Microsoft.Maui.Controls.Element)
    name: MapAutomationPropertiesExcludedWithChildren
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.mapautomationpropertiesexcludedwithchildren
  - name: (
  - uid: Microsoft.Maui.IElementHandler
    name: IElementHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.ielementhandler
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.AutomationId
  commentId: P:Microsoft.Maui.Controls.Element.AutomationId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.automationid
  name: AutomationId
  nameWithType: Element.AutomationId
  fullName: Microsoft.Maui.Controls.Element.AutomationId
- uid: Microsoft.Maui.Controls.Element.ClassId
  commentId: P:Microsoft.Maui.Controls.Element.ClassId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.classid
  name: ClassId
  nameWithType: Element.ClassId
  fullName: Microsoft.Maui.Controls.Element.ClassId
- uid: Microsoft.Maui.Controls.Element.Effects
  commentId: P:Microsoft.Maui.Controls.Element.Effects
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.effects
  name: Effects
  nameWithType: Element.Effects
  fullName: Microsoft.Maui.Controls.Element.Effects
- uid: Microsoft.Maui.Controls.Element.Id
  commentId: P:Microsoft.Maui.Controls.Element.Id
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.id
  name: Id
  nameWithType: Element.Id
  fullName: Microsoft.Maui.Controls.Element.Id
- uid: Microsoft.Maui.Controls.Element.StyleId
  commentId: P:Microsoft.Maui.Controls.Element.StyleId
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.styleid
  name: StyleId
  nameWithType: Element.StyleId
  fullName: Microsoft.Maui.Controls.Element.StyleId
- uid: Microsoft.Maui.Controls.Element.Parent
  commentId: P:Microsoft.Maui.Controls.Element.Parent
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parent
  name: Parent
  nameWithType: Element.Parent
  fullName: Microsoft.Maui.Controls.Element.Parent
- uid: Microsoft.Maui.Controls.Element.Handler
  commentId: P:Microsoft.Maui.Controls.Element.Handler
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handler
  name: Handler
  nameWithType: Element.Handler
  fullName: Microsoft.Maui.Controls.Element.Handler
- uid: Microsoft.Maui.Controls.Element.ChildAdded
  commentId: E:Microsoft.Maui.Controls.Element.ChildAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childadded
  name: ChildAdded
  nameWithType: Element.ChildAdded
  fullName: Microsoft.Maui.Controls.Element.ChildAdded
- uid: Microsoft.Maui.Controls.Element.ChildRemoved
  commentId: E:Microsoft.Maui.Controls.Element.ChildRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.childremoved
  name: ChildRemoved
  nameWithType: Element.ChildRemoved
  fullName: Microsoft.Maui.Controls.Element.ChildRemoved
- uid: Microsoft.Maui.Controls.Element.DescendantAdded
  commentId: E:Microsoft.Maui.Controls.Element.DescendantAdded
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantadded
  name: DescendantAdded
  nameWithType: Element.DescendantAdded
  fullName: Microsoft.Maui.Controls.Element.DescendantAdded
- uid: Microsoft.Maui.Controls.Element.DescendantRemoved
  commentId: E:Microsoft.Maui.Controls.Element.DescendantRemoved
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.descendantremoved
  name: DescendantRemoved
  nameWithType: Element.DescendantRemoved
  fullName: Microsoft.Maui.Controls.Element.DescendantRemoved
- uid: Microsoft.Maui.Controls.Element.ParentChanging
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanging
  name: ParentChanging
  nameWithType: Element.ParentChanging
  fullName: Microsoft.Maui.Controls.Element.ParentChanging
- uid: Microsoft.Maui.Controls.Element.ParentChanged
  commentId: E:Microsoft.Maui.Controls.Element.ParentChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.parentchanged
  name: ParentChanged
  nameWithType: Element.ParentChanged
  fullName: Microsoft.Maui.Controls.Element.ParentChanged
- uid: Microsoft.Maui.Controls.Element.HandlerChanging
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanging
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanging
  name: HandlerChanging
  nameWithType: Element.HandlerChanging
  fullName: Microsoft.Maui.Controls.Element.HandlerChanging
- uid: Microsoft.Maui.Controls.Element.HandlerChanged
  commentId: E:Microsoft.Maui.Controls.Element.HandlerChanged
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.handlerchanged
  name: HandlerChanged
  nameWithType: Element.HandlerChanged
  fullName: Microsoft.Maui.Controls.Element.HandlerChanged
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  commentId: F:Microsoft.Maui.Controls.BindableObject.BindingContextProperty
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextproperty
  name: BindingContextProperty
  nameWithType: BindableObject.BindingContextProperty
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextProperty
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  name: ClearValue(BindableProperty)
  nameWithType: BindableObject.ClearValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindableProperty)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  name: ClearValue(BindablePropertyKey)
  nameWithType: BindableObject.ClearValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ClearValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: ClearValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.clearvalue#microsoft-maui-controls-bindableobject-clearvalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  name: GetValue(BindableProperty)
  nameWithType: BindableObject.GetValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.GetValue(Microsoft.Maui.Controls.BindableProperty)
    name: GetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.getvalue
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  name: IsSet(BindableProperty)
  nameWithType: BindableObject.IsSet(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.IsSet(Microsoft.Maui.Controls.BindableProperty)
    name: IsSet
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.isset
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  name: RemoveBinding(BindableProperty)
  nameWithType: BindableObject.RemoveBinding(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.RemoveBinding(Microsoft.Maui.Controls.BindableProperty)
    name: RemoveBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.removebinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  name: SetBinding(BindableProperty, BindingBase)
  nameWithType: BindableObject.SetBinding(BindableProperty, BindingBase)
  fullName: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty, Microsoft.Maui.Controls.BindingBase)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetBinding(Microsoft.Maui.Controls.BindableProperty,Microsoft.Maui.Controls.BindingBase)
    name: SetBinding
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setbinding
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindingBase
    name: BindingBase
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindingbase
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.ApplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  name: ApplyBindings()
  nameWithType: BindableObject.ApplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.ApplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.ApplyBindings
    name: ApplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.applybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  commentId: M:Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  name: OnPropertyChanging(string)
  nameWithType: BindableObject.OnPropertyChanging(string)
  fullName: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(string)
  nameWithType.vb: BindableObject.OnPropertyChanging(String)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(String)
  name.vb: OnPropertyChanging(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.OnPropertyChanging(System.String)
    name: OnPropertyChanging
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.onpropertychanging
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  commentId: M:Microsoft.Maui.Controls.BindableObject.UnapplyBindings
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  name: UnapplyBindings()
  nameWithType: BindableObject.UnapplyBindings()
  fullName: Microsoft.Maui.Controls.BindableObject.UnapplyBindings()
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.UnapplyBindings
    name: UnapplyBindings
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.unapplybindings
  - name: (
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  name: SetValue(BindableProperty, object)
  nameWithType: BindableObject.SetValue(BindableProperty, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, object)
  nameWithType.vb: BindableObject.SetValue(BindableProperty, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty, Object)
  name.vb: SetValue(BindableProperty, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindableProperty,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindableproperty-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  commentId: M:Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  name: SetValue(BindablePropertyKey, object)
  nameWithType: BindableObject.SetValue(BindablePropertyKey, object)
  fullName: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, object)
  nameWithType.vb: BindableObject.SetValue(BindablePropertyKey, Object)
  fullName.vb: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey, Object)
  name.vb: SetValue(BindablePropertyKey, Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.SetValue(Microsoft.Maui.Controls.BindablePropertyKey,System.Object)
    name: SetValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.setvalue#microsoft-maui-controls-bindableobject-setvalue(microsoft-maui-controls-bindablepropertykey-system-object)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  name: CoerceValue(BindableProperty)
  nameWithType: BindableObject.CoerceValue(BindableProperty)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindableProperty)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindableproperty)
  - name: (
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  commentId: M:Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  name: CoerceValue(BindablePropertyKey)
  nameWithType: BindableObject.CoerceValue(BindablePropertyKey)
  fullName: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.BindableObject.CoerceValue(Microsoft.Maui.Controls.BindablePropertyKey)
    name: CoerceValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.coercevalue#microsoft-maui-controls-bindableobject-coercevalue(microsoft-maui-controls-bindablepropertykey)
  - name: (
  - uid: Microsoft.Maui.Controls.BindablePropertyKey
    name: BindablePropertyKey
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindablepropertykey
  - name: )
- uid: Microsoft.Maui.Controls.BindableObject.Dispatcher
  commentId: P:Microsoft.Maui.Controls.BindableObject.Dispatcher
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.dispatcher
  name: Dispatcher
  nameWithType: BindableObject.Dispatcher
  fullName: Microsoft.Maui.Controls.BindableObject.Dispatcher
- uid: Microsoft.Maui.Controls.BindableObject.BindingContext
  commentId: P:Microsoft.Maui.Controls.BindableObject.BindingContext
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontext
  name: BindingContext
  nameWithType: BindableObject.BindingContext
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContext
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanged
  name: PropertyChanged
  nameWithType: BindableObject.PropertyChanged
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanged
- uid: Microsoft.Maui.Controls.BindableObject.PropertyChanging
  commentId: E:Microsoft.Maui.Controls.BindableObject.PropertyChanging
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.propertychanging
  name: PropertyChanging
  nameWithType: BindableObject.PropertyChanging
  fullName: Microsoft.Maui.Controls.BindableObject.PropertyChanging
- uid: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  commentId: E:Microsoft.Maui.Controls.BindableObject.BindingContextChanged
  parent: Microsoft.Maui.Controls.BindableObject
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableobject.bindingcontextchanged
  name: BindingContextChanged
  nameWithType: BindableObject.BindingContextChanged
  fullName: Microsoft.Maui.Controls.BindableObject.BindingContextChanged
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: DrawnUi.Draw.TextSpan.DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  commentId: M:DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(``0,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  parent: DrawnUi.Draw.ThemeBindings
  definition: DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(``0,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  href: DrawnUi.Draw.ThemeBindings.html#DrawnUi_Draw_ThemeBindings_WithThemeBinding__1___0_Microsoft_Maui_Controls_BindableProperty_System_Object_System_Object_System_Object_
  name: WithThemeBinding<TextSpan>(TextSpan, BindableProperty, object, object, object)
  nameWithType: ThemeBindings.WithThemeBinding<TextSpan>(TextSpan, BindableProperty, object, object, object)
  fullName: DrawnUi.Draw.ThemeBindings.WithThemeBinding<DrawnUi.Draw.TextSpan>(DrawnUi.Draw.TextSpan, Microsoft.Maui.Controls.BindableProperty, object, object, object)
  nameWithType.vb: ThemeBindings.WithThemeBinding(Of TextSpan)(TextSpan, BindableProperty, Object, Object, Object)
  fullName.vb: DrawnUi.Draw.ThemeBindings.WithThemeBinding(Of DrawnUi.Draw.TextSpan)(DrawnUi.Draw.TextSpan, Microsoft.Maui.Controls.BindableProperty, Object, Object, Object)
  name.vb: WithThemeBinding(Of TextSpan)(TextSpan, BindableProperty, Object, Object, Object)
  spec.csharp:
  - uid: DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(DrawnUi.Draw.TextSpan,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
    name: WithThemeBinding
    href: DrawnUi.Draw.ThemeBindings.html#DrawnUi_Draw_ThemeBindings_WithThemeBinding__1___0_Microsoft_Maui_Controls_BindableProperty_System_Object_System_Object_System_Object_
  - name: <
  - uid: DrawnUi.Draw.TextSpan
    name: TextSpan
    href: DrawnUi.Draw.TextSpan.html
  - name: '>'
  - name: (
  - uid: DrawnUi.Draw.TextSpan
    name: TextSpan
    href: DrawnUi.Draw.TextSpan.html
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(DrawnUi.Draw.TextSpan,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
    name: WithThemeBinding
    href: DrawnUi.Draw.ThemeBindings.html#DrawnUi_Draw_ThemeBindings_WithThemeBinding__1___0_Microsoft_Maui_Controls_BindableProperty_System_Object_System_Object_System_Object_
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.TextSpan
    name: TextSpan
    href: DrawnUi.Draw.TextSpan.html
  - name: )
  - name: (
  - uid: DrawnUi.Draw.TextSpan
    name: TextSpan
    href: DrawnUi.Draw.TextSpan.html
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Draw.StaticResourcesExtensions.FindParent``1
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Draw.StaticResourcesExtensions
  definition: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.FindMauiContext(System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: Microsoft.Maui.Controls.Element.DrawnUi.Extensions.InternalExtensions.GetParentsPath
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Microsoft.Maui.Controls
  commentId: N:Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls
  nameWithType: Microsoft.Maui.Controls
  fullName: Microsoft.Maui.Controls
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
- uid: System.ComponentModel
  commentId: N:System.ComponentModel
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.ComponentModel
  nameWithType: System.ComponentModel
  fullName: System.ComponentModel
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.ComponentModel
    name: ComponentModel
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.componentmodel
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(``0,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  commentId: M:DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(``0,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
  isExternal: true
  href: DrawnUi.Draw.ThemeBindings.html#DrawnUi_Draw_ThemeBindings_WithThemeBinding__1___0_Microsoft_Maui_Controls_BindableProperty_System_Object_System_Object_System_Object_
  name: WithThemeBinding<TControl>(TControl, BindableProperty, object, object, object)
  nameWithType: ThemeBindings.WithThemeBinding<TControl>(TControl, BindableProperty, object, object, object)
  fullName: DrawnUi.Draw.ThemeBindings.WithThemeBinding<TControl>(TControl, Microsoft.Maui.Controls.BindableProperty, object, object, object)
  nameWithType.vb: ThemeBindings.WithThemeBinding(Of TControl)(TControl, BindableProperty, Object, Object, Object)
  fullName.vb: DrawnUi.Draw.ThemeBindings.WithThemeBinding(Of TControl)(TControl, Microsoft.Maui.Controls.BindableProperty, Object, Object, Object)
  name.vb: WithThemeBinding(Of TControl)(TControl, BindableProperty, Object, Object, Object)
  spec.csharp:
  - uid: DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(``0,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
    name: WithThemeBinding
    href: DrawnUi.Draw.ThemeBindings.html#DrawnUi_Draw_ThemeBindings_WithThemeBinding__1___0_Microsoft_Maui_Controls_BindableProperty_System_Object_System_Object_System_Object_
  - name: <
  - name: TControl
  - name: '>'
  - name: (
  - name: TControl
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ThemeBindings.WithThemeBinding``1(``0,Microsoft.Maui.Controls.BindableProperty,System.Object,System.Object,System.Object)
    name: WithThemeBinding
    href: DrawnUi.Draw.ThemeBindings.html#DrawnUi_Draw_ThemeBindings_WithThemeBinding__1___0_Microsoft_Maui_Controls_BindableProperty_System_Object_System_Object_System_Object_
  - name: (
  - name: Of
  - name: " "
  - name: TControl
  - name: )
  - name: (
  - name: TControl
  - name: ','
  - name: " "
  - uid: Microsoft.Maui.Controls.BindableProperty
    name: BindableProperty
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Draw.ThemeBindings
  commentId: T:DrawnUi.Draw.ThemeBindings
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ThemeBindings.html
  name: ThemeBindings
  nameWithType: ThemeBindings
  fullName: DrawnUi.Draw.ThemeBindings
- uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  name: FindParent<T>(Element)
  nameWithType: StaticResourcesExtensions.FindParent<T>(Element)
  fullName: DrawnUi.Draw.StaticResourcesExtensions.FindParent<T>(Microsoft.Maui.Controls.Element)
  nameWithType.vb: StaticResourcesExtensions.FindParent(Of T)(Element)
  fullName.vb: DrawnUi.Draw.StaticResourcesExtensions.FindParent(Of T)(Microsoft.Maui.Controls.Element)
  name.vb: FindParent(Of T)(Element)
  spec.csharp:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: <
  - name: T
  - name: '>'
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.StaticResourcesExtensions.FindParent``1(Microsoft.Maui.Controls.Element)
    name: FindParent
    href: DrawnUi.Draw.StaticResourcesExtensions.html#DrawnUi_Draw_StaticResourcesExtensions_FindParent__1_Microsoft_Maui_Controls_Element_
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Draw.StaticResourcesExtensions
  commentId: T:DrawnUi.Draw.StaticResourcesExtensions
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.StaticResourcesExtensions.html
  name: StaticResourcesExtensions
  nameWithType: StaticResourcesExtensions
  fullName: DrawnUi.Draw.StaticResourcesExtensions
- uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  name: FindMauiContext(Element, bool)
  nameWithType: InternalExtensions.FindMauiContext(Element, bool)
  fullName: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, bool)
  nameWithType.vb: InternalExtensions.FindMauiContext(Element, Boolean)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element, Boolean)
  name.vb: FindMauiContext(Element, Boolean)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FindMauiContext(Microsoft.Maui.Controls.Element,System.Boolean)
    name: FindMauiContext
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FindMauiContext_Microsoft_Maui_Controls_Element_System_Boolean_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: ','
  - name: " "
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  commentId: M:DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  name: GetParentsPath(Element)
  nameWithType: InternalExtensions.GetParentsPath(Element)
  fullName: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.GetParentsPath(Microsoft.Maui.Controls.Element)
    name: GetParentsPath
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_GetParentsPath_Microsoft_Maui_Controls_Element_
  - name: (
  - uid: Microsoft.Maui.Controls.Element
    name: Element
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: Microsoft.Maui.Controls.BindableProperty
  commentId: T:Microsoft.Maui.Controls.BindableProperty
  parent: Microsoft.Maui.Controls
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.bindableproperty
  name: BindableProperty
  nameWithType: BindableProperty
  fullName: Microsoft.Maui.Controls.BindableProperty
- uid: DrawnUi.Draw.TextSpan.Text*
  commentId: Overload:DrawnUi.Draw.TextSpan.Text
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Text
  name: Text
  nameWithType: TextSpan.Text
  fullName: DrawnUi.Draw.TextSpan.Text
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: DrawnUi.Draw.TextSpan.Default*
  commentId: Overload:DrawnUi.Draw.TextSpan.Default
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Default
  name: Default
  nameWithType: TextSpan.Default
  fullName: DrawnUi.Draw.TextSpan.Default
- uid: DrawnUi.Draw.TextSpan
  commentId: T:DrawnUi.Draw.TextSpan
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.TextSpan.html
  name: TextSpan
  nameWithType: TextSpan
  fullName: DrawnUi.Draw.TextSpan
- uid: DrawnUi.Draw.TextSpan.DebugString*
  commentId: Overload:DrawnUi.Draw.TextSpan.DebugString
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_DebugString
  name: DebugString
  nameWithType: TextSpan.DebugString
  fullName: DrawnUi.Draw.TextSpan.DebugString
- uid: DrawnUi.Draw.TextSpan.RenderingScale*
  commentId: Overload:DrawnUi.Draw.TextSpan.RenderingScale
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_RenderingScale
  name: RenderingScale
  nameWithType: TextSpan.RenderingScale
  fullName: DrawnUi.Draw.TextSpan.RenderingScale
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.TextSpan.Glyphs*
  commentId: Overload:DrawnUi.Draw.TextSpan.Glyphs
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Glyphs
  name: Glyphs
  nameWithType: TextSpan.Glyphs
  fullName: DrawnUi.Draw.TextSpan.Glyphs
- uid: System.Collections.Generic.List{DrawnUi.Draw.UsedGlyph}
  commentId: T:System.Collections.Generic.List{DrawnUi.Draw.UsedGlyph}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<UsedGlyph>
  nameWithType: List<UsedGlyph>
  fullName: System.Collections.Generic.List<DrawnUi.Draw.UsedGlyph>
  nameWithType.vb: List(Of UsedGlyph)
  fullName.vb: System.Collections.Generic.List(Of DrawnUi.Draw.UsedGlyph)
  name.vb: List(Of UsedGlyph)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Draw.UsedGlyph
    name: UsedGlyph
    href: DrawnUi.Draw.UsedGlyph.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.UsedGlyph
    name: UsedGlyph
    href: DrawnUi.Draw.UsedGlyph.html
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Draw.TextSpan.Shape*
  commentId: Overload:DrawnUi.Draw.TextSpan.Shape
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Shape
  name: Shape
  nameWithType: TextSpan.Shape
  fullName: DrawnUi.Draw.TextSpan.Shape
- uid: DrawnUi.Draw.TextSpan.TextFiltered*
  commentId: Overload:DrawnUi.Draw.TextSpan.TextFiltered
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextFiltered
  name: TextFiltered
  nameWithType: TextSpan.TextFiltered
  fullName: DrawnUi.Draw.TextSpan.TextFiltered
- uid: DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered*
  commentId: Overload:DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_CheckGlyphsCanBeRendered
  name: CheckGlyphsCanBeRendered
  nameWithType: TextSpan.CheckGlyphsCanBeRendered
  fullName: DrawnUi.Draw.TextSpan.CheckGlyphsCanBeRendered
- uid: DrawnUi.Draw.TextSpan.SetupPaint*
  commentId: Overload:DrawnUi.Draw.TextSpan.SetupPaint
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_SetupPaint_System_Double_SkiaSharp_SKPaint_
  name: SetupPaint
  nameWithType: TextSpan.SetupPaint
  fullName: DrawnUi.Draw.TextSpan.SetupPaint
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: SkiaSharp.SKPaint
  commentId: T:SkiaSharp.SKPaint
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skpaint
  name: SKPaint
  nameWithType: SKPaint
  fullName: SkiaSharp.SKPaint
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Draw.TextSpan.HasSetFont*
  commentId: Overload:DrawnUi.Draw.TextSpan.HasSetFont
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetFont
  name: HasSetFont
  nameWithType: TextSpan.HasSetFont
  fullName: DrawnUi.Draw.TextSpan.HasSetFont
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.TextSpan.HasSetSize*
  commentId: Overload:DrawnUi.Draw.TextSpan.HasSetSize
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetSize
  name: HasSetSize
  nameWithType: TextSpan.HasSetSize
  fullName: DrawnUi.Draw.TextSpan.HasSetSize
- uid: DrawnUi.Draw.TextSpan.HasSetColor*
  commentId: Overload:DrawnUi.Draw.TextSpan.HasSetColor
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasSetColor
  name: HasSetColor
  nameWithType: TextSpan.HasSetColor
  fullName: DrawnUi.Draw.TextSpan.HasSetColor
- uid: DrawnUi.Draw.TextSpan.Paint*
  commentId: Overload:DrawnUi.Draw.TextSpan.Paint
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Paint
  name: Paint
  nameWithType: TextSpan.Paint
  fullName: DrawnUi.Draw.TextSpan.Paint
- uid: DrawnUi.Draw.TextSpan.TypeFace*
  commentId: Overload:DrawnUi.Draw.TextSpan.TypeFace
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TypeFace
  name: TypeFace
  nameWithType: TextSpan.TypeFace
  fullName: DrawnUi.Draw.TextSpan.TypeFace
- uid: SkiaSharp.SKTypeface
  commentId: T:SkiaSharp.SKTypeface
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.sktypeface
  name: SKTypeface
  nameWithType: SKTypeface
  fullName: SkiaSharp.SKTypeface
- uid: DrawnUi.Draw.TextSpan.NeedShape*
  commentId: Overload:DrawnUi.Draw.TextSpan.NeedShape
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_NeedShape
  name: NeedShape
  nameWithType: TextSpan.NeedShape
  fullName: DrawnUi.Draw.TextSpan.NeedShape
- uid: DrawnUi.Draw.TextSpan.HasDecorations*
  commentId: Overload:DrawnUi.Draw.TextSpan.HasDecorations
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasDecorations
  name: HasDecorations
  nameWithType: TextSpan.HasDecorations
  fullName: DrawnUi.Draw.TextSpan.HasDecorations
- uid: DrawnUi.Draw.TextSpan.Underline*
  commentId: Overload:DrawnUi.Draw.TextSpan.Underline
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Underline
  name: Underline
  nameWithType: TextSpan.Underline
  fullName: DrawnUi.Draw.TextSpan.Underline
- uid: DrawnUi.Draw.TextSpan.UnderlineWidth*
  commentId: Overload:DrawnUi.Draw.TextSpan.UnderlineWidth
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_UnderlineWidth
  name: UnderlineWidth
  nameWithType: TextSpan.UnderlineWidth
  fullName: DrawnUi.Draw.TextSpan.UnderlineWidth
- uid: DrawnUi.Draw.TextSpan.Strikeout*
  commentId: Overload:DrawnUi.Draw.TextSpan.Strikeout
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Strikeout
  name: Strikeout
  nameWithType: TextSpan.Strikeout
  fullName: DrawnUi.Draw.TextSpan.Strikeout
- uid: DrawnUi.Draw.TextSpan.StrikeoutWidth*
  commentId: Overload:DrawnUi.Draw.TextSpan.StrikeoutWidth
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_StrikeoutWidth
  name: StrikeoutWidth
  nameWithType: TextSpan.StrikeoutWidth
  fullName: DrawnUi.Draw.TextSpan.StrikeoutWidth
- uid: DrawnUi.Draw.TextSpan.StrikeoutColor*
  commentId: Overload:DrawnUi.Draw.TextSpan.StrikeoutColor
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_StrikeoutColor
  name: StrikeoutColor
  nameWithType: TextSpan.StrikeoutColor
  fullName: DrawnUi.Draw.TextSpan.StrikeoutColor
- uid: Microsoft.Maui.Graphics.Color
  commentId: T:Microsoft.Maui.Graphics.Color
  parent: Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  name: Color
  nameWithType: Color
  fullName: Microsoft.Maui.Graphics.Color
- uid: Microsoft.Maui.Graphics
  commentId: N:Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Graphics
  nameWithType: Microsoft.Maui.Graphics
  fullName: Microsoft.Maui.Graphics
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
- uid: DrawnUi.Draw.TextSpan.HasTapHandler*
  commentId: Overload:DrawnUi.Draw.TextSpan.HasTapHandler
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HasTapHandler
  name: HasTapHandler
  nameWithType: TextSpan.HasTapHandler
  fullName: DrawnUi.Draw.TextSpan.HasTapHandler
- uid: DrawnUi.Draw.TextSpan.ForceCaptureInput*
  commentId: Overload:DrawnUi.Draw.TextSpan.ForceCaptureInput
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ForceCaptureInput
  name: ForceCaptureInput
  nameWithType: TextSpan.ForceCaptureInput
  fullName: DrawnUi.Draw.TextSpan.ForceCaptureInput
- uid: DrawnUi.Draw.TextSpan.HitIsInside*
  commentId: Overload:DrawnUi.Draw.TextSpan.HitIsInside
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_HitIsInside_System_Single_System_Single_
  name: HitIsInside
  nameWithType: TextSpan.HitIsInside
  fullName: DrawnUi.Draw.TextSpan.HitIsInside
- uid: DrawnUi.Draw.TextSpan.FireTap*
  commentId: Overload:DrawnUi.Draw.TextSpan.FireTap
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FireTap
  name: FireTap
  nameWithType: TextSpan.FireTap
  fullName: DrawnUi.Draw.TextSpan.FireTap
- uid: DrawnUi.Draw.TextSpan.Dispose*
  commentId: Overload:DrawnUi.Draw.TextSpan.Dispose
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Dispose
  name: Dispose
  nameWithType: TextSpan.Dispose
  fullName: DrawnUi.Draw.TextSpan.Dispose
- uid: System.IDisposable.Dispose
  commentId: M:System.IDisposable.Dispose
  parent: System.IDisposable
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  name: Dispose()
  nameWithType: IDisposable.Dispose()
  fullName: System.IDisposable.Dispose()
  spec.csharp:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
  spec.vb:
  - uid: System.IDisposable.Dispose
    name: Dispose
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.idisposable.dispose
  - name: (
  - name: )
- uid: DrawnUi.Draw.TextSpan.DrawingOffset*
  commentId: Overload:DrawnUi.Draw.TextSpan.DrawingOffset
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_DrawingOffset
  name: DrawingOffset
  nameWithType: TextSpan.DrawingOffset
  fullName: DrawnUi.Draw.TextSpan.DrawingOffset
- uid: SkiaSharp.SKPoint
  commentId: T:SkiaSharp.SKPoint
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.skpoint
  name: SKPoint
  nameWithType: SKPoint
  fullName: SkiaSharp.SKPoint
- uid: DrawnUi.Draw.TextSpan.Tag*
  commentId: Overload:DrawnUi.Draw.TextSpan.Tag
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_Tag
  name: Tag
  nameWithType: TextSpan.Tag
  fullName: DrawnUi.Draw.TextSpan.Tag
- uid: System.Collections.Generic.List{SkiaSharp.SKRect}
  commentId: T:System.Collections.Generic.List{SkiaSharp.SKRect}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<SKRect>
  nameWithType: List<SKRect>
  fullName: System.Collections.Generic.List<SkiaSharp.SKRect>
  nameWithType.vb: List(Of SKRect)
  fullName.vb: System.Collections.Generic.List(Of SkiaSharp.SKRect)
  name.vb: List(Of SKRect)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: SkiaSharp.SKRect
    name: SKRect
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/skiasharp.skrect
  - name: )
- uid: DrawnUi.Draw.TextSpan.CommandTapped*
  commentId: Overload:DrawnUi.Draw.TextSpan.CommandTapped
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_CommandTapped
  name: CommandTapped
  nameWithType: TextSpan.CommandTapped
  fullName: DrawnUi.Draw.TextSpan.CommandTapped
- uid: System.Windows.Input.ICommand
  commentId: T:System.Windows.Input.ICommand
  parent: System.Windows.Input
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.windows.input.icommand
  name: ICommand
  nameWithType: ICommand
  fullName: System.Windows.Input.ICommand
- uid: System.Windows.Input
  commentId: N:System.Windows.Input
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Windows.Input
  nameWithType: System.Windows.Input
  fullName: System.Windows.Input
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Windows
    name: Windows
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.windows
  - name: .
  - uid: System.Windows.Input
    name: Input
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.windows.input
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Windows
    name: Windows
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.windows
  - name: .
  - uid: System.Windows.Input
    name: Input
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.windows.input
- uid: System.EventHandler{DrawnUi.Draw.SkiaControl.ControlTappedEventArgs}
  commentId: T:System.EventHandler{DrawnUi.Draw.SkiaControl.ControlTappedEventArgs}
  parent: System
  definition: System.EventHandler`1
  href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  name: EventHandler<SkiaControl.ControlTappedEventArgs>
  nameWithType: EventHandler<SkiaControl.ControlTappedEventArgs>
  fullName: System.EventHandler<DrawnUi.Draw.SkiaControl.ControlTappedEventArgs>
  nameWithType.vb: EventHandler(Of SkiaControl.ControlTappedEventArgs)
  fullName.vb: System.EventHandler(Of DrawnUi.Draw.SkiaControl.ControlTappedEventArgs)
  name.vb: EventHandler(Of SkiaControl.ControlTappedEventArgs)
  spec.csharp:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: <
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: .
  - uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
    name: ControlTappedEventArgs
    href: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html
  - name: '>'
  spec.vb:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaControl
    name: SkiaControl
    href: DrawnUi.Draw.SkiaControl.html
  - name: .
  - uid: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs
    name: ControlTappedEventArgs
    href: DrawnUi.Draw.SkiaControl.ControlTappedEventArgs.html
  - name: )
- uid: System.EventHandler`1
  commentId: T:System.EventHandler`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  name: EventHandler<TEventArgs>
  nameWithType: EventHandler<TEventArgs>
  fullName: System.EventHandler<TEventArgs>
  nameWithType.vb: EventHandler(Of TEventArgs)
  fullName.vb: System.EventHandler(Of TEventArgs)
  name.vb: EventHandler(Of TEventArgs)
  spec.csharp:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: <
  - name: TEventArgs
  - name: '>'
  spec.vb:
  - uid: System.EventHandler`1
    name: EventHandler
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.eventhandler-1
  - name: (
  - name: Of
  - name: " "
  - name: TEventArgs
  - name: )
- uid: DrawnUi.Draw.TextSpan.ParentControl*
  commentId: Overload:DrawnUi.Draw.TextSpan.ParentControl
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ParentControl
  name: ParentControl
  nameWithType: TextSpan.ParentControl
  fullName: DrawnUi.Draw.TextSpan.ParentControl
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  commentId: M:Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
  parent: Microsoft.Maui.Controls.Element
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  name: OnPropertyChanged(string)
  nameWithType: Element.OnPropertyChanged(string)
  fullName: Microsoft.Maui.Controls.Element.OnPropertyChanged(string)
  nameWithType.vb: Element.OnPropertyChanged(String)
  fullName.vb: Microsoft.Maui.Controls.Element.OnPropertyChanged(String)
  name.vb: OnPropertyChanged(String)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  - name: (
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Element.OnPropertyChanged(System.String)
    name: OnPropertyChanged
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.element.onpropertychanged
  - name: (
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: DrawnUi.Draw.TextSpan.OnPropertyChanged*
  commentId: Overload:DrawnUi.Draw.TextSpan.OnPropertyChanged
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_OnPropertyChanged_System_String_
  name: OnPropertyChanged
  nameWithType: TextSpan.OnPropertyChanged
  fullName: DrawnUi.Draw.TextSpan.OnPropertyChanged
- uid: DrawnUi.Draw.TextSpan.#ctor*
  commentId: Overload:DrawnUi.Draw.TextSpan.#ctor
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan__ctor
  name: TextSpan
  nameWithType: TextSpan.TextSpan
  fullName: DrawnUi.Draw.TextSpan.TextSpan
  nameWithType.vb: TextSpan.New
  fullName.vb: DrawnUi.Draw.TextSpan.New
  name.vb: New
- uid: DrawnUi.Draw.TextSpan.UpdateFont*
  commentId: Overload:DrawnUi.Draw.TextSpan.UpdateFont
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_UpdateFont
  name: UpdateFont
  nameWithType: TextSpan.UpdateFont
  fullName: DrawnUi.Draw.TextSpan.UpdateFont
- uid: DrawnUi.Draw.TextSpan.FontFamily*
  commentId: Overload:DrawnUi.Draw.TextSpan.FontFamily
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontFamily
  name: FontFamily
  nameWithType: TextSpan.FontFamily
  fullName: DrawnUi.Draw.TextSpan.FontFamily
- uid: DrawnUi.Draw.TextSpan.LineSpacing*
  commentId: Overload:DrawnUi.Draw.TextSpan.LineSpacing
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_LineSpacing
  name: LineSpacing
  nameWithType: TextSpan.LineSpacing
  fullName: DrawnUi.Draw.TextSpan.LineSpacing
- uid: DrawnUi.Draw.TextSpan.LineHeight*
  commentId: Overload:DrawnUi.Draw.TextSpan.LineHeight
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_LineHeight
  name: LineHeight
  nameWithType: TextSpan.LineHeight
  fullName: DrawnUi.Draw.TextSpan.LineHeight
- uid: DrawnUi.Draw.TextSpan.FontWeight*
  commentId: Overload:DrawnUi.Draw.TextSpan.FontWeight
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontWeight
  name: FontWeight
  nameWithType: TextSpan.FontWeight
  fullName: DrawnUi.Draw.TextSpan.FontWeight
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.TextSpan.TextColor*
  commentId: Overload:DrawnUi.Draw.TextSpan.TextColor
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_TextColor
  name: TextColor
  nameWithType: TextSpan.TextColor
  fullName: DrawnUi.Draw.TextSpan.TextColor
- uid: DrawnUi.Draw.TextSpan.BackgroundColor*
  commentId: Overload:DrawnUi.Draw.TextSpan.BackgroundColor
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_BackgroundColor
  name: BackgroundColor
  nameWithType: TextSpan.BackgroundColor
  fullName: DrawnUi.Draw.TextSpan.BackgroundColor
- uid: DrawnUi.Draw.TextSpan.ParagraphColor*
  commentId: Overload:DrawnUi.Draw.TextSpan.ParagraphColor
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_ParagraphColor
  name: ParagraphColor
  nameWithType: TextSpan.ParagraphColor
  fullName: DrawnUi.Draw.TextSpan.ParagraphColor
- uid: DrawnUi.Draw.TextSpan.FontSize*
  commentId: Overload:DrawnUi.Draw.TextSpan.FontSize
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontSize
  name: FontSize
  nameWithType: TextSpan.FontSize
  fullName: DrawnUi.Draw.TextSpan.FontSize
- uid: DrawnUi.Draw.TextSpan.IsItalic*
  commentId: Overload:DrawnUi.Draw.TextSpan.IsItalic
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_IsItalic
  name: IsItalic
  nameWithType: TextSpan.IsItalic
  fullName: DrawnUi.Draw.TextSpan.IsItalic
- uid: DrawnUi.Draw.TextSpan.IsBold*
  commentId: Overload:DrawnUi.Draw.TextSpan.IsBold
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_IsBold
  name: IsBold
  nameWithType: TextSpan.IsBold
  fullName: DrawnUi.Draw.TextSpan.IsBold
- uid: DrawnUi.Draw.TextSpan.AutoFindFont*
  commentId: Overload:DrawnUi.Draw.TextSpan.AutoFindFont
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_AutoFindFont
  name: AutoFindFont
  nameWithType: TextSpan.AutoFindFont
  fullName: DrawnUi.Draw.TextSpan.AutoFindFont
- uid: DrawnUi.Draw.TextSpan.FontDetectedWith*
  commentId: Overload:DrawnUi.Draw.TextSpan.FontDetectedWith
  href: DrawnUi.Draw.TextSpan.html#DrawnUi_Draw_TextSpan_FontDetectedWith
  name: FontDetectedWith
  nameWithType: TextSpan.FontDetectedWith
  fullName: DrawnUi.Draw.TextSpan.FontDetectedWith
