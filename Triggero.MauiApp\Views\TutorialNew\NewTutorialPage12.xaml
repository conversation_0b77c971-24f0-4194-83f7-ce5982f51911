﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:parts="clr-namespace:Triggero.Controls.Parts"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage12"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="360"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="140"/>
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="3"
                Aspect="Fill"
                Source="tutorialBlur3.png"/>


            <Grid Grid.Row="0">

                <Grid
                    Margin="20,0,20,0"
                    Padding="0"
                    VerticalOptions="End"
                    HeightRequest="310">
                    
                    <Image
                        Aspect="Fill"
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill"
                        Margin="-2,0,0,0"
                        Source="tutorialTrackerContainer.png"/>

                    <Grid Padding="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="56"/>
                        </Grid.RowDefinitions>

                        <Grid 
                            Margin="25,0,25,0"
                            HorizontalOptions="Fill"
                            VerticalOptions="Center"
                            Grid.Row="0">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="50"/>
                                <RowDefinition Height="85"/>
                                <RowDefinition Height="85"/>
                            </Grid.RowDefinitions>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                                <ColumnDefinition Width="1*"/>
                            </Grid.ColumnDefinitions>

                            <Grid Grid.Row="0"
                                  Grid.ColumnSpan="3">
                                <Label 
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontSize="17"
                                    FontAttributes="Bold"
                                    Margin="0,0,0,0"             
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerStart.HowAreYou}"/>
                            </Grid>

                            <StackLayout
                                Grid.Row="1"
                                Grid.Column="0"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <Image 
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="60"
                                    WidthRequest="60"
                                    Source="dayMood1.png"/>
                                <Label 
                                    HorizontalOptions="Center"
                                    FontSize="10"
                                    TextColor="#000000"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood5}"/>
                            </StackLayout>

                            <StackLayout
                                Grid.Row="1"
                                Grid.Column="1"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <Image 
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="60"
                                    WidthRequest="60"
                                    Source="dayMood2.png"/>
                                <Label 
                                    HorizontalOptions="Center"
                                    FontSize="10"
                                    TextColor="#000000"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood4}"/>
                            </StackLayout>

                            <StackLayout
                                Grid.Row="1"
                                Grid.Column="2"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <Image 
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="60"
                                    WidthRequest="60"
                                    Source="dayMood3.png"/>
                                <Label 
                                    HorizontalOptions="Center"
                                    FontSize="10"
                                    TextColor="#000000"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood3}"/>
                            </StackLayout>

                            <StackLayout
                                Grid.Row="2"
                                Grid.Column="0"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <Image 
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="60"
                                    WidthRequest="60"
                                    Source="dayMood4.png"/>
                                <Label 
                                    HorizontalOptions="Center"
                                    FontSize="10"
                                    TextColor="#000000"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood2}"/>
                            </StackLayout>

                            <StackLayout
                                Grid.Row="2"
                                Grid.Column="1"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <Image 
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="60"
                                    WidthRequest="60"
                                    Source="dayMood5.png"/>
                                <Label 
                                    HorizontalOptions="Center"
                                    FontSize="10"
                                    TextColor="#000000"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood1}"/>
                            </StackLayout>

                            <StackLayout
                                Grid.Row="2"
                                Grid.Column="2"
                                HorizontalOptions="Center"
                                VerticalOptions="Center">
                                <Image 
                                    VerticalOptions="Center"
                                    HorizontalOptions="Center"
                                    HeightRequest="60"
                                    WidthRequest="60"
                                    Source="dayMood6.png"/>
                                <Label 
                                    HorizontalOptions="Center"
                                    FontSize="10"
                                    TextColor="#000000"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerGeneral.Mood0}"/>
                            </StackLayout>

                        </Grid>

                        <Grid
                            BackgroundColor="Transparent"
                            Grid.Row="1">
                            <!--<Button 
                                Command="{Binding Source={x:Reference this},Path=GoToTracker}"
                                Style="{x:StaticResource transparent_btn}"            
                                Text="{Binding Source={x:Static app:App.This},Path=Interface.MoodTracker.TrackerStart.MoodTracker}"
                                VerticalOptions="Center"
                                HorizontalOptions="Center"/>-->

                            <Label
                                TextColor="{x:StaticResource greyTextColor}"
                                FontSize="17"
                                Margin="0,-6,0,0"
                                TextDecorations="Underline"
                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MoodTracker.TrackerStart.MoodTracker}"
                                VerticalOptions="Center"
                                HorizontalOptions="Center">
                            </Label>

                        </Grid>

                    </Grid>

                </Grid>



            </Grid>

            <Grid Grid.Row="1">

                <StackLayout
                    Margin="20,0,20,0">

                    <Grid 
                    Margin="0,10,0,0"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill"
                    HeightRequest="100">

                        <Frame
                            BackgroundColor="White"
                            Background="White"
                            CornerRadius="34"
                            HeightRequest="68"
                            WidthRequest="68"
                            HasShadow="False"
                            Padding="0"
                            HorizontalOptions="Center"
                            VerticalOptions="Start"/>


                        <Frame
                            BackgroundColor="White"
                            Background="White"
                            CornerRadius="10"
                            HeightRequest="76"
                            HasShadow="False"
                            Padding="0"
                            VerticalOptions="End"/>

                        <parts:TransparentFooter 
                            InputTransparent="True"
                            HorizontalOptions="Fill"/>


                    </Grid>

                    <Label 
                        Margin="0,35,0,0"
                        TextColor="#000000"
                        FontAttributes="Bold"
                        FontSize="{x:OnPlatform Android=Large,iOS=19}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="317"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage12.Header}"/>
                    <Label 
                        Margin="0,0,0,0"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="{x:OnPlatform Android=Default,iOS=16}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="320"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage12.Description}"/>

                </StackLayout>
                
            </Grid>


            <Grid Grid.Row="2">
                <Button 
                    Command="{Binding Source={x:Reference this},Path=FinishTutorial}"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill"
                    Margin="63,0,63,0"
                    HeightRequest="56"
                    FontSize="17"
                    FontAttributes="Bold"
                    Style="{x:StaticResource yellow_btn}"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage12.GoNext}"/>

            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>