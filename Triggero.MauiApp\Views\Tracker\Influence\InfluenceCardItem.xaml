﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Cards.Tracker.Influence.InfluenceCardItem">
  <ContentView.Content>
        <Frame
            x:Name="frame"
            HasShadow="False"
            Padding="0"
            Background="#F8F9FC"
            CornerRadius="10">
            <Label 
                x:Name="titleLabel"
                TextColor="#000000"
                FontSize="{x:OnPlatform Android=12,iOS=14}"
                FontFamily="FontTextLight"
                VerticalOptions="Center"
                HorizontalTextAlignment="Center"
                HorizontalOptions="Center"/>
        </Frame>
    </ContentView.Content>
</ContentView>