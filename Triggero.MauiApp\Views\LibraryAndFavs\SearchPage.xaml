﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.SearchPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:viewModels="clr-namespace:Triggero.MauiMobileApp.ViewModels"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:controls="clr-namespace:Triggero.MauiMobileApp.Controls"
    x:Name="this"
    x:DataType="viewModels:SearchPageViewModel"
    BackgroundColor="White"
    NavigationPage.HasNavigationBar="False">

    <ContentPage.Content>
        <Grid BackgroundColor="#FFFFFF">
            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="70" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="1">
                <Grid Margin="5,20,25,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="50" />
                        <ColumnDefinition Width="*" />
                    </Grid.ColumnDefinitions>

                    <ImageButton
                        Grid.Column="0"
                        BackgroundColor="Transparent"
                        Command="{Binding Source={x:Reference this}, Path=Close}"
                        CornerRadius="0"
                        HeightRequest="16"
                        HorizontalOptions="Center"
                        Source="arrowStickBack.png"
                        VerticalOptions="Center"
                        WidthRequest="16" />


                    <Grid Grid.Column="1">

                        <!--todo-->
                        
                        <!--Padding="{x:OnPlatform Android='44,8,12,8',
                        iOS='44,0,12,0'}"
                        FocusedBorderColor="#EBEBEB"-->

                        <Entry
                            BackgroundColor="#FBFBFB"
                            HeightRequest="40"
                            HorizontalOptions="Fill"
                            Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.SearchPage.Search}"
                            Style="{x:StaticResource grayTextEdit}"
                            Text="{Binding Source={x:Reference this}, Path=SearchQuery, Mode=TwoWay}"
                            TextChanged="textChanged"
                            Unfocused="onTextEditUnfocused"
                            VerticalOptions="Center" />

                        <Image
                            Margin="15,0,0,0"
                            HeightRequest="16"
                            HorizontalOptions="Start"
                            Source="searchGray.png"
                            VerticalOptions="Center"
                            WidthRequest="16" />

                    </Grid>

                </Grid>
            </Grid>

            <Grid Grid.Row="2">

                <!--  DRAWN  -->
                <draw:Canvas
                    x:Name="DrawnCanvas"
                    Margin="10,0,10,20"
                    Gestures="Lock"
                    RenderingMode="Accelerated"
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">

                    <draw:SkiaLayout
                        HorizontalOptions="Fill"
                        VerticalOptions="Fill">

                        <draw:SkiaScroll
                            FrictionScrolled="0.35"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill">

                            <draw:SkiaLayout
                                x:Name="StackCells"
                                Padding="0,0,0,0"
                                HorizontalOptions="Fill"
                                ItemTemplate="{Binding ItemTemplate}"
                                ItemsSource="{Binding Items}"
                                RecyclingTemplate="Enabled"
                                Spacing="-8"
                                Type="Column"
                                VirtualisationInflated="350">
                                <!--<draw:SkiaLayout.EmptyView>

                                    <draw:SkiaLabel
                                        Margin="0,33,0,0"
                                        FontAttributes="Bold"
                                        FontSize="22"
                                        HorizontalOptions="Center"
                                        HorizontalTextAlignment="Center"
                                        Text="{Binding Source={x:Static app:App.This}, Path=Interface.SearchPage.FrequentRequests}"
                                        TextColor="{x:StaticResource greyTextColor}" />

                                </draw:SkiaLayout.EmptyView>-->
                            </draw:SkiaLayout>

                        </draw:SkiaScroll>

                        <!--  FPS  -->
                        <draw:SkiaLabelFps
                            Margin="0,0,4,84"
                            BackgroundColor="DarkRed"
                            ForceRefresh="False"
                            HorizontalOptions="End"
                            IsVisible="{x:Static triggeroV2:Globals.ShowFPS}"
                            Rotation="-45"
                            TextColor="White"
                            VerticalOptions="End" />

                    </draw:SkiaLayout>

                </draw:Canvas>

                <StackLayout
                    Margin="0,50,0,0"
                    IsVisible="{Binding IsEmpty}">

                    <!--<Image
                        Source="fuckYouEye.png"
                        HorizontalOptions="Center"
                        VerticalOptions="Start"
                        HeightRequest="50"
                        WidthRequest="70"/>-->

                    <Label
                        Margin="24,24,24,0"
                        FontAttributes="Bold"
                        FontSize="14"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        IsVisible="{Binding NothingFound}"
                        Text="Ничего не найдено, попробуйте изменить строку запроса."
                        TextColor="{x:StaticResource greyTextColor}" />


                    <controls:GifCachedImage
                        HeightRequest="50"
                        HorizontalOptions="Center"
                        IsVisible="{Binding NothingFound, Converter={x:StaticResource NotConverter}}"
                        Source="animationEyeSearchAnim.gif"
                        VerticalOptions="Start"
                        WidthRequest="70" />

                    <Label
                        Margin="0,33,0,0"
                        FontAttributes="Bold"
                        FontSize="18"
                        HorizontalOptions="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.SearchPage.FrequentRequests}"
                        TextColor="{x:StaticResource greyTextColor}" />

                    <StackLayout
                        x:Name="queriesLayout"
                        Margin="0,22,0,0"
                        Spacing="20">

                        <!--<Label
                            TextColor="{x:StaticResource blueColor}"
                            FontSize="17"
                            TextDecorations="Underline"
                            HorizontalOptions="Center"
                            Text="{Binding Source={x:Static app:App.This},Path=Interface.SearchPage.Anxienty}">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SetSearchQuery}" CommandParameter="{Binding Source={x:Static app:App.This},Path=Interface.SearchPage.Anxienty}"/>
                            </Label.GestureRecognizers>
                        </Label>

                        <Label
                            TextColor="{x:StaticResource blueColor}"
                            FontSize="17"
                            TextDecorations="Underline"
                            HorizontalOptions="Center"
                            Text="{Binding Source={x:Static app:App.This},Path=Interface.SearchPage.PanicAttack}">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SetSearchQuery}" CommandParameter="{Binding Source={x:Static app:App.This},Path=Interface.SearchPage.PanicAttack}"/>
                            </Label.GestureRecognizers>
                        </Label>

                        <Label
                            TextColor="{x:StaticResource blueColor}"
                            FontSize="17"
                            TextDecorations="Underline"
                            HorizontalOptions="Center"
                            Text="{Binding Source={x:Static app:App.This},Path=Interface.SearchPage.Relationships}">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SetSearchQuery}" CommandParameter="{Binding Source={x:Static app:App.This},Path=Interface.SearchPage.Relationships}"/>
                            </Label.GestureRecognizers>
                        </Label>

                        <Label
                            TextColor="{x:StaticResource blueColor}"
                            FontSize="17"
                            TextDecorations="Underline"
                            HorizontalOptions="Center"
                            Text="{Binding Source={x:Static app:App.This},Path=Interface.SearchPage.Conflict}">
                            <Label.GestureRecognizers>
                                <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=SetSearchQuery}" CommandParameter="{Binding Source={x:Static app:App.This},Path=Interface.SearchPage.Conflict}"/>
                            </Label.GestureRecognizers>
                        </Label>-->


                    </StackLayout>

                </StackLayout>



                <!--<Grid.Triggers>
                        <DataTrigger
                            Binding="{Binding Source={x:Reference this}, Path=IsMainBlockVisible}"
                            TargetType="Grid"
                            Value="False">
                            <Setter Property="IsVisible" Value="True" />
                        </DataTrigger>
                    </Grid.Triggers>-->

                <!--<CollectionView
                        Margin="20,0,20,20"
                        x:Name="collection">

                        <CollectionView.ItemTemplate>
                            <controls:MaterialsDataTemplateSelector>
                                <controls:MaterialsDataTemplateSelector.TestTemplate>
                                    <DataTemplate>
                                        <templates:TestCard
                                            HeightRequest="104"
                                            VerticalOptions="Start"
                                            HorizontalOptions="Fill"
                                            Padding="0,12,0,0" />
                                    </DataTemplate>
                                </controls:MaterialsDataTemplateSelector.TestTemplate>
                                <controls:MaterialsDataTemplateSelector.ExerciseTemplate>
                                    <DataTemplate>
                                        <templates:ExerciseCard
                                            HeightRequest="104"
                                            VerticalOptions="Start"
                                            HorizontalOptions="Fill"
                                            Padding="0,12,0,0" />
                                    </DataTemplate>
                                </controls:MaterialsDataTemplateSelector.ExerciseTemplate>
                                <controls:MaterialsDataTemplateSelector.PracticeTemplate>
                                    <DataTemplate>
                                        <templates:PracticeCard
                                            HeightRequest="104"
                                            VerticalOptions="Start"
                                            HorizontalOptions="Fill"
                                            Padding="0,12,0,0" />
                                    </DataTemplate>
                                </controls:MaterialsDataTemplateSelector.PracticeTemplate>
                                <controls:MaterialsDataTemplateSelector.TopicTemplate>
                                    <DataTemplate>
                                        <templates:TopicCard
                                            HeightRequest="104"
                                            VerticalOptions="Start"
                                            HorizontalOptions="Fill"
                                            Padding="0,12,0,0" />
                                    </DataTemplate>
                                </controls:MaterialsDataTemplateSelector.TopicTemplate>

                            </controls:MaterialsDataTemplateSelector>
                        </CollectionView.ItemTemplate>
                    </CollectionView>-->





            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>