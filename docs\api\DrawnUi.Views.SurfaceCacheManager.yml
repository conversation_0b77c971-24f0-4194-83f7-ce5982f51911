### YamlMime:ManagedReference
items:
- uid: DrawnUi.Views.SurfaceCacheManager
  commentId: T:DrawnUi.Views.SurfaceCacheManager
  id: SurfaceCacheManager
  parent: DrawnUi.Views
  children:
  - DrawnUi.Views.SurfaceCacheManager.#ctor(DrawnUi.Views.IDisposeManager)
  - DrawnUi.Views.SurfaceCacheManager.Dispose
  - DrawnUi.Views.SurfaceCacheManager.GetSurface(System.Int32,System.Int32)
  - DrawnUi.Views.SurfaceCacheManager.ReturnSurface(SkiaSharp.SKSurface)
  langs:
  - csharp
  - vb
  name: SurfaceCacheManager
  nameWithType: SurfaceCacheManager
  fullName: DrawnUi.Views.SurfaceCacheManager
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SurfaceCacheManager
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 5
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public class SurfaceCacheManager
    content.vb: Public Class SurfaceCacheManager
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Views.SurfaceCacheManager.#ctor(DrawnUi.Views.IDisposeManager)
  commentId: M:DrawnUi.Views.SurfaceCacheManager.#ctor(DrawnUi.Views.IDisposeManager)
  id: '#ctor(DrawnUi.Views.IDisposeManager)'
  parent: DrawnUi.Views.SurfaceCacheManager
  langs:
  - csharp
  - vb
  name: SurfaceCacheManager(IDisposeManager)
  nameWithType: SurfaceCacheManager.SurfaceCacheManager(IDisposeManager)
  fullName: DrawnUi.Views.SurfaceCacheManager.SurfaceCacheManager(DrawnUi.Views.IDisposeManager)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 17
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public SurfaceCacheManager(IDisposeManager disposeManager)
    parameters:
    - id: disposeManager
      type: DrawnUi.Views.IDisposeManager
    content.vb: Public Sub New(disposeManager As IDisposeManager)
  overload: DrawnUi.Views.SurfaceCacheManager.#ctor*
  nameWithType.vb: SurfaceCacheManager.New(IDisposeManager)
  fullName.vb: DrawnUi.Views.SurfaceCacheManager.New(DrawnUi.Views.IDisposeManager)
  name.vb: New(IDisposeManager)
- uid: DrawnUi.Views.SurfaceCacheManager.GetSurface(System.Int32,System.Int32)
  commentId: M:DrawnUi.Views.SurfaceCacheManager.GetSurface(System.Int32,System.Int32)
  id: GetSurface(System.Int32,System.Int32)
  parent: DrawnUi.Views.SurfaceCacheManager
  langs:
  - csharp
  - vb
  name: GetSurface(int, int)
  nameWithType: SurfaceCacheManager.GetSurface(int, int)
  fullName: DrawnUi.Views.SurfaceCacheManager.GetSurface(int, int)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetSurface
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 22
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public SKSurface GetSurface(int width, int height)
    parameters:
    - id: width
      type: System.Int32
    - id: height
      type: System.Int32
    return:
      type: SkiaSharp.SKSurface
    content.vb: Public Function GetSurface(width As Integer, height As Integer) As SKSurface
  overload: DrawnUi.Views.SurfaceCacheManager.GetSurface*
  nameWithType.vb: SurfaceCacheManager.GetSurface(Integer, Integer)
  fullName.vb: DrawnUi.Views.SurfaceCacheManager.GetSurface(Integer, Integer)
  name.vb: GetSurface(Integer, Integer)
- uid: DrawnUi.Views.SurfaceCacheManager.ReturnSurface(SkiaSharp.SKSurface)
  commentId: M:DrawnUi.Views.SurfaceCacheManager.ReturnSurface(SkiaSharp.SKSurface)
  id: ReturnSurface(SkiaSharp.SKSurface)
  parent: DrawnUi.Views.SurfaceCacheManager
  langs:
  - csharp
  - vb
  name: ReturnSurface(SKSurface)
  nameWithType: SurfaceCacheManager.ReturnSurface(SKSurface)
  fullName: DrawnUi.Views.SurfaceCacheManager.ReturnSurface(SkiaSharp.SKSurface)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ReturnSurface
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 44
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public void ReturnSurface(SKSurface surface)
    parameters:
    - id: surface
      type: SkiaSharp.SKSurface
    content.vb: Public Sub ReturnSurface(surface As SKSurface)
  overload: DrawnUi.Views.SurfaceCacheManager.ReturnSurface*
- uid: DrawnUi.Views.SurfaceCacheManager.Dispose
  commentId: M:DrawnUi.Views.SurfaceCacheManager.Dispose
  id: Dispose
  parent: DrawnUi.Views.SurfaceCacheManager
  langs:
  - csharp
  - vb
  name: Dispose()
  nameWithType: SurfaceCacheManager.Dispose()
  fullName: DrawnUi.Views.SurfaceCacheManager.Dispose()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 124
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public void Dispose()
    content.vb: Public Sub Dispose()
  overload: DrawnUi.Views.SurfaceCacheManager.Dispose*
references:
- uid: DrawnUi.Views
  commentId: N:DrawnUi.Views
  href: DrawnUi.html
  name: DrawnUi.Views
  nameWithType: DrawnUi.Views
  fullName: DrawnUi.Views
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Views.SurfaceCacheManager.#ctor*
  commentId: Overload:DrawnUi.Views.SurfaceCacheManager.#ctor
  href: DrawnUi.Views.SurfaceCacheManager.html#DrawnUi_Views_SurfaceCacheManager__ctor_DrawnUi_Views_IDisposeManager_
  name: SurfaceCacheManager
  nameWithType: SurfaceCacheManager.SurfaceCacheManager
  fullName: DrawnUi.Views.SurfaceCacheManager.SurfaceCacheManager
  nameWithType.vb: SurfaceCacheManager.New
  fullName.vb: DrawnUi.Views.SurfaceCacheManager.New
  name.vb: New
- uid: DrawnUi.Views.IDisposeManager
  commentId: T:DrawnUi.Views.IDisposeManager
  parent: DrawnUi.Views
  href: DrawnUi.Views.IDisposeManager.html
  name: IDisposeManager
  nameWithType: IDisposeManager
  fullName: DrawnUi.Views.IDisposeManager
- uid: DrawnUi.Views.SurfaceCacheManager.GetSurface*
  commentId: Overload:DrawnUi.Views.SurfaceCacheManager.GetSurface
  href: DrawnUi.Views.SurfaceCacheManager.html#DrawnUi_Views_SurfaceCacheManager_GetSurface_System_Int32_System_Int32_
  name: GetSurface
  nameWithType: SurfaceCacheManager.GetSurface
  fullName: DrawnUi.Views.SurfaceCacheManager.GetSurface
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: SkiaSharp.SKSurface
  commentId: T:SkiaSharp.SKSurface
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.sksurface
  name: SKSurface
  nameWithType: SKSurface
  fullName: SkiaSharp.SKSurface
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Views.SurfaceCacheManager.ReturnSurface*
  commentId: Overload:DrawnUi.Views.SurfaceCacheManager.ReturnSurface
  href: DrawnUi.Views.SurfaceCacheManager.html#DrawnUi_Views_SurfaceCacheManager_ReturnSurface_SkiaSharp_SKSurface_
  name: ReturnSurface
  nameWithType: SurfaceCacheManager.ReturnSurface
  fullName: DrawnUi.Views.SurfaceCacheManager.ReturnSurface
- uid: DrawnUi.Views.SurfaceCacheManager.Dispose*
  commentId: Overload:DrawnUi.Views.SurfaceCacheManager.Dispose
  href: DrawnUi.Views.SurfaceCacheManager.html#DrawnUi_Views_SurfaceCacheManager_Dispose
  name: Dispose
  nameWithType: SurfaceCacheManager.Dispose
  fullName: DrawnUi.Views.SurfaceCacheManager.Dispose
