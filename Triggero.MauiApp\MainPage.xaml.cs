﻿using CommunityToolkit.Maui;
using CommunityToolkit.Maui.Extensions;
//using Plugin.FirebasePushNotification;
using CommunityToolkit.Maui.Views;
using System.Diagnostics;
using Triggero.MauiMobileApp.Views.Pages.Subscriptions;
using Triggero.MauiMobileApp.Views.Popups;
using Triggero.MauiMobileApp.Helpers;

namespace Triggero.MauiPort.Views.Pages
{

    public partial class MainPage
    {
        public HomeView SubViewHome { get; set; }
        public LibraryView SubViewLibrary { get; set; }
        public TestsView SubViewTestCategories { get; set; }
        public ChatBotView SubViewChatBot { get; set; }

        bool maybe_exit;

        void Dev()
        {
            var st = 2;
        }

        protected override bool OnBackButtonPressed()
        {
            if (Navigation.NavigationStack.Count > 1 || Device.RuntimePlatform != Device.Android)
            {
                return false; //do nothing, lets the system pop us
            }

            if (GoBack()) // has views on top
            {
                return true;
            }

            if (maybe_exit)
            {
                return false; //exit
            }

            //disabled on purpose
            //App.ShowToast("Нажмите еще раз для выхода из программы");
            //maybe_exit = true;
            //Device.StartTimer(TimeSpan.FromSeconds(2), () =>
            //{
            //    maybe_exit = false;
            //    return false;
            //});

            return true; //true - dont process BACK by system
        }

        void LoadHomeView()
        {
            SubViewHome = new HomeView();
            MainViewGrid.Children.Add(SubViewHome);
            //SubViewHome.Render();
        }

        public MainPage()
        {
            try
            {
                InitializeComponent();
                NavigationPage.SetHasNavigationBar(this, false);

                LoadHomeView();

                this.Footer.IsMainPageSelected = true;
            }
            catch (Exception e)
            {
                Super.DisplayException(this, e);
            }
        }

        protected override void OnAppearing()
        {
            //App.PlatformUi.ApplyTheme();

            if (IsRendered)
                return;

            Render();
        }

        private bool IsRendered = false;
        private async Task Render()
        {
            IsRendered = true;

            //Получаем актуальные материалы
            Task.Run(async () =>
            {
                ApplicationState.TodayData.StartJob();

                Task.WaitAll(new Task[]
                {
                    ApplicationState.Data.GetTests(),
                    ApplicationState.Data.GetTopics(),
                    ApplicationState.Data.GetExercises(),
                    ApplicationState.Data.GetPractices(),
                });
            });



            MainThread.BeginInvokeOnMainThread(async () =>
            {
                var user = AuthHelper.User;
                if (user != null)
                {
                    if (AuthHelper.IsSubscriptionEnded && !App.This.WasShownEndSubscriptionPopup)
                    {
                        App.This.WasShownEndSubscriptionPopup = true;
                        if (AuthHelper.TrialActivated)
                        {
                            var popup = new SubscriptionEndedPopup();
                            await this.ShowPopupAsync(popup);
                        }
                        else
                        {
                            App.OpenPage(new SelectTrialPage());
                        }
                    }
                    else
                    {
                        if (!ApplicationState.ConfigData.WasShownFullTutorial && user.RegisteredAt.AddDays(1) > DateTime.Now)
                        {
                            _ = StartTutorial();
                        }
                    }
                }
            });

            try
            {
                var token = await Plugin.Firebase.CloudMessaging.CrossFirebaseCloudMessaging.Current.GetTokenAsync();
                // TODO: Replace with actual API endpoint URL
                await MobileApiRequestHelper.ExecuteRequestAsync(
                    $"https://your-api-host/api/Common/AddUserDeviceIfNew?userId={AuthHelper.User.Id}&token={token}",
                    HttpMethod.Post);
            }
            catch(Exception e)
            {
                Super.Log(e);
            }

        }

        private async Task StartTutorial()
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                if (!Constants.EnableTutorial)
                    return;

                await Task.Delay(30000);

                ApplicationState.ConfigData.WasShownFullTutorial = true;
                ApplicationState.ConfigData.SaveChangesToMemory();


                App.OpenPage(new NewTutorialPage1());

            });
        }

        public void SetMainView()
        {
            HideAllGrids();
            MainViewGrid.IsVisible = true;
            SubViewHome.IsVisible = true;

            MainViewGrid.Opacity = 0;
            MainViewGrid.FadeTo(1, 500);

            UpdateState();
        }

        public void FixMainView()
        {
            HideAllGrids();
            MainViewGrid.IsVisible = true;
            SubViewHome.IsVisible = true;
            MainViewGrid.Opacity = 1;
            UpdateState();
        }

        public void SetLibraryView()
        {
            HideAllGrids();

            LibraryGrid.IsVisible = true;

            SubViewLibrary.IsVisible = true;

            SubViewLibrary.UpdateTabsUponChecks();

            LibraryGrid.Opacity = 0.01;

            LibraryGrid.FadeTo(1, 500);

            UpdateState();
        }

        public void SetTestsView()
        {
            HideAllGrids();
            TestsGrid.IsVisible = true;
            SubViewTestCategories.IsVisible = true;

            TestsGrid.Opacity = 0;
            TestsGrid.FadeTo(1, 500);

            UpdateState();
        }

        public void SetChatBotView()
        {
            HideAllGrids();
            ChatBotGrid.IsVisible = true;
            SubViewChatBot.IsVisible = true;

            ChatBotGrid.Opacity = 0;
            ChatBotGrid.FadeTo(1, 500);

            SubViewChatBot.ScrollToEnd();

            UpdateState();
        }

        public bool GoBack()
        {
            if (contentGrid != null)
            {
                var view = contentGrid.Children.FirstOrDefault();
                if (view is not HomeView && view is not LibraryView && view is not TestsView && view is not ChatBotView && view != null)
                {
                    if (_lastTab != null)
                    {
                        SetView(_lastTab);
                        return true;
                    }
                }
            }
            return false;
        }

        View _lastTab;
        View _lastDisposableView;

        void UpdateState()
        {
            if (SubViewChatBot != null && SubViewChatBot.IsVisible)
                _lastTab = SubViewChatBot;
            else
            if (SubViewTestCategories != null && SubViewTestCategories.IsVisible)
                _lastTab = SubViewTestCategories;
            else
            if (SubViewLibrary != null && SubViewLibrary.IsVisible)
                _lastTab = SubViewLibrary;
            else
            if (SubViewHome != null && SubViewHome.IsVisible)
                _lastTab = SubViewHome;

            Debug.WriteLine($"[SHELL] last tab: {_lastTab}");

            App.UpdateState();
        }

        public void SetView(View view)
        {
            var dispose = _lastDisposableView as IDisposable;

            Debug.WriteLine($"[SHELL] will dispose: {dispose}");

            //_lastView = contentGrid.Children.LastOrDefault();

            HideAllGrids();

            if (view is HomeView)
            {
                SetMainView();
                Debug.WriteLine($"[SHELL] set view MAIN");
            }
            else if (view is LibraryView)
            {
                SetLibraryView();
                Debug.WriteLine($"[SHELL] set view LIBRARY");
            }
            else if (view is TestsView)
            {
                SetTestsView();
                Debug.WriteLine($"[SHELL] set view TESTS");
            }
            else if (view is ChatBotView)
            {
                SetChatBotView();
                Debug.WriteLine($"[SHELL] set view CHAT");

            }
            else
            {
                _lastDisposableView = view;

                contentGrid.Children.Clear();

                contentGrid.IsVisible = true;
                contentGrid.Opacity = 1;
                contentGrid.Children.Add(view);

                Debug.WriteLine($"[SHELL] set view {view}");
                //contentGrid.FadeTo(1, 500);
            }

            UpdateState();

            if (dispose != null)
            {
                dispose?.Dispose();
            }
        }

        private void HideAllGrids()
        {
            contentGrid.IsVisible = false;

            if (SubViewHome != null)
            {
                SubViewHome.IsVisible = false;
            }

            if (SubViewLibrary != null)
            {
                SubViewLibrary.IsVisible = false;
            }

            if (SubViewChatBot != null)
            {
                SubViewChatBot.IsVisible = false;
            }

            if (SubViewTestCategories != null)
            {
                SubViewTestCategories.IsVisible = false;
            }

            MainViewGrid.IsVisible = false;
            LibraryGrid.IsVisible = false;
            TestsGrid.IsVisible = false;
            ChatBotGrid.IsVisible = false;


        }

        private void onChildAdded(object sender, ElementEventArgs e)
        {
            //var view = e.Element as View;

            //view.Opacity = 0;
            //view.FadeTo(1, 100);
        }
    }
}