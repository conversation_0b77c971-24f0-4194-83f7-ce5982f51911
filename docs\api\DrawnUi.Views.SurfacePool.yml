### YamlMime:ManagedReference
items:
- uid: DrawnUi.Views.SurfacePool
  commentId: T:DrawnUi.Views.SurfacePool
  id: SurfacePool
  parent: DrawnUi.Views
  children:
  - DrawnUi.Views.SurfacePool.#ctor(System.Int32)
  - DrawnUi.Views.SurfacePool.Dispose(DrawnUi.Views.IDisposeManager)
  - DrawnUi.Views.SurfacePool.TryAdd(SkiaSharp.SKSurface)
  - DrawnUi.Views.SurfacePool.TryTake(SkiaSharp.SKSurface@)
  langs:
  - csharp
  - vb
  name: SurfacePool
  nameWithType: SurfacePool
  fullName: DrawnUi.Views.SurfacePool
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SurfacePool
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 170
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public class SurfacePool
    content.vb: Public Class SurfacePool
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Views.SurfacePool.#ctor(System.Int32)
  commentId: M:DrawnUi.Views.SurfacePool.#ctor(System.Int32)
  id: '#ctor(System.Int32)'
  parent: DrawnUi.Views.SurfacePool
  langs:
  - csharp
  - vb
  name: SurfacePool(int)
  nameWithType: SurfacePool.SurfacePool(int)
  fullName: DrawnUi.Views.SurfacePool.SurfacePool(int)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 176
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public SurfacePool(int maxSize)
    parameters:
    - id: maxSize
      type: System.Int32
    content.vb: Public Sub New(maxSize As Integer)
  overload: DrawnUi.Views.SurfacePool.#ctor*
  nameWithType.vb: SurfacePool.New(Integer)
  fullName.vb: DrawnUi.Views.SurfacePool.New(Integer)
  name.vb: New(Integer)
- uid: DrawnUi.Views.SurfacePool.TryTake(SkiaSharp.SKSurface@)
  commentId: M:DrawnUi.Views.SurfacePool.TryTake(SkiaSharp.SKSurface@)
  id: TryTake(SkiaSharp.SKSurface@)
  parent: DrawnUi.Views.SurfacePool
  langs:
  - csharp
  - vb
  name: TryTake(out SKSurface)
  nameWithType: SurfacePool.TryTake(out SKSurface)
  fullName: DrawnUi.Views.SurfacePool.TryTake(out SkiaSharp.SKSurface)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TryTake
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 181
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public bool TryTake(out SKSurface surface)
    parameters:
    - id: surface
      type: SkiaSharp.SKSurface
    return:
      type: System.Boolean
    content.vb: Public Function TryTake(surface As SKSurface) As Boolean
  overload: DrawnUi.Views.SurfacePool.TryTake*
  nameWithType.vb: SurfacePool.TryTake(SKSurface)
  fullName.vb: DrawnUi.Views.SurfacePool.TryTake(SkiaSharp.SKSurface)
  name.vb: TryTake(SKSurface)
- uid: DrawnUi.Views.SurfacePool.TryAdd(SkiaSharp.SKSurface)
  commentId: M:DrawnUi.Views.SurfacePool.TryAdd(SkiaSharp.SKSurface)
  id: TryAdd(SkiaSharp.SKSurface)
  parent: DrawnUi.Views.SurfacePool
  langs:
  - csharp
  - vb
  name: TryAdd(SKSurface)
  nameWithType: SurfacePool.TryAdd(SKSurface)
  fullName: DrawnUi.Views.SurfacePool.TryAdd(SkiaSharp.SKSurface)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TryAdd
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 193
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public bool TryAdd(SKSurface surface)
    parameters:
    - id: surface
      type: SkiaSharp.SKSurface
    return:
      type: System.Boolean
    content.vb: Public Function TryAdd(surface As SKSurface) As Boolean
  overload: DrawnUi.Views.SurfacePool.TryAdd*
- uid: DrawnUi.Views.SurfacePool.Dispose(DrawnUi.Views.IDisposeManager)
  commentId: M:DrawnUi.Views.SurfacePool.Dispose(DrawnUi.Views.IDisposeManager)
  id: Dispose(DrawnUi.Views.IDisposeManager)
  parent: DrawnUi.Views.SurfacePool
  langs:
  - csharp
  - vb
  name: Dispose(IDisposeManager)
  nameWithType: SurfacePool.Dispose(IDisposeManager)
  fullName: DrawnUi.Views.SurfacePool.Dispose(DrawnUi.Views.IDisposeManager)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dispose
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 205
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public void Dispose(IDisposeManager disposeManager)
    parameters:
    - id: disposeManager
      type: DrawnUi.Views.IDisposeManager
    content.vb: Public Sub Dispose(disposeManager As IDisposeManager)
  overload: DrawnUi.Views.SurfacePool.Dispose*
references:
- uid: DrawnUi.Views
  commentId: N:DrawnUi.Views
  href: DrawnUi.html
  name: DrawnUi.Views
  nameWithType: DrawnUi.Views
  fullName: DrawnUi.Views
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Views.SurfacePool.#ctor*
  commentId: Overload:DrawnUi.Views.SurfacePool.#ctor
  href: DrawnUi.Views.SurfacePool.html#DrawnUi_Views_SurfacePool__ctor_System_Int32_
  name: SurfacePool
  nameWithType: SurfacePool.SurfacePool
  fullName: DrawnUi.Views.SurfacePool.SurfacePool
  nameWithType.vb: SurfacePool.New
  fullName.vb: DrawnUi.Views.SurfacePool.New
  name.vb: New
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Views.SurfacePool.TryTake*
  commentId: Overload:DrawnUi.Views.SurfacePool.TryTake
  href: DrawnUi.Views.SurfacePool.html#DrawnUi_Views_SurfacePool_TryTake_SkiaSharp_SKSurface__
  name: TryTake
  nameWithType: SurfacePool.TryTake
  fullName: DrawnUi.Views.SurfacePool.TryTake
- uid: SkiaSharp.SKSurface
  commentId: T:SkiaSharp.SKSurface
  parent: SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp.sksurface
  name: SKSurface
  nameWithType: SKSurface
  fullName: SkiaSharp.SKSurface
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: SkiaSharp
  commentId: N:SkiaSharp
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/skiasharp
  name: SkiaSharp
  nameWithType: SkiaSharp
  fullName: SkiaSharp
- uid: DrawnUi.Views.SurfacePool.TryAdd*
  commentId: Overload:DrawnUi.Views.SurfacePool.TryAdd
  href: DrawnUi.Views.SurfacePool.html#DrawnUi_Views_SurfacePool_TryAdd_SkiaSharp_SKSurface_
  name: TryAdd
  nameWithType: SurfacePool.TryAdd
  fullName: DrawnUi.Views.SurfacePool.TryAdd
- uid: DrawnUi.Views.SurfacePool.Dispose*
  commentId: Overload:DrawnUi.Views.SurfacePool.Dispose
  href: DrawnUi.Views.SurfacePool.html#DrawnUi_Views_SurfacePool_Dispose_DrawnUi_Views_IDisposeManager_
  name: Dispose
  nameWithType: SurfacePool.Dispose
  fullName: DrawnUi.Views.SurfacePool.Dispose
- uid: DrawnUi.Views.IDisposeManager
  commentId: T:DrawnUi.Views.IDisposeManager
  parent: DrawnUi.Views
  href: DrawnUi.Views.IDisposeManager.html
  name: IDisposeManager
  nameWithType: IDisposeManager
  fullName: DrawnUi.Views.IDisposeManager
