using Newtonsoft.Json;
using RestSharp;
using System.Diagnostics;
using System.Net;
using System.Text;
using Odintcovo.API.Helpers;

namespace Triggero.MauiMobileApp.Helpers
{
    /// <summary>
    /// Mobile-optimized HTTP client helper for MAUI applications
    /// Uses HttpClient instead of RestSharp for better performance and security
    /// Maintains compatibility with existing RequestHelper API
    /// </summary>
    public static class MobileApiRequestHelper
    {
        private static readonly HttpClient _httpClient;
        private static string _token;
        private static IEnumerable<KeyValuePair<string, string>> _headers;

        public static EventHandler OnUnauthorized;

        static MobileApiRequestHelper()
        {
            var handler = new HttpClientHandler();
            
            // For development - bypass SSL certificate validation
            // TODO: Remove this in production or make it configurable
            handler.ServerCertificateCustomValidationCallback = (sender, certificate, chain, sslPolicyErrors) => true;
            
            _httpClient = new HttpClient(handler);
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
        }

        public static void SetBearerToken(string token)
        {
            _token = token;
        }

        public static void AddDefaultHeaders(IEnumerable<KeyValuePair<string, string>> headers)
        {
            _headers = headers;
        }

        public static void ClearDefaultHeaders()
        {
            _headers = null;
        }

        /// <summary>
        /// Response object that mimics RestSharp.RestResponse for compatibility
        /// </summary>
        public class MobileResponse
        {
            public string Content { get; set; }
            public HttpStatusCode StatusCode { get; set; }
            public string ErrorMessage { get; set; }
            public Exception ErrorException { get; set; }
            public bool IsSuccessful => (int)StatusCode >= 200 && (int)StatusCode <= 299;
        }

        private static System.Net.Http.HttpMethod ConvertRestSharpMethod(Method method)
        {
            return method switch
            {
                Method.Get => System.Net.Http.HttpMethod.Get,
                Method.Post => System.Net.Http.HttpMethod.Post,
                Method.Put => System.Net.Http.HttpMethod.Put,
                Method.Delete => System.Net.Http.HttpMethod.Delete,
                Method.Patch => new System.Net.Http.HttpMethod("PATCH"),
                _ => System.Net.Http.HttpMethod.Get
            };
        }

        /// <summary>
        /// Execute HTTP request with RestSharp Method enum compatibility
        /// </summary>
        public static async Task<RestResponse> ExecuteRequestAsync(
            string url,
            Method method,
            object body = null)
        {
            try
            {
                Debug.WriteLine($"[MobileApiRequestHelper] {url}");

                using var request = new HttpRequestMessage(ConvertRestSharpMethod(method), url);
                
                // Add authorization header if token is set
                if (!string.IsNullOrEmpty(_token))
                {
                    Debug.WriteLine($"[MobileApiRequestHelper] Authorization Bearer {_token}");
                    request.Headers.Add("Authorization", "Bearer " + _token);
                }

                // Add default headers
                if (_headers != null)
                {
                    foreach (var header in _headers)
                    {
                        request.Headers.Add(header.Key, header.Value);
                    }
                }

                // Add JSON body if provided
                if (body != null)
                {
                    Debug.WriteLine("[MobileApiRequestHelper] Sending request with body");
                    var json = JsonConvert.SerializeObject(body);
                    Debug.WriteLine(json);
                    
                    request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                }

                var response = await _httpClient.SendAsync(request);
                var content = await response.Content.ReadAsStringAsync();

                // Create RestResponse for compatibility
                var restResponse = new RestResponse
                {
                    Content = content,
                    StatusCode = response.StatusCode,
                    IsSuccessful = response.IsSuccessStatusCode
                };

                if (response.StatusCode == HttpStatusCode.Unauthorized)
                {
                    OnUnauthorized?.Invoke(null, null);
                }

                return restResponse;
            }
            catch (Exception e)
            {
                Debug.WriteLine($"[MobileApiRequestHelper] Error: {e.Message}");
                return new RestResponse
                {
                    ErrorMessage = e.Message,
                    ErrorException = e,
                    StatusCode = HttpStatusCode.BadRequest
                };
            }
        }

        /// <summary>
        /// Execute request and deserialize response to model
        /// </summary>
        public static async Task<T> ExecuteRequestReceiveModelAsync<T>(string url, Method method, object dto = null)
        {
            try
            {
                var response = await ExecuteRequestAsync(url, method, dto);

                if (!response.IsSuccessful)
                {
                    Debug.WriteLine($"[MobileApiRequestHelper] Request failed with status: {response.StatusCode}");
                    return default;
                }

                return JsonConvert.DeserializeObject<T>(response.Content);
            }
            catch (Exception e)
            {
                Debug.WriteLine($"[MobileApiRequestHelper] Deserialization error: {e}");
            }

            return default;
        }

        /// <summary>
        /// Dispose resources when application shuts down
        /// </summary>
        public static void Dispose()
        {
            _httpClient?.Dispose();
        }
    }
}
