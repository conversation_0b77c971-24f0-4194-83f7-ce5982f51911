﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="Triggero.Controls.MoodTracker.TrackerMoodPentagram"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    
    xmlns:charts="clr-namespace:Syncfusion.Maui.Charts;assembly=Syncfusion.Maui.Charts"
    x:Name="this">
    <ContentView.Content>

        <StackLayout>

            <Label
                Margin="0,13,0,0"
                FontAttributes="Bold"
                FontSize="17"
                HorizontalOptions="Start"
                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.MoodPentagram}"
                TextColor="{x:StaticResource greyTextColor}" />
            <Label
                FontSize="12"
                HorizontalOptions="Start"
                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.MoodPentagramDescription}"
                TextColor="{x:StaticResource ColorTextGray}" />


            <Grid
                HorizontalOptions="FillAndExpand"
                VerticalOptions="FillAndExpand">


                <Grid
                    HeightRequest="220"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    WidthRequest="220">
                    <Image
                        Aspect="AspectFill"
                        HeightRequest="220"
                        HorizontalOptions="Center"
                        Source="pentagramRings.png"
                        VerticalOptions="Center"
                        WidthRequest="220" />


                    <charts:SfPolarChart
                        Margin="{x:OnPlatform Android='-80',
                                            iOS='-100'}"
                        BackgroundColor="Transparent"
                        HorizontalOptions="FillAndExpand"
                        VerticalOptions="FillAndExpand">

                        <charts:SfPolarChart.PrimaryAxis>
                            <charts:CategoryAxis
                                IsVisible="False"
                                ShowMajorGridLines="False">
                                <charts:CategoryAxis.AxisLineStyle>
                                    <charts:ChartLineStyle
                                        Stroke="#CEE3F4"
                                        StrokeWidth="0.5" />
                                </charts:CategoryAxis.AxisLineStyle>
                                <charts:CategoryAxis.LabelStyle>
                                    <charts:ChartAxisLabelStyle TextColor="Transparent" />
                                </charts:CategoryAxis.LabelStyle>
                            </charts:CategoryAxis>
                        </charts:SfPolarChart.PrimaryAxis>

                        <charts:SfPolarChart.SecondaryAxis>
                            <charts:NumericalAxis
                                Interval="1"
                                IsVisible="False"
                                Maximum="5"
                                Minimum="0"
                                ShowMajorGridLines="False">
                                <charts:NumericalAxis.AxisLineStyle>
                                    <charts:ChartLineStyle
                                        Stroke="Transparent"
                                        StrokeWidth="0.01" />
                                </charts:NumericalAxis.AxisLineStyle>
                            </charts:NumericalAxis>
                        </charts:SfPolarChart.SecondaryAxis>

                        <charts:PolarAreaSeries
                            IsClosed="True"
                            ItemsSource="{Binding Source={x:Reference this}, Path=Data, Mode=TwoWay}"
                            Stroke="Transparent"
                            StrokeWidth="1"
                            XBindingPath="Title"
                            YBindingPath="Value"
                            Fill="Transparent"
                            ShowDataLabels="False">
                        </charts:PolarAreaSeries>

                        <charts:PolarAreaSeries
                            IsClosed="True"
                            ItemsSource="{Binding Source={x:Reference this}, Path=Data, Mode=TwoWay}"
                            Opacity="0.2"
                            Stroke="Transparent"
                            StrokeWidth="0.75"
                            XBindingPath="Title"
                            YBindingPath="Value"
                            Fill="#CEE3F4"
                            ShowDataLabels="False">
                        </charts:PolarAreaSeries>

                    </charts:SfPolarChart>
                </Grid>

                <StackLayout
                    Margin="0,-280,0,0"
                    HorizontalOptions="Center"
                    Spacing="2"
                    VerticalOptions="Center">
                    <Image
                        HeightRequest="40"
                        HorizontalOptions="Center"
                        Source="mood1.png"
                        VerticalOptions="Center"
                        WidthRequest="40" />
                    <Label
                        FontSize="9"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.HappinessHyphenation}"
                        TextColor="#000000"
                        WidthRequest="55" />
                </StackLayout>


                <StackLayout
                    Margin="247,-130,0,0"
                    HorizontalOptions="Center"
                    Spacing="2"
                    VerticalOptions="Center">
                    <Image
                        HeightRequest="40"
                        HorizontalOptions="Center"
                        Source="mood2.png"
                        VerticalOptions="Center"
                        WidthRequest="40" />
                    <Label
                        FontSize="9"

                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.InspirationHyphenation}"
                        TextColor="#000000"
                        WidthRequest="55" />
                </StackLayout>


                <StackLayout
                    Margin="247,130,0,0"
                    HorizontalOptions="Center"
                    Spacing="2"
                    VerticalOptions="Center">
                    <Image
                        HeightRequest="40"
                        HorizontalOptions="Center"
                        Source="mood9.png"
                        VerticalOptions="Center"
                        WidthRequest="40" />
                    <Label
                        FontSize="9"

                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.ConfidenceHyphenation}"
                        TextColor="#000000"
                        WidthRequest="55" />
                </StackLayout>

                <StackLayout
                    Margin="0,280,0,0"
                    HorizontalOptions="Center"
                    Spacing="2"
                    VerticalOptions="Center">
                    <Image
                        HeightRequest="40"
                        HorizontalOptions="Center"
                        Source="mood8.png"
                        VerticalOptions="Center"
                        WidthRequest="40" />
                    <Label
                        FontSize="9"

                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.StressHyphenation}"
                        TextColor="#000000"
                        WidthRequest="55" />
                </StackLayout>


                <StackLayout
                    Margin="-247,-130,0,0"
                    HorizontalOptions="Center"
                    Spacing="2"
                    VerticalOptions="Center">
                    <Image
                        HeightRequest="40"
                        HorizontalOptions="Center"
                        Source="mood6.png"
                        VerticalOptions="Center"
                        WidthRequest="40" />
                    <Label
                        FontSize="9"

                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.AnnoyanceHyphenation}"
                        TextColor="#000000"
                        WidthRequest="55" />
                </StackLayout>


                <StackLayout
                    Margin="-247,130,0,0"
                    HorizontalOptions="Center"
                    Spacing="2"
                    VerticalOptions="Center">
                    <Image
                        HeightRequest="40"
                        HorizontalOptions="Center"
                        Source="mood4.png"
                        VerticalOptions="Center"
                        WidthRequest="40" />
                    <Label
                        FontSize="9"

                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerGeneral.AnxientyHyphenation}"
                        TextColor="#000000"
                        WidthRequest="55" />
                </StackLayout>

            </Grid>

        </StackLayout>

    </ContentView.Content>
</ContentView>