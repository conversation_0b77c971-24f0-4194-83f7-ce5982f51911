﻿using Newtonsoft.Json;
using MobileAPIWrapper.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Domain.Models;
using Triggero.Models.Enums;
using Triggero.Models.Messengers.ChatBot;
using Triggero.Models.Messengers.ChatBot.Attachments;
using Triggero.Models.Tests;

namespace MobileAPIWrapper.Methods.Messengers
{
    public class ChatBotMessengerMethods
    {
        private string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("ChatBotMessenger/");


        public async Task<ChatBotChat> GetChat(int userId)
        {
            string url = BASE_HOST + $"GetChat?userId={userId}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<ChatBotChat>(str);
            return obj;
        }



        public async Task SendMessage(int userId, ChatBotMessage message)
        {
            var clone = CreateCloneMessage(message);

            string jsonEncoded = JsonConvert.SerializeObject(clone, new JsonSerializerSettings
            {
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                DateParseHandling = DateParseHandling.DateTime,
                DateFormatString = "yyyy-MM-ddTHH:mm:ss.511Z"
            });
            string jsonStrEncoded = JsonConvert.SerializeObject(jsonEncoded);

            string url = BASE_HOST + $"SendMessage?userId={userId}&newtonJsonEncoded={jsonEncoded}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Put);


        }
        public async Task SendMessages(int userId, List<ChatBotMessage> messages)
        {

            var cloneMessages = new List<ChatBotMessage>();
            foreach (var msg in messages)
            {
                var clone = CreateCloneMessage(msg);
                cloneMessages.Add(clone);
            }

            string jsonEncoded = JsonConvert.SerializeObject(cloneMessages, new JsonSerializerSettings
            {
                DateFormatHandling = DateFormatHandling.IsoDateFormat,
                DateParseHandling = DateParseHandling.DateTime,
                DateFormatString = "yyyy-MM-ddTHH:mm:ss.511Z"
            });
            string jsonStrEncoded = JsonConvert.SerializeObject(jsonEncoded);

            string url = BASE_HOST + $"SendMessages?userId={userId}&newtonJsonEncoded={jsonEncoded}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Put);


        }


        //2023-04-16T19:10:28.065809 00:00




        public async Task<ChatBotLongPollItem> LongPolling(int userId)
        {
            string url = BASE_HOST + $"LongPolling?userId={userId}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<ChatBotLongPollItem>(response.Content);
            return obj;
        }
        public async Task<ChatBotLongPollItem> LongPollingV2(int userId, int startMessagesCount)
        {
            string url = BASE_HOST + $"LongPollingV2?userId={userId}&startMessagesCount={startMessagesCount}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<ChatBotLongPollItem>(response.Content);
            return obj;
        }



        public async Task SetSeverity(int userId, ChatBotChatSeverity severity)
        {
            string url = BASE_HOST + $"SetSeverity?userId={userId}&severity={(int)severity}";
            var response = await MobileRequestApiHelper.ExecuteRequestAsync(url, Method.Put);
        }


        private ChatBotMessage CreateCloneMessage(ChatBotMessage msg)
        {
            var msgClone = msg.Clone();
            msgClone.Attachments = new List<Triggero.Models.Abstractions.ChatBotAttachment>();
            foreach (var attachment in msg.Attachments)
            {
                if (attachment is ChatBotContentAttachment contentAttachment)
                {
                    var cloneAttachment = contentAttachment.Clone();

                    cloneAttachment.Discriminator = "ChatBotContentAttachment";
                    cloneAttachment.TestId = cloneAttachment.Test?.Id;
                    cloneAttachment.ExerciseId = cloneAttachment.Exercise?.Id;
                    cloneAttachment.PracticeId = cloneAttachment.Practice?.Id;
                    cloneAttachment.TopicId = cloneAttachment.Topic?.Id;
                    cloneAttachment.BreathPracticeId = cloneAttachment.BreathPractice?.Id;

                    cloneAttachment.Test = null;
                    cloneAttachment.Exercise = null;
                    cloneAttachment.Practice = null;
                    cloneAttachment.Topic = null;
                    cloneAttachment.BreathPractice = null;

                    msgClone.Attachments.Add(cloneAttachment);
                }
            }
            return msgClone;
        }

    }

}
