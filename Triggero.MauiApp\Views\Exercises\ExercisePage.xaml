﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Library.ExercisePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:custom="clr-namespace:Triggero.MauiMobileApp.Custom"
    xmlns:other="clr-namespace:Triggero.MauiMobileApp.Controls.Other"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this"
    NavigationPage.HasNavigationBar="False"
    Background="White"
    BackgroundColor="White">
    <ContentPage.Content>
        <Grid RowSpacing="0">

            <Grid Margin="{x:OnPlatform Android='0', iOS='0,-50,0,0'}">
                <ScrollView
                    Margin="0,0,0,0"
                    VerticalScrollBarVisibility="Never">

                    <StackLayout x:Name="mainStackLayout">

                        <!--  BANNER NAVIGATION  -->
                        <Grid
                            HeightRequest="180"
                            VerticalOptions="Start">

                            <draw:Canvas
                                HeightRequest="180"
                                HorizontalOptions="Fill">
                                <draw:SkiaImage
                                    x:Name="headerImg"
                                    Aspect="AspectCover"
                                    HorizontalOptions="Fill"
                                    BackgroundColor="#11000000"
                                    VerticalOptions="Fill" />
                            </draw:Canvas>
                            
                            <!--<Image
                                x:Name="headerImg"
                                Aspect="AspectFill"
                                BackgroundColor="Gainsboro" />-->

                            <Grid
                                Margin="20,0,20,0"
                                HeightRequest="30"
                                VerticalOptions="Center">

                                <!--BTN CLOSE-->
                                <Grid
                                    Margin="-10,0,0,0"
                                    HeightRequest="55"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    WidthRequest="55">
                                    <Grid.GestureRecognizers>
                                        <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=Close}" />
                                    </Grid.GestureRecognizers>

                                    <ImageButton
                                        BackgroundColor="Transparent"
                                        Command="{Binding Source={x:Reference this}, Path=Close}"
                                        CornerRadius="0"
                                        HeightRequest="17"
                                        HorizontalOptions="Center"
                                        Source="closeWhite.png"
                                        VerticalOptions="Center"
                                        WidthRequest="17" />
                                </Grid>


                                <!--HEADER TITLE-->
                                <Label
                                    Margin="0,0,0,0"
                                    FontAttributes="Bold"
                                    FontSize="17"
                                    HorizontalOptions="Center"
                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Exercises}"
                                    TextColor="White"
                                    VerticalOptions="Center" />


                                <!--BTN FAV-->
                                <Grid
                                    Margin="0,0,-10,0"
                                    HeightRequest="55"
                                    HorizontalOptions="End"
                                    VerticalOptions="Center"
                                    WidthRequest="55">
                                    <Grid.GestureRecognizers>
                                        <TapGestureRecognizer Tapped="toggleFavorite" />
                                    </Grid.GestureRecognizers>
                                    <RadioButton
                                        x:Name="favoriteRb"
                                        CornerRadius="0"
                                        HeightRequest="24"
                                        HorizontalOptions="Center"
                                        InputTransparent="True"
                                        Style="{x:StaticResource favorite_hearted_white_rb}"
                                        VerticalOptions="Center"
                                        WidthRequest="24" />
                                </Grid>


                            </Grid>

                        </Grid>

                        <!--  TITLE HEADER  -->
                        <Label
                            x:Name="titleLabel"
                            Margin="20,15,120,0"
                            HorizontalOptions="Fill"
                            HorizontalTextAlignment="Start"
                            Style="{x:StaticResource StyleArticleTitle}" />

                        <Label
                            x:Name="subLabel"
                            Margin="20,0,20,0"
                            HorizontalOptions="Start"
                            Style="{x:StaticResource StyleArticleSubTitle}" />

                        <Grid
                            Margin="20,20,20,0"
                            HeightRequest="55">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                            </Grid.ColumnDefinitions>

                            <StackLayout
                                Grid.Column="0"
                                Orientation="Horizontal"
                                Spacing="8">
                                <Image
                                    Margin="0,-5,0,0"
                                    HeightRequest="16"
                                    HorizontalOptions="Start"
                                    Source="timeCircleAqua.png"
                                    VerticalOptions="Center"
                                    WidthRequest="16" />

                                <StackLayout
                                    HorizontalOptions="Start"
                                    Spacing="5"
                                    VerticalOptions="Start">
                                    <Label
                                        Margin="0,0,0,0"
                                        FontSize="12"
                                        HorizontalOptions="Start"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.PassingTime}"
                                        TextColor="{x:StaticResource greyTextColor}"
                                        VerticalOptions="Start" />
                                    <Label
                                        Margin="0,0,0,0"
                                        FontSize="12"
                                        HorizontalOptions="Start"
                                        TextColor="{x:StaticResource blueColor}"
                                        VerticalOptions="Start">
                                        <Label.FormattedText>
                                            <FormattedString>
                                                <FormattedString.Spans>
                                                    <Span Text="{Binding Source={x:Reference this}, Path=Exercise.PassingTimeInMinutes, Mode=OneWay}" />
                                                    <Span Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.MinutesAbbrevated}" />
                                                </FormattedString.Spans>
                                            </FormattedString>
                                        </Label.FormattedText>
                                    </Label>
                                </StackLayout>

                            </StackLayout>

                            <StackLayout
                                Grid.Column="1"
                                Orientation="Horizontal"
                                Spacing="8">
                                <Image
                                    Margin="0,-5,0,0"
                                    HeightRequest="16"
                                    HorizontalOptions="Start"
                                    Source="pinAqua.png"
                                    VerticalOptions="Center"
                                    WidthRequest="16" />

                                <StackLayout
                                    HorizontalOptions="Start"
                                    Spacing="5"
                                    VerticalOptions="Start">
                                    <Label
                                        Margin="0,0,0,0"
                                        FontSize="12"
                                        HorizontalOptions="Start"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.MayBeNeeded}"
                                        TextColor="{x:StaticResource greyTextColor}"
                                        VerticalOptions="Start" />
                                    <Label
                                        Margin="0,0,0,0"
                                        FontSize="12"
                                        HorizontalOptions="Start"
                                        Text="{Binding Source={x:Reference this}, Path=Exercise.MayBeNeeded}"
                                        TextColor="{x:StaticResource blueColor}"
                                        VerticalOptions="Start" />
                                </StackLayout>

                            </StackLayout>

                        </Grid>


                        <!--  SEPARATOR LINE  -->
                        <BoxView
                            Margin="0,10,0,0"
                            BackgroundColor="Black"
                            HeightRequest="1"
                            HorizontalOptions="Fill"
                            Opacity="0.2"
                            VerticalOptions="Start" />


                        <StackLayout
                            x:Name="ContentStack"
                            HorizontalOptions="Fill"
                            Opacity="0.001">

                            <!--  TEXT HTML  -->
                            <custom:AlfaHtmlLabel
                                x:Name="descLabel"
                                Margin="20,20,20,0" />


                            <Frame
                                Margin="20,20,20,0"
                                Padding="0"
                                BackgroundColor="Transparent"
                                CornerRadius="20"
                                HasShadow="False"
                                HeightRequest="240"
                                IsClippedToBounds="True"
                                VerticalOptions="Start">
                                <Image
                                    x:Name="img"
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="Fill" />
                            </Frame>

                            <custom:AlfaHtmlLabel
                                x:Name="desc2Label"
                                Margin="20,20,20,0" />

                            <!--REFERENCE LINK-->
                            <draw:Canvas
                                Gestures="Lock"
                                x:Name="LabelExternalLink"
                                HorizontalOptions="Start"
                                IsVisible="False">
                                <draw:SkiaLayout HorizontalOptions="Fill">

                                    <draw:SkiaLabel
                                        Padding="24,8,24,8"
                                        draw:AddGestures.CommandTapped="{Binding Source={x:Reference this}, Path=CommandOpenReferenceLink}"
                                        BackgroundColor="White"
                                        FontFamily="FontTextMedium"
                                        FontSize="15"
                                        HorizontalOptions="Start"
                                        TextColor="{x:StaticResource blueColor}">
                                        <draw:SkiaLabel.Spans>

                                            <draw:TextSpan
                                                x:Name="ReferenceLinkSpan"
                                                Text="Источник" />

                                            <draw:TextSpan Text=" " />

                                            <draw:SvgSpan
                                                Width="17"
                                                Height="17"
                                                Source="Resources/Images/linkout.svg"
                                                TintColor="{x:StaticResource blueColor}"
                                                VerticalAlignement="Center" />

                                        </draw:SkiaLabel.Spans>

                                    </draw:SkiaLabel>

                                </draw:SkiaLayout>
                            </draw:Canvas>

                            <Label
                                Margin="20,30,0,0"
                                FontAttributes="Bold"
                                FontSize="22"
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.WhatAreYouFeelingNow}"
                                TextColor="{x:StaticResource greyTextColor}" />


                            <Frame
                                Margin="20,20,20,0"
                                Padding="0"
                                BackgroundColor="#E6F1F9"
                                CornerRadius="20"
                                HasShadow="False"
                                HeightRequest="400"
                                VerticalOptions="Start">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="1*" />
                                        <ColumnDefinition Width="1*" />
                                        <ColumnDefinition Width="1*" />
                                    </Grid.ColumnDefinitions>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="1*" />
                                        <RowDefinition Height="1*" />
                                        <RowDefinition Height="1*" />
                                    </Grid.RowDefinitions>


                                    <other:ExerciseEmojiRadioButton
                                        x:Name="emojiHappiness"
                                        Grid.Row="0"
                                        Grid.Column="0"
                                        EmojiType="Happiness"
                                        ImgPath="mood1.png"
                                        Tapped="onEmojiTapped"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Emotions.Happiness}" />

                                    <other:ExerciseEmojiRadioButton
                                        x:Name="emojiTranquility"
                                        Grid.Row="0"
                                        Grid.Column="1"
                                        EmojiType="Tranquility"
                                        ImgPath="mood2.png"
                                        Tapped="onEmojiTapped"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Emotions.Tranquility}" />

                                    <other:ExerciseEmojiRadioButton
                                        x:Name="emojiConfidence"
                                        Grid.Row="0"
                                        Grid.Column="2"
                                        EmojiType="Confidence"
                                        ImgPath="mood9.png"
                                        Tapped="onEmojiTapped"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Emotions.Confidence}" />




                                    <other:ExerciseEmojiRadioButton
                                        x:Name="emojiAnxienty"
                                        Grid.Row="1"
                                        Grid.Column="0"
                                        EmojiType="Anxienty"
                                        ImgPath="mood4.png"
                                        Tapped="onEmojiTapped"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Emotions.Anxienty}" />

                                    <other:ExerciseEmojiRadioButton
                                        x:Name="emojiSadness"
                                        Grid.Row="1"
                                        Grid.Column="1"
                                        EmojiType="Sadness"
                                        ImgPath="mood3.png"
                                        Tapped="onEmojiTapped"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Emotions.Sadness}" />

                                    <other:ExerciseEmojiRadioButton
                                        x:Name="emojiPanic"
                                        Grid.Row="1"
                                        Grid.Column="2"
                                        EmojiType="Panic"
                                        ImgPath="mood7.png"
                                        Tapped="onEmojiTapped"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Emotions.Panic}" />





                                    <other:ExerciseEmojiRadioButton
                                        x:Name="emojiFear"
                                        Grid.Row="2"
                                        Grid.Column="0"
                                        EmojiType="Fear"
                                        ImgPath="mood5.png"
                                        Tapped="onEmojiTapped"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Emotions.Fear}" />

                                    <other:ExerciseEmojiRadioButton
                                        x:Name="emojiAnnoyance"
                                        Grid.Row="2"
                                        Grid.Column="1"
                                        EmojiType="Annoyance"
                                        ImgPath="mood6.png"
                                        Tapped="onEmojiTapped"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Emotions.Annoyance}" />

                                    <other:ExerciseEmojiRadioButton
                                        x:Name="emojiAnger"
                                        Grid.Row="2"
                                        Grid.Column="2"
                                        EmojiType="Anger"
                                        ImgPath="mood8.png"
                                        Tapped="onEmojiTapped"
                                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Emotions.Anger}" />


                                </Grid>
                            </Frame>



                            <!--  SEPARATOR  -->
                            <BoxView
                                Margin="0,40,0,0"
                                BackgroundColor="Black"
                                HeightRequest="1"
                                HorizontalOptions="Fill"
                                Opacity="0.2"
                                VerticalOptions="Start" />


                            <!--  FOOTER  -->
                            <Grid
                                x:Name="nextItemLayout"
                                Margin="20,0,20,0"
                                HeightRequest="200"
                                VerticalOptions="Start">



                            </Grid>

                        </StackLayout>


                    </StackLayout>
                </ScrollView>
            </Grid>



        </Grid>
    </ContentPage.Content>
</ContentPage>