### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ThemeBindingExtension
  commentId: T:DrawnUi.Draw.ThemeBindingExtension
  id: ThemeBindingExtension
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ThemeBindingExtension.Dark
  - DrawnUi.Draw.ThemeBindingExtension.Default
  - DrawnUi.Draw.ThemeBindingExtension.GetCurrentThemeValue
  - DrawnUi.Draw.ThemeBindingExtension.Light
  - DrawnUi.Draw.ThemeBindingExtension.ProvideValue(System.IServiceProvider)
  langs:
  - csharp
  - vb
  name: ThemeBindingExtension
  nameWithType: ThemeBindingExtension
  fullName: DrawnUi.Draw.ThemeBindingExtension
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ThemeBindingExtension
    path: ../src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
    startLine: 54
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: >-
    Theme binding extension for DrawnUI controls

    Supports MAUI, Blazor, and other platforms through IThemeProvider abstraction
  example: []
  syntax:
    content: >-
      [ContentProperty("Default")]

      public class ThemeBindingExtension : IMarkupExtension<object>, IMarkupExtension
    content.vb: >-
      <ContentProperty("Default")>

      Public Class ThemeBindingExtension Implements IMarkupExtension(Of Object), IMarkupExtension
  inheritance:
  - System.Object
  implements:
  - Microsoft.Maui.Controls.Xaml.IMarkupExtension{System.Object}
  - Microsoft.Maui.Controls.Xaml.IMarkupExtension
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  attributes:
  - type: Microsoft.Maui.Controls.ContentPropertyAttribute
    ctor: Microsoft.Maui.Controls.ContentPropertyAttribute.#ctor(System.String)
    arguments:
    - type: System.String
      value: Default
- uid: DrawnUi.Draw.ThemeBindingExtension.Light
  commentId: P:DrawnUi.Draw.ThemeBindingExtension.Light
  id: Light
  parent: DrawnUi.Draw.ThemeBindingExtension
  langs:
  - csharp
  - vb
  name: Light
  nameWithType: ThemeBindingExtension.Light
  fullName: DrawnUi.Draw.ThemeBindingExtension.Light
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Light
    path: ../src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
    startLine: 57
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public object Light { get; set; }
    parameters: []
    return:
      type: System.Object
    content.vb: Public Property Light As Object
  overload: DrawnUi.Draw.ThemeBindingExtension.Light*
- uid: DrawnUi.Draw.ThemeBindingExtension.Dark
  commentId: P:DrawnUi.Draw.ThemeBindingExtension.Dark
  id: Dark
  parent: DrawnUi.Draw.ThemeBindingExtension
  langs:
  - csharp
  - vb
  name: Dark
  nameWithType: ThemeBindingExtension.Dark
  fullName: DrawnUi.Draw.ThemeBindingExtension.Dark
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Dark
    path: ../src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
    startLine: 58
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public object Dark { get; set; }
    parameters: []
    return:
      type: System.Object
    content.vb: Public Property Dark As Object
  overload: DrawnUi.Draw.ThemeBindingExtension.Dark*
- uid: DrawnUi.Draw.ThemeBindingExtension.Default
  commentId: P:DrawnUi.Draw.ThemeBindingExtension.Default
  id: Default
  parent: DrawnUi.Draw.ThemeBindingExtension
  langs:
  - csharp
  - vb
  name: Default
  nameWithType: ThemeBindingExtension.Default
  fullName: DrawnUi.Draw.ThemeBindingExtension.Default
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Default
    path: ../src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
    startLine: 59
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public object Default { get; set; }
    parameters: []
    return:
      type: System.Object
    content.vb: Public Property [Default] As Object
  overload: DrawnUi.Draw.ThemeBindingExtension.Default*
- uid: DrawnUi.Draw.ThemeBindingExtension.ProvideValue(System.IServiceProvider)
  commentId: M:DrawnUi.Draw.ThemeBindingExtension.ProvideValue(System.IServiceProvider)
  id: ProvideValue(System.IServiceProvider)
  parent: DrawnUi.Draw.ThemeBindingExtension
  langs:
  - csharp
  - vb
  name: ProvideValue(IServiceProvider)
  nameWithType: ThemeBindingExtension.ProvideValue(IServiceProvider)
  fullName: DrawnUi.Draw.ThemeBindingExtension.ProvideValue(System.IServiceProvider)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ProvideValue
    path: ../src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
    startLine: 61
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: public object ProvideValue(IServiceProvider serviceProvider)
    parameters:
    - id: serviceProvider
      type: System.IServiceProvider
    return:
      type: System.Object
    content.vb: Public Function ProvideValue(serviceProvider As IServiceProvider) As Object
  overload: DrawnUi.Draw.ThemeBindingExtension.ProvideValue*
  implements:
  - Microsoft.Maui.Controls.Xaml.IMarkupExtension{System.Object}.ProvideValue(System.IServiceProvider)
- uid: DrawnUi.Draw.ThemeBindingExtension.GetCurrentThemeValue
  commentId: M:DrawnUi.Draw.ThemeBindingExtension.GetCurrentThemeValue
  id: GetCurrentThemeValue
  parent: DrawnUi.Draw.ThemeBindingExtension
  langs:
  - csharp
  - vb
  name: GetCurrentThemeValue()
  nameWithType: ThemeBindingExtension.GetCurrentThemeValue()
  fullName: DrawnUi.Draw.ThemeBindingExtension.GetCurrentThemeValue()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetCurrentThemeValue
    path: ../src/Maui/DrawnUi/Internals/Extensions/ThemeBindingExtension.cs
    startLine: 95
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public object GetCurrentThemeValue()
    return:
      type: System.Object
    content.vb: Public Function GetCurrentThemeValue() As Object
  overload: DrawnUi.Draw.ThemeBindingExtension.GetCurrentThemeValue*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension{System.Object}
  commentId: T:Microsoft.Maui.Controls.Xaml.IMarkupExtension{System.Object}
  parent: Microsoft.Maui.Controls.Xaml
  definition: Microsoft.Maui.Controls.Xaml.IMarkupExtension`1
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1
  name: IMarkupExtension<object>
  nameWithType: IMarkupExtension<object>
  fullName: Microsoft.Maui.Controls.Xaml.IMarkupExtension<object>
  nameWithType.vb: IMarkupExtension(Of Object)
  fullName.vb: Microsoft.Maui.Controls.Xaml.IMarkupExtension(Of Object)
  name.vb: IMarkupExtension(Of Object)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension`1
    name: IMarkupExtension
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1
  - name: <
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: '>'
  spec.vb:
  - uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension`1
    name: IMarkupExtension
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension
  commentId: T:Microsoft.Maui.Controls.Xaml.IMarkupExtension
  parent: Microsoft.Maui.Controls.Xaml
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension
  name: IMarkupExtension
  nameWithType: IMarkupExtension
  fullName: Microsoft.Maui.Controls.Xaml.IMarkupExtension
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension`1
  commentId: T:Microsoft.Maui.Controls.Xaml.IMarkupExtension`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1
  name: IMarkupExtension<T>
  nameWithType: IMarkupExtension<T>
  fullName: Microsoft.Maui.Controls.Xaml.IMarkupExtension<T>
  nameWithType.vb: IMarkupExtension(Of T)
  fullName.vb: Microsoft.Maui.Controls.Xaml.IMarkupExtension(Of T)
  name.vb: IMarkupExtension(Of T)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension`1
    name: IMarkupExtension
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension`1
    name: IMarkupExtension
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: Microsoft.Maui.Controls.Xaml
  commentId: N:Microsoft.Maui.Controls.Xaml
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Controls.Xaml
  nameWithType: Microsoft.Maui.Controls.Xaml
  fullName: Microsoft.Maui.Controls.Xaml
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  - name: .
  - uid: Microsoft.Maui.Controls.Xaml
    name: Xaml
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Controls
    name: Controls
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls
  - name: .
  - uid: Microsoft.Maui.Controls.Xaml
    name: Xaml
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ThemeBindingExtension.Light*
  commentId: Overload:DrawnUi.Draw.ThemeBindingExtension.Light
  href: DrawnUi.Draw.ThemeBindingExtension.html#DrawnUi_Draw_ThemeBindingExtension_Light
  name: Light
  nameWithType: ThemeBindingExtension.Light
  fullName: DrawnUi.Draw.ThemeBindingExtension.Light
- uid: DrawnUi.Draw.ThemeBindingExtension.Dark*
  commentId: Overload:DrawnUi.Draw.ThemeBindingExtension.Dark
  href: DrawnUi.Draw.ThemeBindingExtension.html#DrawnUi_Draw_ThemeBindingExtension_Dark
  name: Dark
  nameWithType: ThemeBindingExtension.Dark
  fullName: DrawnUi.Draw.ThemeBindingExtension.Dark
- uid: DrawnUi.Draw.ThemeBindingExtension.Default*
  commentId: Overload:DrawnUi.Draw.ThemeBindingExtension.Default
  href: DrawnUi.Draw.ThemeBindingExtension.html#DrawnUi_Draw_ThemeBindingExtension_Default
  name: Default
  nameWithType: ThemeBindingExtension.Default
  fullName: DrawnUi.Draw.ThemeBindingExtension.Default
- uid: DrawnUi.Draw.ThemeBindingExtension.ProvideValue*
  commentId: Overload:DrawnUi.Draw.ThemeBindingExtension.ProvideValue
  href: DrawnUi.Draw.ThemeBindingExtension.html#DrawnUi_Draw_ThemeBindingExtension_ProvideValue_System_IServiceProvider_
  name: ProvideValue
  nameWithType: ThemeBindingExtension.ProvideValue
  fullName: DrawnUi.Draw.ThemeBindingExtension.ProvideValue
- uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension{System.Object}.ProvideValue(System.IServiceProvider)
  commentId: M:Microsoft.Maui.Controls.Xaml.IMarkupExtension{System.Object}.ProvideValue(System.IServiceProvider)
  parent: Microsoft.Maui.Controls.Xaml.IMarkupExtension{System.Object}
  definition: Microsoft.Maui.Controls.Xaml.IMarkupExtension`1.ProvideValue(System.IServiceProvider)
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1.providevalue
  name: ProvideValue(IServiceProvider)
  nameWithType: IMarkupExtension<object>.ProvideValue(IServiceProvider)
  fullName: Microsoft.Maui.Controls.Xaml.IMarkupExtension<object>.ProvideValue(System.IServiceProvider)
  nameWithType.vb: IMarkupExtension(Of Object).ProvideValue(IServiceProvider)
  fullName.vb: Microsoft.Maui.Controls.Xaml.IMarkupExtension(Of Object).ProvideValue(System.IServiceProvider)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension{System.Object}.ProvideValue(System.IServiceProvider)
    name: ProvideValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1.providevalue
  - name: (
  - uid: System.IServiceProvider
    name: IServiceProvider
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iserviceprovider
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension{System.Object}.ProvideValue(System.IServiceProvider)
    name: ProvideValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1.providevalue
  - name: (
  - uid: System.IServiceProvider
    name: IServiceProvider
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iserviceprovider
  - name: )
- uid: System.IServiceProvider
  commentId: T:System.IServiceProvider
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iserviceprovider
  name: IServiceProvider
  nameWithType: IServiceProvider
  fullName: System.IServiceProvider
- uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension`1.ProvideValue(System.IServiceProvider)
  commentId: M:Microsoft.Maui.Controls.Xaml.IMarkupExtension`1.ProvideValue(System.IServiceProvider)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1.providevalue
  name: ProvideValue(IServiceProvider)
  nameWithType: IMarkupExtension<T>.ProvideValue(IServiceProvider)
  fullName: Microsoft.Maui.Controls.Xaml.IMarkupExtension<T>.ProvideValue(System.IServiceProvider)
  nameWithType.vb: IMarkupExtension(Of T).ProvideValue(IServiceProvider)
  fullName.vb: Microsoft.Maui.Controls.Xaml.IMarkupExtension(Of T).ProvideValue(System.IServiceProvider)
  spec.csharp:
  - uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension`1.ProvideValue(System.IServiceProvider)
    name: ProvideValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1.providevalue
  - name: (
  - uid: System.IServiceProvider
    name: IServiceProvider
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iserviceprovider
  - name: )
  spec.vb:
  - uid: Microsoft.Maui.Controls.Xaml.IMarkupExtension`1.ProvideValue(System.IServiceProvider)
    name: ProvideValue
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.controls.xaml.imarkupextension-1.providevalue
  - name: (
  - uid: System.IServiceProvider
    name: IServiceProvider
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iserviceprovider
  - name: )
- uid: DrawnUi.Draw.ThemeBindingExtension.GetCurrentThemeValue*
  commentId: Overload:DrawnUi.Draw.ThemeBindingExtension.GetCurrentThemeValue
  href: DrawnUi.Draw.ThemeBindingExtension.html#DrawnUi_Draw_ThemeBindingExtension_GetCurrentThemeValue
  name: GetCurrentThemeValue
  nameWithType: ThemeBindingExtension.GetCurrentThemeValue
  fullName: DrawnUi.Draw.ThemeBindingExtension.GetCurrentThemeValue
