﻿global using DrawnUi.Draw;
global using Triggero.MauiMobileApp;
global using Triggero.MauiMobileApp.Enums;
global using Triggero.MauiMobileApp.Extensions.Helpers;
global using Triggero.MauiMobileApp.Views;
global using Triggero.MauiPort.Views.Pages;
using CommunityToolkit.Maui;
using Microsoft.Extensions.Logging;
using Microsoft.Maui.LifecycleEvents;
using Plugin.Maui.Audio;
using Sharpnado.MaterialFrame;
using Syncfusion.Maui.Buttons;
using Syncfusion.Maui.Core.Hosting;
using Triggero.MauiMobileApp.Services;
using Triggero.MauiMobileApp.Helpers;

namespace Triggero.MauiMobileApp
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {


            var test = new SfSwitch();
            test.SwitchSettings = new SwitchSettings()
            {

            };

            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .UseMauiCommunityToolkit()
                .UseDrawnUi(new()
                {
                    MobileIsFullscreen = true,
                    DesktopWindow = new()
                    {
                        Height = 812,
                        Width = 375,
                    }
                })

                //For some yet to be discovered reasons, AcrylicBlur value doesn't work in a dynamic context on iOS.
                .UseSharpnadoMaterialFrame(loggerEnable: false)

                .ConfigureSyncfusionCore()

                .AddAudio()

                .RegisterFirebaseServices()

                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "FontText");
                    fonts.AddFont("OpenSans-Bold.ttf", "FontTextBold");
                    fonts.AddFont("OpenSans-BoldItalic.ttf", "FontTextBoldItalic");
                    fonts.AddFont("OpenSans-ExtraBold.ttf", "FontTextExtraBold");
                    fonts.AddFont("OpenSans-ExtraBoldItalic.ttf", "FontTextExtraBoldItalic");
                    fonts.AddFont("OpenSans-Italic.ttf", "FontTextItalic");
                    fonts.AddFont("OpenSans-Light.ttf", "FontTextLight");
                    fonts.AddFont("OpenSans-LightItalic.ttf", "FontTextLightItalic");
                    fonts.AddFont("OpenSans-Medium.ttf", "FontTextMedium");
                    fonts.AddFont("OpenSans-MediumItalic.ttf", "FontTextMediumItalic");
                    fonts.AddFont("OpenSans-SemiBold.ttf", "FontTextSemiBold");
                    fonts.AddFont("OpenSans-SemiBoldItalic.ttf", "FontTextSemiBoldItalic");
                });

            SkiaFontManager.ThrowIfFailedToCreateFont = false;
            SkiaImageManager.ReuseBitmaps = true;

            builder.Services.AddSingleton<IInAppMessager, InAppMessager>();
            builder.Services.AddSingleton<IAudioService, AudioService>();

            // Initialize mobile-optimized API wrapper
            TriggeroMobileAPI.Initialize();

            // Register platform services
            //builder.AddPlatformServices();

#if DEBUG
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }

        private static MauiAppBuilder RegisterFirebaseServices(this MauiAppBuilder builder)
        {
            builder.ConfigureLifecycleEvents(events => {
#if IOS
                events.AddiOS(iOS => iOS.WillFinishLaunching((_, __) => {
                    Plugin.Firebase.Core.Platforms.iOS.CrossFirebase.Initialize();
                    Plugin.Firebase.CloudMessaging.FirebaseCloudMessagingImplementation.Initialize();
                    return false;
                }));
#elif ANDROID
            events.AddAndroid(android => android.OnCreate((activity, _) =>
                Plugin.Firebase.Core.Platforms.Android.CrossFirebase.Initialize(activity)));
#endif
            });

            //not using Auth
            //builder.Services.AddSingleton(_ => CrossFirebaseAuth.Current);

            return builder;
        }
    }
}
