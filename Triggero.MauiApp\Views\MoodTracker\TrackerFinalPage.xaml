﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.MoodTracker.TrackerFinalPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:moodtracker="clr-namespace:Triggero.Controls.MoodTracker"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    x:Name="ThisPage"
    Background="White"
    BackgroundColor="White">
    <ContentPage.Content>
        <Grid>




            <ScrollView
                Margin="0,0,0,0"
                HorizontalOptions="Fill"
                VerticalOptions="Fill">


                <Grid>


                    <Image
                        Margin="0,-200,0,0"
                        Aspect="Fill"
                        HeightRequest="1500"
                        Source="lightBlueGradientBg2" />


                    <StackLayout Margin="0,90,0,0">

                        <StackLayout
                            Margin="20,0,20,0"
                            HorizontalOptions="Fill"
                            Spacing="8"
                            VerticalOptions="End">
                            <Label
                                FontAttributes="Bold"
                                FontFamily="FontTextLight"
                                FontSize="17"
                                HorizontalOptions="Center"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerFinal.Header}"
                                TextColor="{x:StaticResource greyTextColor}"
                                VerticalOptions="Center" />
                            <Label
                                x:Name="dateLabel"
                                FontFamily="FontTextLight"
                                FontSize="14"
                                HorizontalOptions="Center"
                                Text=""
                                TextColor="{x:StaticResource ColorTextGray}"
                                VerticalOptions="Center" />
                        </StackLayout>



                        <StackLayout
                            Margin="20,30,20,0"
                            Orientation="Horizontal"
                            Spacing="8">
                            <Label
                                x:Name="LabelNote"
                                FontAttributes="Bold"
                                FontFamily="FontTextLight"
                                FontSize="15"
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerFinal.AddNote}"
                                TextColor="{x:StaticResource greyTextColor}"
                                VerticalOptions="Center" />
                            <Image
                                x:Name="IconEdit"
                                HeightRequest="12"
                                HorizontalOptions="Start"
                                Opacity="0.5"
                                Source="editName.png"
                                VerticalOptions="Center"
                                WidthRequest="12" />
                        </StackLayout>


                        <!--<sh:Shadows
                        Margin="20,6,20,0"
                        HeightRequest="50"
                        VerticalOptions="Start"
                        Shades="{sh:SingleShade Offset='2, 2',
                                                BlurRadius=12,
                                                Opacity=0.06,
                                                Color=#27527A}">-->

                        <!--todo-->
                        <!--BorderThickness="0"
                        -->

                        <Editor
                            Placeholder="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerFinal.NotePlaceholder}"
                            x:Name="Editor"
                            Margin="20,6,20,0"
                            BackgroundColor="White"
                            HeightRequest="50"
                            HorizontalOptions="Fill"
                            Style="{x:StaticResource grayTextEditor}"
                            Text="{Binding Source={x:Reference ThisPage}, Path=Tracker.Note.Text, Mode=TwoWay}"
                            VerticalOptions="Start" />
                        <!--</sh:Shadows>-->


                        <StackLayout
                            Margin="20,30,20,0"
                            HeightRequest="130">
                            <Label
                                FontAttributes="Bold"
                                FontFamily="FontTextLight"
                                FontSize="17"
                                HorizontalOptions="Start"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerFinal.YourMood}"
                                TextColor="{x:StaticResource greyTextColor}"
                                VerticalOptions="Center" />

                            <Grid
                                Margin="-15,0,-15,0"
                                HeightRequest="100"
                                VerticalOptions="Start">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="1*" />
                                    <ColumnDefinition Width="1*" />
                                </Grid.ColumnDefinitions>

                                <Image
                                    Grid.ColumnSpan="2"
                                    Aspect="Fill"
                                    HorizontalOptions="Fill"
                                    Source="aquaOpaqueContainer.png"
                                    VerticalOptions="Fill" />


                                <Grid Grid.Column="0">
                                    <Label
                                        x:Name="moodTitleLabel"
                                        Margin="0,0,10,0"
                                        FontFamily="FontTextLight"
                                        FontSize="17"
                                        HorizontalOptions="End"
                                        TextColor="{x:StaticResource greyTextColor}"
                                        VerticalOptions="Center" />
                                </Grid>

                                <Grid Grid.Column="1">

                                    <Image
                                        x:Name="moodImg"
                                        Margin="10,0,0,0"
                                        HeightRequest="60"
                                        HorizontalOptions="Start"
                                        VerticalOptions="Center"
                                        WidthRequest="60" />
                                </Grid>

                            </Grid>


                        </StackLayout>

                        <Label
                            Margin="20,50,20,0"
                            FontAttributes="Bold"
                            FontFamily="FontTextLight"
                            FontSize="17"
                            HorizontalOptions="Start"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerFinal.YourFeelings}"
                            TextColor="{x:StaticResource greyTextColor}"
                            VerticalOptions="Center" />


                        <!--<sh:Shadows
                        Grid.Row="1"
                        Margin="20,20,20,20"
                        Shades="{sh:SingleShade Offset='2, 2',
                                                BlurRadius=12,
                                                Opacity=0.06,
                                                Color=#27527A}">-->
                        <moodtracker:TrackerFeelingsView
                            x:Name="feelingsView"
                            Grid.Row="1"
                            Margin="20,20,20,20"
                            HeightRequest="710"
                            InputTransparent="True"
                            Tracker="{Binding Source={x:Reference ThisPage}, Path=Tracker}"
                            VerticalOptions="Start" />
                        <!--</sh:Shadows>-->


                        <Label
                            Margin="20,0,20,0"
                            FontAttributes="Bold"
                            FontFamily="FontTextLight"
                            FontSize="17"
                            HorizontalOptions="Start"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerFinal.WhatAffected}"
                            TextColor="{x:StaticResource greyTextColor}"
                            VerticalOptions="Center" />

                        <StackLayout
                            x:Name="whatAffectedLayout"
                            Margin="20,12,20,150"
                            Spacing="12">



                        </StackLayout>

                        <Grid
                            Margin="0,0,0,-20"
                            Padding="20,0,20,0"
                            BackgroundColor="White"
                            HeightRequest="122"
                            VerticalOptions="Start">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="56" />
                                <ColumnDefinition Width="*" />
                            </Grid.ColumnDefinitions>

                            <!--<ImageButton
                            Grid.Column="0"
                            Margin="0,12,0,0"
                            Command="{Binding Source={x:Reference ThisPage},Path=GoBack}"
                            Source="buttonBackBordered.png"
                            WidthRequest="56"
                            HeightRequest="56"
                            HorizontalOptions="Start"
                            VerticalOptions="Center"
                            BackgroundColor="Transparent"
                            CornerRadius="0"/>-->

                            <pancakeview:PancakeView
                                Grid.Column="0"
                                Margin="0,12,0,0"
                                Padding="0"
                                BackgroundColor="Transparent"
                                StrokeShape="RoundRectangle 15"
                                HeightRequest="56"
                                HorizontalOptions="Start"
                                VerticalOptions="Start"
                                StrokeThickness="1"
                                Stroke="#4D4D4D"
                                WidthRequest="56">
                                <pancakeview:PancakeView.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference ThisPage}, Path=GoBack}" />
                                </pancakeview:PancakeView.GestureRecognizers>
                                <Grid>
                                    <Image
                                        HeightRequest="12"
                                        HorizontalOptions="Center"
                                        Source="arrowBack.png"
                                        VerticalOptions="Center"
                                        WidthRequest="7" />
                                </Grid>
                            </pancakeview:PancakeView>

                            <Button
                                x:Name="saveBtn"
                                Grid.Column="1"
                                Margin="0,12,0,0"
                                Command="{Binding Source={x:Reference ThisPage}, Path=SaveEntry}"
                                HeightRequest="56"
                                Style="{x:StaticResource yellow_btn}"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerFinal.SaveToDiary}"
                                VerticalOptions="Start" />

                        </Grid>

                    </StackLayout>
                </Grid>


            </ScrollView>
        </Grid>
    </ContentPage.Content>
</ContentPage>