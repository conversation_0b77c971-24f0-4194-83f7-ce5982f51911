using MobileAPIWrapper;
using Triggero.MauiMobileApp.Helpers;

namespace Triggero.MauiMobileApp.Helpers
{
    /// <summary>
    /// Mobile-optimized wrapper for API calls that uses MobileApiRequestHelper instead of RestSharp
    /// This provides the same interface as TriggeroMobileAPI but with better mobile performance
    /// </summary>
    public static class MobileTriggeroAPI
    {
        /// <summary>
        /// Initialize the mobile API wrapper with the mobile request helper
        /// Call this during app startup to ensure proper configuration
        /// </summary>
        public static void Initialize()
        {
            // The MobileApiRequestHelper is already initialized in its static constructor
            // This method is here for future configuration if needed
        }

        /// <summary>
        /// Set the bearer token for authentication
        /// </summary>
        public static void SetBearerToken(string token)
        {
            MobileApiRequestHelper.SetBearerToken(token);
        }

        /// <summary>
        /// Add default headers for all requests
        /// </summary>
        public static void AddDefaultHeaders(IEnumerable<KeyValuePair<string, string>> headers)
        {
            MobileApiRequestHelper.AddDefaultHeaders(headers);
        }

        /// <summary>
        /// Clear all default headers
        /// </summary>
        public static void ClearDefaultHeaders()
        {
            MobileApiRequestHelper.ClearDefaultHeaders();
        }

        /// <summary>
        /// Get the base URL for API calls
        /// </summary>
        public static string GetBaseUrl()
        {
            return TriggeroMobileAPI.GetBaseUrl();
        }

        /// <summary>
        /// Add base URL to a path
        /// </summary>
        public static string AddBaseUrl(string path)
        {
            return TriggeroMobileAPI.AddBaseUrl(path);
        }

        /// <summary>
        /// Event handler for unauthorized responses
        /// </summary>
        public static EventHandler OnUnauthorized
        {
            get => MobileApiRequestHelper.OnUnauthorized;
            set => MobileApiRequestHelper.OnUnauthorized = value;
        }

        /// <summary>
        /// Access to the original API methods - these will still use RestSharp
        /// For mobile optimization, consider creating mobile-specific versions of frequently used methods
        /// </summary>
        public static class Original
        {
            public static MobileAPIWrapper.Methods.AccountMethods Account => TriggeroMobileAPI.Account;
            public static MobileAPIWrapper.MethodGroupings.GeneralMethods GeneralMethods => TriggeroMobileAPI.GeneralMethods;
            public static MobileAPIWrapper.MethodGroupings.LibraryMethods LibraryMethods => TriggeroMobileAPI.LibraryMethods;
            public static MobileAPIWrapper.MethodGroupings.MessengersMethods MessengersMethods => TriggeroMobileAPI.MessengersMethods;
            public static MobileAPIWrapper.Methods.PaymentMethods Payment => TriggeroMobileAPI.Payment;
            public static MobileAPIWrapper.Methods.CommonMethods Common => TriggeroMobileAPI.Common;
            public static MobileAPIWrapper.Methods.TestsMethods TestsMethods => TriggeroMobileAPI.TestsMethods;
        }

        /// <summary>
        /// Dispose resources when application shuts down
        /// </summary>
        public static void Dispose()
        {
            MobileApiRequestHelper.Dispose();
        }
    }
}
