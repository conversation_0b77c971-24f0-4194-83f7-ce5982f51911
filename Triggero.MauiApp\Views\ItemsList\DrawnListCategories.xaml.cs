﻿using AppoMobi.Specials;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.ViewModels;



namespace Triggero.MauiMobileApp.Views.Drawn
{
    public partial class DrawnListCategories
    {
        private readonly BaseCategoriesViewModel _vm;

        public DrawnListCategories(BaseCategoriesViewModel vm, int msDelayInitialize = 1)
        {
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            BackgroundColor = Colors.White;

            _vm = vm;

            InitializeComponent();

            BindingContext = _vm;

            Tasks.StartDelayed(TimeSpan.FromMilliseconds(50), async () =>
            {
                await _vm.InitializeAsyc();
            });
        }
    }
}