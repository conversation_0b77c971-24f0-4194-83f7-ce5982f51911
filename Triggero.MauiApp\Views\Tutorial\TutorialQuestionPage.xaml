﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.Pages.Tests.TutorialQuestionPage"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid BackgroundColor="White">


            <Image
                Aspect="Fill"
                VerticalOptions="Fill"
                HorizontalOptions="Fill"
                Source="lightBlueGradientBg.png"/>

            <Label 
                x:Name="skipLabel"
                Margin="20,50,0,0"
                HorizontalOptions="Start"
                VerticalOptions="Start"
                TextColor="{x:StaticResource greyTextColor}"
                Opacity="0.5"
                FontSize="14"
                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Start.HelloSliderPage.Skip}"
                TextDecorations="Underline"
                TextTransform="None">
                <Label.GestureRecognizers>
                    <TapGestureRecognizer Command="{Binding Source={x:Reference this},Path=Skip}"/>
                </Label.GestureRecognizers>
            </Label>

            <Frame
                BackgroundColor="Transparent"
                VerticalOptions="Start"
                HorizontalOptions="End"
                Margin="0,80,16,0"
                BorderColor="{x:StaticResource greyColor}"
                WidthRequest="36"
                HeightRequest="22"
                CornerRadius="9"
                HasShadow="False"
                Padding="0">
                <Label 
                    x:Name="numberLabel"
                    TextColor="{x:StaticResource greyColor}"
                    FontSize="14"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    Text=""/>
            </Frame>

            <ScrollView 
                Margin="20,0,20,0"
                VerticalOptions="Center"
                VerticalScrollBarVisibility="Never">
                <StackLayout>
             

                    <StackLayout
                        Margin="0,25,0,0"
                        x:Name="testQuestionViewLayout">
                        
                    </StackLayout>

                    <Grid
                        Margin="0,30,0,0"
                        ColumnSpacing="16"
                        HeightRequest="56">

                        <Button 
                            Command="{Binding Source={x:Reference this},Path=GoNext}"
                            VerticalOptions="Fill"
                            HeightRequest="56"
                            Style="{x:StaticResource yellow_btn}"
                            Text="{Binding Source={x:Static mobile:App.This},Path=Interface.Tutorial.StartTutorialPage.GoNext}"/>

                    </Grid>

                </StackLayout>
            </ScrollView>
        </Grid>
    </ContentPage.Content>
</ContentPage>