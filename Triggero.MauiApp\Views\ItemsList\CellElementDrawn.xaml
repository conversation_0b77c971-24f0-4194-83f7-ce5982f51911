﻿<?xml version="1.0" encoding="UTF-8" ?>
<drawn:FastCellWithBanner
    x:Class="Triggero.Controls.Templates.CellElementDrawn"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Triggero.MauiMobileApp.Views.Drawn"
    xmlns:practices="clr-namespace:Triggero.Models.Practices;assembly=Triggero.Models"
    xmlns:tasksForToday="clr-namespace:Triggero.MauiMobileApp.Controls.Cards.TasksForToday;assembly=Triggero.MauiMobileApp"
    xmlns:tasksForToday1="clr-namespace:Triggero.MauiMobileApp.Controls.Cards.TasksForToday"
    x:Name="this"
    x:DataType="practices:IElementDetails"
    HeightRequest="120"
    HorizontalOptions="Fill"
    UseCache="ImageDoubleBuffered">

    <draw:SkiaLayout
        Padding="10"
        HorizontalOptions="Fill"
        UseCache="Operations"
        VerticalOptions="Fill">
        <draw:SkiaShape
            BackgroundColor="White"
            CornerRadius="0,16,16,0"
            HeightRequest="100"
            HorizontalOptions="Fill"
            VerticalOptions="Fill"
            ZIndex="-1">

            <draw:SkiaShape.Shadows>

                <draw:SkiaShadow
                    Blur="4"
                    Opacity="0.05"
                    X="0"
                    Y="1"
                    Color="#363B40" />

            </draw:SkiaShape.Shadows>

        </draw:SkiaShape>
    </draw:SkiaLayout>

    <draw:SkiaShape
        x:Name="MainFrame"
        Margin="10"
        CornerRadius="0,16,16,0"
        HeightRequest="100"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            Type="Grid"
            VerticalOptions="Fill">

            <draw:SkiaLayout.ColumnDefinitions>
                <ColumnDefinition Width="100" />
                <ColumnDefinition Width="*" />
                <ColumnDefinition Width="50" />
            </draw:SkiaLayout.ColumnDefinitions>

            <!--  BANNER  -->
            <draw:SkiaImage
                LoadSourceOnFirstDraw="True"
                x:Name="ImageBanner"
                Aspect="AspectCover"
                EraseChangedContent="True"
                HorizontalOptions="Fill"
                VerticalOptions="Fill" />

            <!--  PASSED  -->
            <draw:SkiaLayout
                IsVisible="False"
                x:Name="OverlayPassed"
                BackgroundColor="#596D92AF"
                HorizontalOptions="Fill"
                UseCache="Image"
                VerticalOptions="Fill">

                <draw:SkiaImage
                    HeightRequest="16"
                    HorizontalOptions="Center"
                    LoadSourceOnFirstDraw="True"
                    Source="whiteCheckMark.png"
                    VerticalOptions="Center"
                    WidthRequest="22" />

            </draw:SkiaLayout>

            <!--  DESCRIPTION  -->
            <draw:SkiaLayout
                Grid.Column="1"
                Margin="16,0,0,0"
                HorizontalOptions="Fill"
                Type="Column"
                UseCache="Operations"
                VerticalOptions="Center">


                <!--  ICON TYPE  -->
                <draw:SkiaLayout
                    HeightRequest="12"
                    HorizontalOptions="Start"
                    Tag="StackRow"
                    Type="Row">

                    <draw:SkiaImage
                        x:Name="ImageTypeIcon"
                        Aspect="AspectFit"
                        HeightRequest="12"
                        Opacity="0.5"
                        VerticalOptions="Center"
                        WidthRequest="12" />

                    <draw:SkiaLabel
                        x:Name="LabelTypeDescription"
                        FontSize="10"
                        TextColor="{x:StaticResource ColorTextGray}"
                        VerticalOptions="Center" />

                </draw:SkiaLayout>

                <!--  Text="{Binding Source={x:Static app:App.This}, Path=Interface.Library.Library.CompletingLowercase}"  -->

                <!--  TITLE  -->
                <draw:SkiaLabel
                    x:Name="LabelTitle"
                    FontAttributes="Bold"
                    FontFamily="FontTextSemiBold"
                    FontSize="13"
                    HorizontalOptions="Start"
                    MaxLines="2"
                    TextColor="{x:StaticResource greyTextColor}" />

                <!--  TIME ETC  -->
                <draw:SkiaLabel
                    x:Name="LabelDetails"
                    FontSize="10"
                    HorizontalOptions="Start"
                    MaxLines="1"
                    TextColor="{x:StaticResource ColorTextGray}" />

            </draw:SkiaLayout>

            <draw:SkiaHotspot
                AnimationTapped="Ripple"
                TouchEffectColor="{x:StaticResource ColorPrimary}"
                TransformView="{x:Reference MainFrame}"
                Grid.ColumnSpan="2"
                Tapped="SkiaHotspot_Tapped" />

            <!--  FAVORITE  -->
            <tasksForToday1:CheckBoxFavorite
                x:Name="favoriteRb"
                Grid.Column="2"
                HorizontalOptions="Fill"
                Toggled="favoriteRb_Toggled"
                UseCache="Image"
                VerticalOptions="Fill" />

        </draw:SkiaLayout>

    </draw:SkiaShape>

</drawn:FastCellWithBanner>