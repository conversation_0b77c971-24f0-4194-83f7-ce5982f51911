﻿using System.Linq;
using Triggero.Models.Tests;
using Triggero.MauiMobileApp.Views.Pages.Tests;

using Triggero.MauiMobileApp.Extensions.Helpers;

namespace Triggero.Controls.Templates
{

    public partial class TestCard : ContentView
    {
        public TestCard()
        {
            InitializeComponent();
        }
        public TestCard(Test test)
        {
            InitializeComponent();

            Model = test;
            titleLabel.Text = Model.GetLocalizedTitle(LanguageHelper.LangCode);

            if (Model.Id != 0)
            {
                favoriteRb.IsToggled = ApplicationState.UserFavorites.HasFavoriteTest(Model.Id);
                testPassedGrid.IsVisible = ApplicationState.Data.TestPassingResults.Any(o => o.TestId == Model.Id);
            }
            else
            {
                testPassedGrid.IsVisible = false;
            }

            img.Source = Constants.BuildContentUrl(Model.IconImgPath);
        }


        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if (BindingContext is Test item && Model != item)
            {
                Model = item;


                titleLabel.Text = Model.GetLocalizedTitle(LanguageHelper.LangCode);

                if (Model.Id != 0)
                {
                    favoriteRb.IsToggled = ApplicationState.UserFavorites.HasFavoriteTest(Model.Id);
                    testPassedGrid.IsVisible = ApplicationState.Data.TestPassingResults.Any(o => o.TestId == Model.Id);
                }

                img.Source = Constants.BuildContentUrl(Model.IconImgPath);
            }
        }


        private Test model;
        public Test Model
        {
            get { return model; }
            set { model = value; OnPropertyChanged(nameof(Model)); }
        }

        private async void toggleFavorite(object? sender, bool b)
        {
            MainThread.BeginInvokeOnMainThread(async () =>
            {
                favoriteRb.IsToggled = await ApplicationState.UserFavorites.ToggleTest(Model.Id);
            });
        }



        private void onTapped(object sender, EventArgs e)
        {
            if (Model is null) return;

            App.OpenPage(new TestPage(Model));
        }
    }
}