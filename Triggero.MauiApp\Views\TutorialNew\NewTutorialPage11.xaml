﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:parts="clr-namespace:Triggero.Controls.Parts"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage11"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="340"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="140"/>
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="3"
                Aspect="Fill"
                Source="tutorialBlur3.png"/>


            <Grid Grid.Row="0">

                <Grid
                    VerticalOptions="End"
                    HeightRequest="270">
                    
                    <Image
                        Aspect="Fill"
                        Margin="-2,0,0,0"
                        Source="tutorialChatBotContainer.png"/>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="1*"/>
                            <RowDefinition Height="1*"/>
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0"
                              Margin="20,0,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="50"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <Grid Grid.Column="0">
                                <Image
                                    WidthRequest="47"
                                    HeightRequest="47"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Center"
                                    Source="chatBotAvatar.png"/>
                            </Grid>

                            <Grid Grid.Column="1">
                                <StackLayout 
                                    Spacing="0"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    Margin="15,0,0,0">

                                    <Label 
                                        TextColor="{x:StaticResource greyTextColor}"
                                        FontSize="17"
                                        FontAttributes="Bold"
                                        Margin="0,0,0,0"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Start"
                                        Text="Triggero"/>
                                    <Label 
                                        TextColor="{x:StaticResource greyTextColor}"
                                        Opacity="0.5"
                                        FontSize="12"
                                        Margin="0,0,0,0"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Start"
                                        Text="Виртуальный психолог"/>

                                    <StackLayout
                                        Spacing="4"
                                        Margin="0,5,0,0"
                                        VerticalOptions="Center"
                                        HorizontalOptions="Start"
                                        Orientation="Horizontal">
                                        <Frame 
                                            WidthRequest="8"
                                            HeightRequest="8"
                                            CornerRadius="4"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Padding="0"
                                            HasShadow="False"
                                            Background="#34C759"/>

                                        <Label 
                                            TextColor="{x:StaticResource greyTextColor}"
                                            Opacity="0.5"
                                            FontSize="12"
                                            Margin="0,0,0,0"
                                            VerticalOptions="Center"
                                            HorizontalOptions="Start"
                                            Text="В сети"/>
                                    </StackLayout>

                                </StackLayout>
                            </Grid>

                        </Grid>


                        <Grid Grid.Row="1">

                            <StackLayout Margin="0,-10,0,0">
                                <Label 
                                    TextColor="{x:StaticResource greyTextColor}"
                                    Opacity="0.5"
                                    FontSize="14"
                                    Margin="0,0,0,0"
                                    FontAttributes="Bold"
                                    VerticalOptions="Start"
                                    HorizontalOptions="Center"
                                    Text="4 октября"/>

                                <Frame
                                    CornerRadius="12"
                                    HeightRequest="35"
                                    VerticalOptions="Start"
                                    HorizontalOptions="Start"
                                    Margin="20,7,0,0"
                                    BackgroundColor="#EEF5FB"
                                    HasShadow="False"
                                    Padding="0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="50"/>
                                        </Grid.ColumnDefinitions>

                                        <Grid Grid.Column="0">
                                            <Label 
                                                TextColor="{x:StaticResource greyTextColor}"
                                                FontSize="{x:OnPlatform Android=Default,iOS=16}"
                                                Margin="20,0,0,0"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Start"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage11.ChatMessage1Text}"/>
                                        </Grid>

                                        <Grid Grid.Column="1">
                                            <Label 
                                                TextColor="{x:StaticResource greyTextColor}"
                                                Opacity="0.5"
                                                FontSize="12"
                                                Margin="0,0,0,0"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                Text="8:38"/>
                                        </Grid>

                                    </Grid>
                                </Frame>

                                <Frame
                                    CornerRadius="12"
                                    HeightRequest="35"
                                    VerticalOptions="Start"
                                    HorizontalOptions="Start"
                                    Margin="20,0,0,0"
                                    BackgroundColor="#EEF5FB"
                                    HasShadow="False"
                                    Padding="0">
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="50"/>
                                        </Grid.ColumnDefinitions>

                                        <Grid Grid.Column="0">
                                            <Label 
                                                TextColor="{x:StaticResource greyTextColor}"
                                                FontSize="{x:OnPlatform Android=Default,iOS=16}"
                                                Margin="20,0,0,0"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Start"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage11.ChatMessage2Text}"/>
                                        </Grid>

                                        <Grid Grid.Column="1">
                                            <Label 
                                                TextColor="{x:StaticResource greyTextColor}"
                                                Opacity="0.5"
                                                FontSize="12"
                                                Margin="0,0,0,0"
                                                VerticalOptions="Center"
                                                HorizontalOptions="Center"
                                                Text="8:38"/>
                                        </Grid>

                                    </Grid>
                                </Frame>

                            </StackLayout>

                         

                        </Grid>

                    </Grid>

                </Grid>



            </Grid>

            <Grid
                Grid.Row="1">

                <StackLayout
                    Margin="20,0,20,0">

                    <Grid 
                    Margin="0,10,0,0"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill"
                    HeightRequest="100">

                        <Frame
                            BackgroundColor="White"
                            CornerRadius="34"
                            HeightRequest="68"
                            WidthRequest="68"
                            HasShadow="False"
                            Padding="0"
                            HorizontalOptions="Center"
                            VerticalOptions="Start"/>


                        <Frame
                            BackgroundColor="White"
                            Background="White"
                            CornerRadius="10"
                            HeightRequest="76"
                            HasShadow="False"
                            Padding="0"
                            VerticalOptions="End"/>

                        <parts:TransparentFooter 
                            InputTransparent="True"
                            IsChatBotPageSelected="True"
                            HorizontalOptions="Fill"/>

                    </Grid>

                    <Label 
                        Margin="0,35,0,0"
                        TextColor="#000000"
                        FontAttributes="Bold"
                        FontSize="{x:OnPlatform Android=Large,iOS=19}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="317"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage11.Header}"/>
                    <Label 
                        Margin="0,0,0,0"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="{x:OnPlatform Android=Default,iOS=16}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="320"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage11.Description}"/>

                </StackLayout>
                
            </Grid>


            <Grid Grid.Row="2">
                <Button 
                    Command="{Binding Source={x:Reference this},Path=GoNext}"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill"
                    Margin="63,0,63,0"
                    HeightRequest="56"
                    FontSize="17"
                    FontAttributes="Bold"
                    Style="{x:StaticResource yellow_btn}"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage11.GoNext}"/>

            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>