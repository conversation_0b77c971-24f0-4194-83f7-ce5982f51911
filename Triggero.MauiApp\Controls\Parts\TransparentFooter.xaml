﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Parts.TransparentFooter"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentView.Resources>
        
        <ResourceDictionary>

            <Style x:Key="mainRBStyle" TargetType="RadioButton">
                <Style.Triggers>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Grid>
                                        <StackLayout>
                                            <Image 
                                                WidthRequest="16"
                                                HeightRequest="18"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Start"
                                                Source="footerMain.png"/>
                                            <Label 
                                                TextColor="{x:StaticResource lightBlueColor}"
                                                FontSize="10"
                                                HorizontalTextAlignment="Center"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.FooterMain}"/>
                                        </StackLayout>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Grid>
                                        <StackLayout>
                                            <Image 
                                                WidthRequest="16"
                                                HeightRequest="18"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Start"
                                                Source="footerMainChecked.png"/>
                                            <Label 
                                                TextColor="{x:StaticResource blueColor}"
                                                FontSize="10"
                                                HorizontalTextAlignment="Center"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.FooterMain}"/>
                                        </StackLayout>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="libraryRBStyle" TargetType="RadioButton">
                <Style.Triggers>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Grid>
                                        <StackLayout>
                                            <Image 
                                                WidthRequest="16"
                                                HeightRequest="20"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Start"
                                                Source="footerLibrary.png"/>
                                            <Label 
                                                TextColor="{x:StaticResource lightBlueColor}"
                                                FontSize="10"
                                                HorizontalTextAlignment="Center"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.FooterLibrary}"/>
                                        </StackLayout>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Grid>
                                        <StackLayout>
                                            <Image 
                                                WidthRequest="16"
                                                HeightRequest="20"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Start"
                                                Source="footerLibraryChecked.png"/>
                                            <Label 
                                                TextColor="{x:StaticResource blueColor}"
                                                FontSize="10"
                                                HorizontalTextAlignment="Center"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.FooterLibrary}"/>
                                        </StackLayout>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="testsRBStyle" TargetType="RadioButton">
                <Style.Triggers>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Grid>
                                        <StackLayout>
                                            <Image 
                                                WidthRequest="16"
                                                HeightRequest="20"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Start"
                                                Source="footerTests.png"/>
                                            <Label 
                                                TextColor="{x:StaticResource lightBlueColor}"
                                                FontSize="10"
                                                HorizontalTextAlignment="Center"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.Tests}"/>
                                        </StackLayout>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Grid>
                                        <StackLayout>
                                            <Image 
                                                WidthRequest="16"
                                                HeightRequest="20"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Start"
                                                Source="footerTestsChecked.png"/>
                                            <Label 
                                                TextColor="{x:StaticResource blueColor}"
                                                FontSize="10"
                                                HorizontalTextAlignment="Center"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.Tests}"/>
                                        </StackLayout>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>

            <Style x:Key="chatBotRBStyle" TargetType="RadioButton">
                <Style.Triggers>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="False">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Grid>
                                        <StackLayout>
                                            <Image 
                                                WidthRequest="16"
                                                HeightRequest="20"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Start"
                                                Source="footerChatBot.png"/>
                                            <Label 
                                                TextColor="{x:StaticResource lightBlueColor}"
                                                FontSize="10"
                                                HorizontalTextAlignment="Center"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.ChatBot}"/>
                                        </StackLayout>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                    <Trigger TargetType="RadioButton" Property="IsChecked" Value="True">
                        <Setter Property="ControlTemplate">
                            <Setter.Value>
                                <ControlTemplate>
                                    <Grid>
                                        <StackLayout>
                                            <Image 
                                                WidthRequest="16"
                                                HeightRequest="20"
                                                HorizontalOptions="Center"
                                                VerticalOptions="Start"
                                                Source="footerChatBotChecked.png"/>
                                            <Label 
                                                TextColor="{x:StaticResource blueColor}"
                                                FontSize="10"
                                                HorizontalTextAlignment="Center"
                                                HorizontalOptions="Center"
                                                Text="{Binding Source={x:Static mobile:App.This},Path=Interface.MainPage.ChatBot}"/>
                                        </StackLayout>
                                    </Grid>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Trigger>
                </Style.Triggers>
            </Style>
            
        </ResourceDictionary>
    </ContentView.Resources>
  <ContentView.Content>
      <Grid>
           <Grid.ColumnDefinitions>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="68"/>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>


            <Grid Grid.Column="0">
                <RadioButton 
                    Margin="0,15,0,0"
                    GroupName="groupIcons"
                    IsChecked="{Binding Source={x:Reference this},Path=IsMainPageSelected,Mode=TwoWay}"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    Style="{x:StaticResource mainRBStyle}"/>
            </Grid>
          
            <Grid Grid.Column="1">
                <RadioButton 
                    Margin="0,15,0,0"
                    GroupName="groupIcons"
                    IsChecked="{Binding Source={x:Reference this},Path=IsLibraryPageSelected,Mode=TwoWay}"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    Style="{x:StaticResource libraryRBStyle}"/>
            </Grid>
          
            <Grid Grid.Column="2">
                <ImageButton 
                    InputTransparent="True"
                    Command="{Binding Source={x:Reference this},Path=GoToMoodTracker}"
                    BackgroundColor="Transparent"
                    Source="footerBigCirclePlus.png"
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    Margin="0,6,0,0"
                    HeightRequest="56"
                    WidthRequest="56"/>
            </Grid>
          
            <Grid Grid.Column="3">
                <RadioButton 
                    Margin="0,15,0,0"
                    GroupName="groupIcons"
                    IsChecked="{Binding Source={x:Reference this},Path=IsTestsPageSelected,Mode=TwoWay}"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    Style="{x:StaticResource testsRBStyle}"/>
            </Grid>
          
            <Grid Grid.Column="4">
                <RadioButton 
                    Margin="0,15,0,0"
                    GroupName="groupIcons"
                    IsChecked="{Binding Source={x:Reference this},Path=IsChatBotPageSelected,Mode=TwoWay}"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    Style="{x:StaticResource chatBotRBStyle}"/>
            </Grid>

        </Grid>
  </ContentView.Content>
</ContentView>