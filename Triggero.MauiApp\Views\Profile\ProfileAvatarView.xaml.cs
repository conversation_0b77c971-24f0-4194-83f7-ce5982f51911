﻿using AppoMobi.Specials;

using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows.Input;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models;
using Triggero.Models.General;



namespace Triggero.MauiMobileApp.Views.Pages.Profile
{

    public partial class ProfileAvatarView : ContentPage
    {
        private List<UserAvatar> avatars;

        public ProfileAvatarView()
        {

            try
            {
                InitializeComponent();
                NavigationPage.SetHasNavigationBar(this, false);

                Tasks.StartDelayed(TimeSpan.FromMilliseconds(500), () =>
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        try
                        {
                            Disappearing += ProfileAvatarView_Disappearing;

                            Wrappers = new ObservableCollection<AvatarWrapper>();

                            avatars = await ApplicationState.Data.GetAvatars();

                            foreach (var avatar in avatars)
                            {
                                Wrappers.Add(new AvatarWrapper(avatar));
                            }

                            SetupAvatars();

                        }
                        catch (Exception e)
                        {
                            Super.Log(e);
                            App.GoBack(false);
                        }
                    });
                });


            }
            catch (Exception e)
            {
                Super.DisplayException(this, e);
            }

        }

        void SetupAvatars()
        {
            if (avatars != null)
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    var user = await AuthHelper.GetUser();

                    dotsView.DefaultHeightRequest = 8;
                    dotsView.DefaultWidthRequest = 8;

                    dotsView.SelectedHeightRequest = 8;
                    dotsView.SelectedWidthRequest = 8;

                    var found = Wrappers.FirstOrDefault(o => o.Avatar.Id == user.AvatarId);
                    if (found != null)
                    {
                        SelectedIndex = Wrappers.IndexOf(found);
                    }



                    dotsView.SetDots(avatars.Count, SelectedIndex);
                    dotsView.DotTapped += DotsView_DotTapped;

                });
        }

        protected override async void OnAppearing()
        {
            SetupAvatars();
        }


        private int selectedIndex = -1;
        public int SelectedIndex
        {
            get { return selectedIndex; }
            set
            {
                selectedIndex = value;
                OnPropertyChanged(nameof(SelectedIndex));

                if (dotsView != null)
                {
                    dotsView.SetDots(avatars.Count, SelectedIndex);
                }
            }
        }


        private ObservableCollection<AvatarWrapper> wrappers = new ObservableCollection<AvatarWrapper>();
        public ObservableCollection<AvatarWrapper> Wrappers
        {
            get { return wrappers; }
            set { wrappers = value; OnPropertyChanged(nameof(Wrappers)); }
        }



        private void DotsView_DotTapped(object sender, int e)
        {
            // carousel.SelectedIndex = e;
        }



        private async void ProfileAvatarView_Disappearing(object sender, EventArgs e)
        {
            var selectedAvatar = Wrappers[SelectedIndex].Avatar;
            await AuthHelper.SetUserAvatar(selectedAvatar);
            GlobalEvents.OnUserPropertyChanged(AuthHelper.User);
        }

        private ICommand close;
        public ICommand Close
        {
            get => close ??= new RelayCommand(async obj =>
            {
                await App.Current.MainPage.Navigation.PopAsync();
            });
        }
    }
}