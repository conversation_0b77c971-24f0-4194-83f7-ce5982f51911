﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage1"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="170"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="140"/>
            </Grid.RowDefinitions>
            
            <Image
                Grid.RowSpan="3"
                Aspect="Fill"
                Source="tutorialBlur1.png"/>

            <Grid Grid.Row="0">

                <Grid
                    VerticalOptions="End"
                    HeightRequest="125">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <Image
                        Grid.ColumnSpan="2"
                        Aspect="Fill"
                        Source="tutorialProfileContainer.png"/>

                    <Grid Grid.Column="0">
                        <StackLayout
                            Spacing="12"
                            HeightRequest="66"
                            VerticalOptions="Center"
                            Orientation="Horizontal">

                            <Frame 
                                Margin="20,0,0,0"
                                CornerRadius="33"
                                Padding="0"
                                IsClippedToBounds="True"
                                BackgroundColor="#DEEDF9"
                                BorderColor="{x:StaticResource blueColor}"
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                HeightRequest="66"
                                WidthRequest="66"
                                HasShadow="False">
                                <Image 
                                    x:Name="avatar"
                                    HorizontalOptions="Start"
                                    VerticalOptions="Center"
                                    HeightRequest="66"
                                    WidthRequest="66"/>
                            </Frame>

                            <StackLayout
                                HorizontalOptions="Start"
                                VerticalOptions="Center"
                                Spacing="8">
                                <Label 
                                    x:Name="hiName"
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontSize="17"
                                    FontAttributes="Bold"
                                    HorizontalOptions="Start"
                                    Text="Привет, Кристина"/>
                                <Label 
                                    TextColor="{x:StaticResource greyTextColor}"
                                    FontSize="14"
                                    HorizontalOptions="Start"
                                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage1.WhatWillDo}"/>
                            </StackLayout>

                        </StackLayout>
                    </Grid>

                    <Grid Grid.Column="1">
                        <StackLayout
                            Margin="0,0,20,0"
                            HorizontalOptions="End"
                            VerticalOptions="Center"
                            Spacing="20"
                            Orientation="Horizontal">
                            <ImageButton
                                BackgroundColor="Transparent"
                                WidthRequest="20"
                                HeightRequest="20"
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Source="search.png"/>
                            <ImageButton
                                BackgroundColor="Transparent"
                                WidthRequest="20"
                                HeightRequest="20"
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                Source="likeSet.png"/>
                        </StackLayout>
                    </Grid>

                </Grid>
                
            </Grid>

            <Grid Grid.Row="1">

                <StackLayout
                    Margin="0,40,0,0"
                    Spacing="20">
                    <Label 
                        Margin="0,0,0,0"
                        TextColor="#000000"
                        FontAttributes="Bold"
                        FontSize="{x:OnPlatform Android=Large,iOS=19}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="317"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage1.Header}"/>
                    <Label 
                        Margin="0,0,0,0"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="{x:OnPlatform Android=Default,iOS=16}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="307"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage1.Description}"/>


                </StackLayout>
                
            </Grid>

            <Grid Grid.Row="2">
                <Button 
                    Command="{Binding Source={x:Reference this},Path=GoNext}"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill"
                    Margin="63,0,63,0"
                    HeightRequest="56"
                    FontSize="17"
                    FontAttributes="Bold"
                    Style="{x:StaticResource yellow_btn}"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage1.GoNext}"/>

            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>