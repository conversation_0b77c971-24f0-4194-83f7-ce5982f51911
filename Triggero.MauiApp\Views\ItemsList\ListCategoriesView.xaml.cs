﻿using AppoMobi.Specials;
using DrawnUi.Views;
using SkiaSharp;
using Triggero.MauiMobileApp.ViewModels;

namespace Triggero.MauiMobileApp.Views
{
    public partial class ListCategoriesView : Canvas
    {
        private readonly BaseCategoriesViewModel _vm;

        public ListCategoriesView(BaseCategoriesViewModel vm, int msDelayInitialize = 1)
        {
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            BackgroundColor = Colors.White;

            _vm = vm;

            InitializeComponent();

            BindingContext = _vm;

            Tasks.StartDelayed(TimeSpan.FromMilliseconds(50), async () =>
            {
                await _vm.InitializeAsyc();
            });
        }

      
    }
}