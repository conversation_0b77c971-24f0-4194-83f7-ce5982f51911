﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml" 
             xmlns:parts="clr-namespace:Triggero.Controls.Parts"
             x:Class="Triggero.MauiMobileApp.Views.NewTutorialPage10"
             xmlns:app="clr-namespace:Triggero"
             xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
             x:Name="this">
    <ContentPage.Content>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="340"/>
                <RowDefinition Height="*"/>
                <RowDefinition Height="140"/>
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="3"
                Aspect="Fill"
                Source="tutorialBlur3.png"/>


           <Grid Grid.Row="0">

                <Grid
                    VerticalOptions="End"
                    HeightRequest="300">
                    
                    <Image
                        Aspect="Fill"
                        Margin="-6,0,-13,0"
                        Source="tutorialTestsContainer.png"/>

                    <ScrollView
                        Margin="20,10,20,13"
                        HorizontalScrollBarVisibility="Never"
                        VerticalScrollBarVisibility="Never"
                        InputTransparent="True"
                        IsEnabled="False">
                        <StackLayout
                            Spacing="12"
                            x:Name="testsLayout">

                        </StackLayout>
                    </ScrollView>

                </Grid>



            </Grid>

           <Grid Grid.Row="1">

                <StackLayout
                    Margin="20,0,20,0">

                    <Grid 
                    Margin="0,10,0,0"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill"
                    HeightRequest="100">

                        <Frame
                            BackgroundColor="White"
                            CornerRadius="34"
                            HeightRequest="68"
                            WidthRequest="68"
                            HasShadow="False"
                            Padding="0"
                            HorizontalOptions="Center"
                            VerticalOptions="Start"/>


                        <Frame
                            BackgroundColor="White"
                            CornerRadius="10"
                            HeightRequest="76"
                            HasShadow="False"
                            Padding="0"
                            VerticalOptions="End"/>

                        <parts:TransparentFooter 
                            InputTransparent="True"
                            IsTestsPageSelected="True"
                            HorizontalOptions="Fill"/>


                    </Grid>

                    <Label 
                        Margin="0,35,0,0"
                        TextColor="#000000"
                        FontAttributes="Bold"
                        FontSize="{x:OnPlatform Android=Large,iOS=19}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="317"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage10.Header}"/>
                    <Label 
                        Margin="0,0,0,0"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="{x:OnPlatform Android=Default,iOS=16}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        WidthRequest="270"
                        Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage10.Description}"/>

                </StackLayout>
                
            </Grid>


               <Grid Grid.Row="2">
                <Button 
                    Command="{Binding Source={x:Reference this},Path=GoNext}"
                    VerticalOptions="Start"
                    HorizontalOptions="Fill"
                    Margin="63,0,63,0"
                    HeightRequest="56"
                    FontSize="17"
                    FontAttributes="Bold"
                    Style="{x:StaticResource yellow_btn}"
                    Text="{Binding Source={x:Static mobile:App.This},Path=Interface.TutorialNew.TutorialNewPage10.GoNext}"/>

            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>