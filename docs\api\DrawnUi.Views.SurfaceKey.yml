### YamlMime:ManagedReference
items:
- uid: DrawnUi.Views.SurfaceKey
  commentId: T:DrawnUi.Views.SurfaceKey
  id: SurfaceKey
  parent: DrawnUi.Views
  children:
  - DrawnUi.Views.SurfaceKey.#ctor(System.Int32,System.Int32)
  - DrawnUi.Views.SurfaceKey.Equals(DrawnUi.Views.SurfaceKey)
  - DrawnUi.Views.SurfaceKey.Equals(System.Object)
  - DrawnUi.Views.SurfaceKey.GetHashCode
  - DrawnUi.Views.SurfaceKey.Height
  - DrawnUi.Views.SurfaceKey.Width
  langs:
  - csharp
  - vb
  name: SurfaceKey
  nameWithType: SurfaceKey
  fullName: DrawnUi.Views.SurfaceKey
  type: Struct
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SurfaceKey
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 137
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: 'public struct SurfaceKey : IEquatable<SurfaceKey>'
    content.vb: Public Structure SurfaceKey Implements IEquatable(Of SurfaceKey)
  implements:
  - System.IEquatable{DrawnUi.Views.SurfaceKey}
  inheritedMembers:
  - System.ValueType.ToString
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetType
  - System.Object.ReferenceEquals(System.Object,System.Object)
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Views.SurfaceKey.Width
  commentId: F:DrawnUi.Views.SurfaceKey.Width
  id: Width
  parent: DrawnUi.Views.SurfaceKey
  langs:
  - csharp
  - vb
  name: Width
  nameWithType: SurfaceKey.Width
  fullName: DrawnUi.Views.SurfaceKey.Width
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Width
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 139
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public readonly int Width
    return:
      type: System.Int32
    content.vb: Public ReadOnly Width As Integer
- uid: DrawnUi.Views.SurfaceKey.Height
  commentId: F:DrawnUi.Views.SurfaceKey.Height
  id: Height
  parent: DrawnUi.Views.SurfaceKey
  langs:
  - csharp
  - vb
  name: Height
  nameWithType: SurfaceKey.Height
  fullName: DrawnUi.Views.SurfaceKey.Height
  type: Field
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Height
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 140
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public readonly int Height
    return:
      type: System.Int32
    content.vb: Public ReadOnly Height As Integer
- uid: DrawnUi.Views.SurfaceKey.#ctor(System.Int32,System.Int32)
  commentId: M:DrawnUi.Views.SurfaceKey.#ctor(System.Int32,System.Int32)
  id: '#ctor(System.Int32,System.Int32)'
  parent: DrawnUi.Views.SurfaceKey
  langs:
  - csharp
  - vb
  name: SurfaceKey(int, int)
  nameWithType: SurfaceKey.SurfaceKey(int, int)
  fullName: DrawnUi.Views.SurfaceKey.SurfaceKey(int, int)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 142
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  syntax:
    content: public SurfaceKey(int width, int height)
    parameters:
    - id: width
      type: System.Int32
    - id: height
      type: System.Int32
    content.vb: Public Sub New(width As Integer, height As Integer)
  overload: DrawnUi.Views.SurfaceKey.#ctor*
  nameWithType.vb: SurfaceKey.New(Integer, Integer)
  fullName.vb: DrawnUi.Views.SurfaceKey.New(Integer, Integer)
  name.vb: New(Integer, Integer)
- uid: DrawnUi.Views.SurfaceKey.Equals(DrawnUi.Views.SurfaceKey)
  commentId: M:DrawnUi.Views.SurfaceKey.Equals(DrawnUi.Views.SurfaceKey)
  id: Equals(DrawnUi.Views.SurfaceKey)
  parent: DrawnUi.Views.SurfaceKey
  langs:
  - csharp
  - vb
  name: Equals(SurfaceKey)
  nameWithType: SurfaceKey.Equals(SurfaceKey)
  fullName: DrawnUi.Views.SurfaceKey.Equals(DrawnUi.Views.SurfaceKey)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Equals
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 148
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Indicates whether the current object is equal to another object of the same type.
  example: []
  syntax:
    content: public bool Equals(SurfaceKey other)
    parameters:
    - id: other
      type: DrawnUi.Views.SurfaceKey
      description: An object to compare with this object.
    return:
      type: System.Boolean
      description: <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">true</a> if the current object is equal to the <code class="paramref">other</code> parameter; otherwise, <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">false</a>.
    content.vb: Public Function Equals(other As SurfaceKey) As Boolean
  overload: DrawnUi.Views.SurfaceKey.Equals*
  implements:
  - System.IEquatable{DrawnUi.Views.SurfaceKey}.Equals(DrawnUi.Views.SurfaceKey)
- uid: DrawnUi.Views.SurfaceKey.Equals(System.Object)
  commentId: M:DrawnUi.Views.SurfaceKey.Equals(System.Object)
  id: Equals(System.Object)
  parent: DrawnUi.Views.SurfaceKey
  langs:
  - csharp
  - vb
  name: Equals(object)
  nameWithType: SurfaceKey.Equals(object)
  fullName: DrawnUi.Views.SurfaceKey.Equals(object)
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Equals
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 153
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Indicates whether this instance and a specified object are equal.
  example: []
  syntax:
    content: public override bool Equals(object obj)
    parameters:
    - id: obj
      type: System.Object
      description: The object to compare with the current instance.
    return:
      type: System.Boolean
      description: <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">true</a> if <code class="paramref">obj</code> and this instance are the same type and represent the same value; otherwise, <a href="https://learn.microsoft.com/dotnet/csharp/language-reference/builtin-types/bool">false</a>.
    content.vb: Public Overrides Function Equals(obj As Object) As Boolean
  overridden: System.ValueType.Equals(System.Object)
  overload: DrawnUi.Views.SurfaceKey.Equals*
  nameWithType.vb: SurfaceKey.Equals(Object)
  fullName.vb: DrawnUi.Views.SurfaceKey.Equals(Object)
  name.vb: Equals(Object)
- uid: DrawnUi.Views.SurfaceKey.GetHashCode
  commentId: M:DrawnUi.Views.SurfaceKey.GetHashCode
  id: GetHashCode
  parent: DrawnUi.Views.SurfaceKey
  langs:
  - csharp
  - vb
  name: GetHashCode()
  nameWithType: SurfaceKey.GetHashCode()
  fullName: DrawnUi.Views.SurfaceKey.GetHashCode()
  type: Method
  source:
    remote:
      path: src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetHashCode
    path: ../src/Maui/DrawnUi/Views/SurfaceCacheManager.cs
    startLine: 158
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Views
  summary: Returns the hash code for this instance.
  example: []
  syntax:
    content: public override int GetHashCode()
    return:
      type: System.Int32
      description: A 32-bit signed integer that is the hash code for this instance.
    content.vb: Public Overrides Function GetHashCode() As Integer
  overridden: System.ValueType.GetHashCode
  overload: DrawnUi.Views.SurfaceKey.GetHashCode*
references:
- uid: DrawnUi.Views
  commentId: N:DrawnUi.Views
  href: DrawnUi.html
  name: DrawnUi.Views
  nameWithType: DrawnUi.Views
  fullName: DrawnUi.Views
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Views
    name: Views
    href: DrawnUi.Views.html
- uid: System.IEquatable{DrawnUi.Views.SurfaceKey}
  commentId: T:System.IEquatable{DrawnUi.Views.SurfaceKey}
  parent: System
  definition: System.IEquatable`1
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<SurfaceKey>
  nameWithType: IEquatable<SurfaceKey>
  fullName: System.IEquatable<DrawnUi.Views.SurfaceKey>
  nameWithType.vb: IEquatable(Of SurfaceKey)
  fullName.vb: System.IEquatable(Of DrawnUi.Views.SurfaceKey)
  name.vb: IEquatable(Of SurfaceKey)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - uid: DrawnUi.Views.SurfaceKey
    name: SurfaceKey
    href: DrawnUi.Views.SurfaceKey.html
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Views.SurfaceKey
    name: SurfaceKey
    href: DrawnUi.Views.SurfaceKey.html
  - name: )
- uid: System.ValueType.ToString
  commentId: M:System.ValueType.ToString
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  name: ToString()
  nameWithType: ValueType.ToString()
  fullName: System.ValueType.ToString()
  spec.csharp:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.tostring
  - name: (
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.IEquatable`1
  commentId: T:System.IEquatable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<T>
  nameWithType: IEquatable<T>
  fullName: System.IEquatable<T>
  nameWithType.vb: IEquatable(Of T)
  fullName.vb: System.IEquatable(Of T)
  name.vb: IEquatable(Of T)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.ValueType
  commentId: T:System.ValueType
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype
  name: ValueType
  nameWithType: ValueType
  fullName: System.ValueType
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Views.SurfaceKey.#ctor*
  commentId: Overload:DrawnUi.Views.SurfaceKey.#ctor
  href: DrawnUi.Views.SurfaceKey.html#DrawnUi_Views_SurfaceKey__ctor_System_Int32_System_Int32_
  name: SurfaceKey
  nameWithType: SurfaceKey.SurfaceKey
  fullName: DrawnUi.Views.SurfaceKey.SurfaceKey
  nameWithType.vb: SurfaceKey.New
  fullName.vb: DrawnUi.Views.SurfaceKey.New
  name.vb: New
- uid: DrawnUi.Views.SurfaceKey.Equals*
  commentId: Overload:DrawnUi.Views.SurfaceKey.Equals
  href: DrawnUi.Views.SurfaceKey.html#DrawnUi_Views_SurfaceKey_Equals_DrawnUi_Views_SurfaceKey_
  name: Equals
  nameWithType: SurfaceKey.Equals
  fullName: DrawnUi.Views.SurfaceKey.Equals
- uid: System.IEquatable{DrawnUi.Views.SurfaceKey}.Equals(DrawnUi.Views.SurfaceKey)
  commentId: M:System.IEquatable{DrawnUi.Views.SurfaceKey}.Equals(DrawnUi.Views.SurfaceKey)
  parent: System.IEquatable{DrawnUi.Views.SurfaceKey}
  definition: System.IEquatable`1.Equals(`0)
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1.equals
  name: Equals(SurfaceKey)
  nameWithType: IEquatable<SurfaceKey>.Equals(SurfaceKey)
  fullName: System.IEquatable<DrawnUi.Views.SurfaceKey>.Equals(DrawnUi.Views.SurfaceKey)
  nameWithType.vb: IEquatable(Of SurfaceKey).Equals(SurfaceKey)
  fullName.vb: System.IEquatable(Of DrawnUi.Views.SurfaceKey).Equals(DrawnUi.Views.SurfaceKey)
  spec.csharp:
  - uid: System.IEquatable{DrawnUi.Views.SurfaceKey}.Equals(DrawnUi.Views.SurfaceKey)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1.equals
  - name: (
  - uid: DrawnUi.Views.SurfaceKey
    name: SurfaceKey
    href: DrawnUi.Views.SurfaceKey.html
  - name: )
  spec.vb:
  - uid: System.IEquatable{DrawnUi.Views.SurfaceKey}.Equals(DrawnUi.Views.SurfaceKey)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1.equals
  - name: (
  - uid: DrawnUi.Views.SurfaceKey
    name: SurfaceKey
    href: DrawnUi.Views.SurfaceKey.html
  - name: )
- uid: DrawnUi.Views.SurfaceKey
  commentId: T:DrawnUi.Views.SurfaceKey
  parent: DrawnUi.Views
  href: DrawnUi.Views.SurfaceKey.html
  name: SurfaceKey
  nameWithType: SurfaceKey
  fullName: DrawnUi.Views.SurfaceKey
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: System.IEquatable`1.Equals(`0)
  commentId: M:System.IEquatable`1.Equals(`0)
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1.equals
  name: Equals(T)
  nameWithType: IEquatable<T>.Equals(T)
  fullName: System.IEquatable<T>.Equals(T)
  nameWithType.vb: IEquatable(Of T).Equals(T)
  fullName.vb: System.IEquatable(Of T).Equals(T)
  spec.csharp:
  - uid: System.IEquatable`1.Equals(`0)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1.equals
  - name: (
  - name: T
  - name: )
  spec.vb:
  - uid: System.IEquatable`1.Equals(`0)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1.equals
  - name: (
  - name: T
  - name: )
- uid: System.ValueType.Equals(System.Object)
  commentId: M:System.ValueType.Equals(System.Object)
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  name: Equals(object)
  nameWithType: ValueType.Equals(object)
  fullName: System.ValueType.Equals(object)
  nameWithType.vb: ValueType.Equals(Object)
  fullName.vb: System.ValueType.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.ValueType.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.equals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.ValueType.GetHashCode
  commentId: M:System.ValueType.GetHashCode
  parent: System.ValueType
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  name: GetHashCode()
  nameWithType: ValueType.GetHashCode()
  fullName: System.ValueType.GetHashCode()
  spec.csharp:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.ValueType.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.valuetype.gethashcode
  - name: (
  - name: )
- uid: DrawnUi.Views.SurfaceKey.GetHashCode*
  commentId: Overload:DrawnUi.Views.SurfaceKey.GetHashCode
  href: DrawnUi.Views.SurfaceKey.html#DrawnUi_Views_SurfaceKey_GetHashCode
  name: GetHashCode
  nameWithType: SurfaceKey.GetHashCode
  fullName: DrawnUi.Views.SurfaceKey.GetHashCode
