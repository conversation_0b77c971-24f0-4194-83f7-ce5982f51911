### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.ColorBlendAnimator
  commentId: T:DrawnUi.Draw.ColorBlendAnimator
  id: ColorBlendAnimator
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.ColorBlendAnimator.#ctor(DrawnUi.Draw.IDrawnBase)
  - DrawnUi.Draw.ColorBlendAnimator.AnimateAsync(System.Action{Microsoft.Maui.Graphics.Color},Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color,System.Double,Microsoft.Maui.Easing,System.Threading.CancellationTokenSource)
  - DrawnUi.Draw.ColorBlendAnimator.Color
  - DrawnUi.Draw.ColorBlendAnimator.Color1
  - DrawnUi.Draw.ColorBlendAnimator.Color2
  - DrawnUi.Draw.ColorBlendAnimator.GetColor(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color,System.Single)
  - DrawnUi.Draw.ColorBlendAnimator.OnColorChanged
  - DrawnUi.Draw.ColorBlendAnimator.OnProgressChanged(System.Double)
  langs:
  - csharp
  - vb
  name: ColorBlendAnimator
  nameWithType: ColorBlendAnimator
  fullName: DrawnUi.Draw.ColorBlendAnimator
  type: Class
  source:
    remote:
      path: src/Shared/Features/Animators/ColorBlendAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: ColorBlendAnimator
    path: ../src/Shared/Features/Animators/ColorBlendAnimator.cs
    startLine: 2
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: 'public class ColorBlendAnimator : ProgressAnimator, ISkiaAnimator, IDisposable'
    content.vb: Public Class ColorBlendAnimator Inherits ProgressAnimator Implements ISkiaAnimator, IDisposable
  inheritance:
  - System.Object
  - DrawnUi.Draw.AnimatorBase
  - DrawnUi.Draw.SkiaValueAnimator
  - DrawnUi.Draw.ProgressAnimator
  implements:
  - DrawnUi.Draw.ISkiaAnimator
  - System.IDisposable
  inheritedMembers:
  - DrawnUi.Draw.ProgressAnimator.TransformReportedValue(System.Int64)
  - DrawnUi.Draw.SkiaValueAnimator.Dispose
  - DrawnUi.Draw.SkiaValueAnimator.Stop
  - DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
  - DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
  - DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
  - DrawnUi.Draw.SkiaValueAnimator.CycleFInished
  - DrawnUi.Draw.SkiaValueAnimator.Finished
  - DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
  - DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
  - DrawnUi.Draw.SkiaValueAnimator.UseInterpolator
  - DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
  - DrawnUi.Draw.SkiaValueAnimator.Repeat
  - DrawnUi.Draw.SkiaValueAnimator.mValue
  - DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
  - DrawnUi.Draw.SkiaValueAnimator.mMaxValue
  - DrawnUi.Draw.SkiaValueAnimator.mMinValue
  - DrawnUi.Draw.SkiaValueAnimator.Easing
  - DrawnUi.Draw.SkiaValueAnimator.Speed
  - DrawnUi.Draw.SkiaValueAnimator.Debug
  - DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
  - DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
  - DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
  - DrawnUi.Draw.SkiaValueAnimator.Progress
  - DrawnUi.Draw.SkiaValueAnimator.OnUpdated
  - DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
  - DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
  - DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
  - DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
  - DrawnUi.Draw.AnimatorBase.IsPostAnimator
  - DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
  - DrawnUi.Draw.AnimatorBase.Radians(System.Double)
  - DrawnUi.Draw.AnimatorBase.runDelayMs
  - DrawnUi.Draw.AnimatorBase.Register
  - DrawnUi.Draw.AnimatorBase.Unregister
  - DrawnUi.Draw.AnimatorBase.Cancel
  - DrawnUi.Draw.AnimatorBase.Pause
  - DrawnUi.Draw.AnimatorBase.Resume
  - DrawnUi.Draw.AnimatorBase.IsPaused
  - DrawnUi.Draw.AnimatorBase.OnStop
  - DrawnUi.Draw.AnimatorBase.OnStart
  - DrawnUi.Draw.AnimatorBase.Parent
  - DrawnUi.Draw.AnimatorBase.IsDeactivated
  - DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
  - DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
  - DrawnUi.Draw.AnimatorBase.Uid
  - DrawnUi.Draw.AnimatorBase.IsRunning
  - DrawnUi.Draw.AnimatorBase.WasStarted
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.ColorBlendAnimator.#ctor(DrawnUi.Draw.IDrawnBase)
  commentId: M:DrawnUi.Draw.ColorBlendAnimator.#ctor(DrawnUi.Draw.IDrawnBase)
  id: '#ctor(DrawnUi.Draw.IDrawnBase)'
  parent: DrawnUi.Draw.ColorBlendAnimator
  langs:
  - csharp
  - vb
  name: ColorBlendAnimator(IDrawnBase)
  nameWithType: ColorBlendAnimator.ColorBlendAnimator(IDrawnBase)
  fullName: DrawnUi.Draw.ColorBlendAnimator.ColorBlendAnimator(DrawnUi.Draw.IDrawnBase)
  type: Constructor
  source:
    remote:
      path: src/Shared/Features/Animators/ColorBlendAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Shared/Features/Animators/ColorBlendAnimator.cs
    startLine: 4
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ColorBlendAnimator(IDrawnBase parent)
    parameters:
    - id: parent
      type: DrawnUi.Draw.IDrawnBase
    content.vb: Public Sub New(parent As IDrawnBase)
  overload: DrawnUi.Draw.ColorBlendAnimator.#ctor*
  nameWithType.vb: ColorBlendAnimator.New(IDrawnBase)
  fullName.vb: DrawnUi.Draw.ColorBlendAnimator.New(DrawnUi.Draw.IDrawnBase)
  name.vb: New(IDrawnBase)
- uid: DrawnUi.Draw.ColorBlendAnimator.OnProgressChanged(System.Double)
  commentId: M:DrawnUi.Draw.ColorBlendAnimator.OnProgressChanged(System.Double)
  id: OnProgressChanged(System.Double)
  parent: DrawnUi.Draw.ColorBlendAnimator
  langs:
  - csharp
  - vb
  name: OnProgressChanged(double)
  nameWithType: ColorBlendAnimator.OnProgressChanged(double)
  fullName: DrawnUi.Draw.ColorBlendAnimator.OnProgressChanged(double)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/ColorBlendAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnProgressChanged
    path: ../src/Shared/Features/Animators/ColorBlendAnimator.cs
    startLine: 14
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  example: []
  syntax:
    content: protected override void OnProgressChanged(double progress)
    parameters:
    - id: progress
      type: System.Double
    content.vb: Protected Overrides Sub OnProgressChanged(progress As Double)
  overridden: DrawnUi.Draw.ProgressAnimator.OnProgressChanged(System.Double)
  overload: DrawnUi.Draw.ColorBlendAnimator.OnProgressChanged*
  nameWithType.vb: ColorBlendAnimator.OnProgressChanged(Double)
  fullName.vb: DrawnUi.Draw.ColorBlendAnimator.OnProgressChanged(Double)
  name.vb: OnProgressChanged(Double)
- uid: DrawnUi.Draw.ColorBlendAnimator.Color
  commentId: P:DrawnUi.Draw.ColorBlendAnimator.Color
  id: Color
  parent: DrawnUi.Draw.ColorBlendAnimator
  langs:
  - csharp
  - vb
  name: Color
  nameWithType: ColorBlendAnimator.Color
  fullName: DrawnUi.Draw.ColorBlendAnimator.Color
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/ColorBlendAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Color
    path: ../src/Shared/Features/Animators/ColorBlendAnimator.cs
    startLine: 20
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Color Color { get; protected set; }
    parameters: []
    return:
      type: Microsoft.Maui.Graphics.Color
    content.vb: Public Property Color As Color
  overload: DrawnUi.Draw.ColorBlendAnimator.Color*
- uid: DrawnUi.Draw.ColorBlendAnimator.Color1
  commentId: P:DrawnUi.Draw.ColorBlendAnimator.Color1
  id: Color1
  parent: DrawnUi.Draw.ColorBlendAnimator
  langs:
  - csharp
  - vb
  name: Color1
  nameWithType: ColorBlendAnimator.Color1
  fullName: DrawnUi.Draw.ColorBlendAnimator.Color1
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/ColorBlendAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Color1
    path: ../src/Shared/Features/Animators/ColorBlendAnimator.cs
    startLine: 24
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Color Color1 { get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Graphics.Color
    content.vb: Public Property Color1 As Color
  overload: DrawnUi.Draw.ColorBlendAnimator.Color1*
- uid: DrawnUi.Draw.ColorBlendAnimator.Color2
  commentId: P:DrawnUi.Draw.ColorBlendAnimator.Color2
  id: Color2
  parent: DrawnUi.Draw.ColorBlendAnimator
  langs:
  - csharp
  - vb
  name: Color2
  nameWithType: ColorBlendAnimator.Color2
  fullName: DrawnUi.Draw.ColorBlendAnimator.Color2
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/ColorBlendAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Color2
    path: ../src/Shared/Features/Animators/ColorBlendAnimator.cs
    startLine: 25
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Color Color2 { get; set; }
    parameters: []
    return:
      type: Microsoft.Maui.Graphics.Color
    content.vb: Public Property Color2 As Color
  overload: DrawnUi.Draw.ColorBlendAnimator.Color2*
- uid: DrawnUi.Draw.ColorBlendAnimator.OnColorChanged
  commentId: P:DrawnUi.Draw.ColorBlendAnimator.OnColorChanged
  id: OnColorChanged
  parent: DrawnUi.Draw.ColorBlendAnimator
  langs:
  - csharp
  - vb
  name: OnColorChanged
  nameWithType: ColorBlendAnimator.OnColorChanged
  fullName: DrawnUi.Draw.ColorBlendAnimator.OnColorChanged
  type: Property
  source:
    remote:
      path: src/Shared/Features/Animators/ColorBlendAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OnColorChanged
    path: ../src/Shared/Features/Animators/ColorBlendAnimator.cs
    startLine: 27
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Action<Color> OnColorChanged { get; set; }
    parameters: []
    return:
      type: System.Action{Microsoft.Maui.Graphics.Color}
    content.vb: Public Property OnColorChanged As Action(Of Color)
  overload: DrawnUi.Draw.ColorBlendAnimator.OnColorChanged*
- uid: DrawnUi.Draw.ColorBlendAnimator.GetColor(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color,System.Single)
  commentId: M:DrawnUi.Draw.ColorBlendAnimator.GetColor(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color,System.Single)
  id: GetColor(Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color,System.Single)
  parent: DrawnUi.Draw.ColorBlendAnimator
  langs:
  - csharp
  - vb
  name: GetColor(Color, Color, float)
  nameWithType: ColorBlendAnimator.GetColor(Color, Color, float)
  fullName: DrawnUi.Draw.ColorBlendAnimator.GetColor(Microsoft.Maui.Graphics.Color, Microsoft.Maui.Graphics.Color, float)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/ColorBlendAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: GetColor
    path: ../src/Shared/Features/Animators/ColorBlendAnimator.cs
    startLine: 31
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public static Color GetColor(Color start, Color end, float progress)
    parameters:
    - id: start
      type: Microsoft.Maui.Graphics.Color
    - id: end
      type: Microsoft.Maui.Graphics.Color
    - id: progress
      type: System.Single
    return:
      type: Microsoft.Maui.Graphics.Color
    content.vb: Public Shared Function GetColor(start As Color, [end] As Color, progress As Single) As Color
  overload: DrawnUi.Draw.ColorBlendAnimator.GetColor*
  nameWithType.vb: ColorBlendAnimator.GetColor(Color, Color, Single)
  fullName.vb: DrawnUi.Draw.ColorBlendAnimator.GetColor(Microsoft.Maui.Graphics.Color, Microsoft.Maui.Graphics.Color, Single)
  name.vb: GetColor(Color, Color, Single)
- uid: DrawnUi.Draw.ColorBlendAnimator.AnimateAsync(System.Action{Microsoft.Maui.Graphics.Color},Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color,System.Double,Microsoft.Maui.Easing,System.Threading.CancellationTokenSource)
  commentId: M:DrawnUi.Draw.ColorBlendAnimator.AnimateAsync(System.Action{Microsoft.Maui.Graphics.Color},Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color,System.Double,Microsoft.Maui.Easing,System.Threading.CancellationTokenSource)
  id: AnimateAsync(System.Action{Microsoft.Maui.Graphics.Color},Microsoft.Maui.Graphics.Color,Microsoft.Maui.Graphics.Color,System.Double,Microsoft.Maui.Easing,System.Threading.CancellationTokenSource)
  parent: DrawnUi.Draw.ColorBlendAnimator
  langs:
  - csharp
  - vb
  name: AnimateAsync(Action<Color>, Color, Color, double, Easing, CancellationTokenSource)
  nameWithType: ColorBlendAnimator.AnimateAsync(Action<Color>, Color, Color, double, Easing, CancellationTokenSource)
  fullName: DrawnUi.Draw.ColorBlendAnimator.AnimateAsync(System.Action<Microsoft.Maui.Graphics.Color>, Microsoft.Maui.Graphics.Color, Microsoft.Maui.Graphics.Color, double, Microsoft.Maui.Easing, System.Threading.CancellationTokenSource)
  type: Method
  source:
    remote:
      path: src/Shared/Features/Animators/ColorBlendAnimator.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: AnimateAsync
    path: ../src/Shared/Features/Animators/ColorBlendAnimator.cs
    startLine: 52
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Task AnimateAsync(Action<Color> callback, Color start, Color end, double msLength = 250, Easing easing = null, CancellationTokenSource cancel = null)
    parameters:
    - id: callback
      type: System.Action{Microsoft.Maui.Graphics.Color}
    - id: start
      type: Microsoft.Maui.Graphics.Color
    - id: end
      type: Microsoft.Maui.Graphics.Color
    - id: msLength
      type: System.Double
    - id: easing
      type: Microsoft.Maui.Easing
    - id: cancel
      type: System.Threading.CancellationTokenSource
    return:
      type: System.Threading.Tasks.Task
    content.vb: Public Function AnimateAsync(callback As Action(Of Color), start As Color, [end] As Color, msLength As Double = 250, easing As Easing = Nothing, cancel As CancellationTokenSource = Nothing) As Task
  overload: DrawnUi.Draw.ColorBlendAnimator.AnimateAsync*
  nameWithType.vb: ColorBlendAnimator.AnimateAsync(Action(Of Color), Color, Color, Double, Easing, CancellationTokenSource)
  fullName.vb: DrawnUi.Draw.ColorBlendAnimator.AnimateAsync(System.Action(Of Microsoft.Maui.Graphics.Color), Microsoft.Maui.Graphics.Color, Microsoft.Maui.Graphics.Color, Double, Microsoft.Maui.Easing, System.Threading.CancellationTokenSource)
  name.vb: AnimateAsync(Action(Of Color), Color, Color, Double, Easing, CancellationTokenSource)
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: DrawnUi.Draw.AnimatorBase
  commentId: T:DrawnUi.Draw.AnimatorBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.AnimatorBase.html
  name: AnimatorBase
  nameWithType: AnimatorBase
  fullName: DrawnUi.Draw.AnimatorBase
- uid: DrawnUi.Draw.SkiaValueAnimator
  commentId: T:DrawnUi.Draw.SkiaValueAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaValueAnimator.html
  name: SkiaValueAnimator
  nameWithType: SkiaValueAnimator
  fullName: DrawnUi.Draw.SkiaValueAnimator
- uid: DrawnUi.Draw.ProgressAnimator
  commentId: T:DrawnUi.Draw.ProgressAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ProgressAnimator.html
  name: ProgressAnimator
  nameWithType: ProgressAnimator
  fullName: DrawnUi.Draw.ProgressAnimator
- uid: DrawnUi.Draw.ISkiaAnimator
  commentId: T:DrawnUi.Draw.ISkiaAnimator
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ISkiaAnimator.html
  name: ISkiaAnimator
  nameWithType: ISkiaAnimator
  fullName: DrawnUi.Draw.ISkiaAnimator
- uid: System.IDisposable
  commentId: T:System.IDisposable
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.idisposable
  name: IDisposable
  nameWithType: IDisposable
  fullName: System.IDisposable
- uid: DrawnUi.Draw.ProgressAnimator.TransformReportedValue(System.Int64)
  commentId: M:DrawnUi.Draw.ProgressAnimator.TransformReportedValue(System.Int64)
  parent: DrawnUi.Draw.ProgressAnimator
  isExternal: true
  href: DrawnUi.Draw.ProgressAnimator.html#DrawnUi_Draw_ProgressAnimator_TransformReportedValue_System_Int64_
  name: TransformReportedValue(long)
  nameWithType: ProgressAnimator.TransformReportedValue(long)
  fullName: DrawnUi.Draw.ProgressAnimator.TransformReportedValue(long)
  nameWithType.vb: ProgressAnimator.TransformReportedValue(Long)
  fullName.vb: DrawnUi.Draw.ProgressAnimator.TransformReportedValue(Long)
  name.vb: TransformReportedValue(Long)
  spec.csharp:
  - uid: DrawnUi.Draw.ProgressAnimator.TransformReportedValue(System.Int64)
    name: TransformReportedValue
    href: DrawnUi.Draw.ProgressAnimator.html#DrawnUi_Draw_ProgressAnimator_TransformReportedValue_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ProgressAnimator.TransformReportedValue(System.Int64)
    name: TransformReportedValue
    href: DrawnUi.Draw.ProgressAnimator.html#DrawnUi_Draw_ProgressAnimator_TransformReportedValue_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Dispose
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Dispose
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose
  name: Dispose()
  nameWithType: SkiaValueAnimator.Dispose()
  fullName: DrawnUi.Draw.SkiaValueAnimator.Dispose()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Dispose
    name: Dispose
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Dispose
    name: Dispose
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Dispose
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Stop
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Stop
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Stop
  name: Stop()
  nameWithType: SkiaValueAnimator.Stop()
  fullName: DrawnUi.Draw.SkiaValueAnimator.Stop()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Stop
    name: Stop
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Stop
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Stop
    name: Stop
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Stop
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_
  name: RunAsync(Action, CancellationToken)
  nameWithType: SkiaValueAnimator.RunAsync(Action, CancellationToken)
  fullName: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action, System.Threading.CancellationToken)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
    name: RunAsync
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_
  - name: (
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: ','
  - name: " "
  - uid: System.Threading.CancellationToken
    name: CancellationToken
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.RunAsync(System.Action,System.Threading.CancellationToken)
    name: RunAsync
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_RunAsync_System_Action_System_Threading_CancellationToken_
  - name: (
  - uid: System.Action
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action
  - name: ','
  - name: " "
  - uid: System.Threading.CancellationToken
    name: CancellationToken
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtoken
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_
  name: OnRunningStateChanged(bool)
  nameWithType: SkiaValueAnimator.OnRunningStateChanged(bool)
  fullName: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(bool)
  nameWithType.vb: SkiaValueAnimator.OnRunningStateChanged(Boolean)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(Boolean)
  name.vb: OnRunningStateChanged(Boolean)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
    name: OnRunningStateChanged
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: bool
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.OnRunningStateChanged(System.Boolean)
    name: OnRunningStateChanged
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnRunningStateChanged_System_Boolean_
  - name: (
  - uid: System.Boolean
    name: Boolean
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.boolean
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_
  name: Seek(float)
  nameWithType: SkiaValueAnimator.Seek(float)
  fullName: DrawnUi.Draw.SkiaValueAnimator.Seek(float)
  nameWithType.vb: SkiaValueAnimator.Seek(Single)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.Seek(Single)
  name.vb: Seek(Single)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
    name: Seek
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_
  - name: (
  - uid: System.Single
    name: float
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Seek(System.Single)
    name: Seek
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Seek_System_Single_
  - name: (
  - uid: System.Single
    name: Single
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.single
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.CycleFInished
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.CycleFInished
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_CycleFInished
  name: CycleFInished
  nameWithType: SkiaValueAnimator.CycleFInished
  fullName: DrawnUi.Draw.SkiaValueAnimator.CycleFInished
- uid: DrawnUi.Draw.SkiaValueAnimator.Finished
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Finished
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Finished
  name: Finished
  nameWithType: SkiaValueAnimator.Finished
  fullName: DrawnUi.Draw.SkiaValueAnimator.Finished
- uid: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning
  name: FinishedRunning()
  nameWithType: SkiaValueAnimator.FinishedRunning()
  fullName: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
    name: FinishedRunning
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.FinishedRunning
    name: FinishedRunning
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FinishedRunning
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
  commentId: F:DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_FrameTimeInterpolator
  name: FrameTimeInterpolator
  nameWithType: SkiaValueAnimator.FrameTimeInterpolator
  fullName: DrawnUi.Draw.SkiaValueAnimator.FrameTimeInterpolator
- uid: DrawnUi.Draw.SkiaValueAnimator.UseInterpolator
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.UseInterpolator
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UseInterpolator
  name: UseInterpolator
  nameWithType: SkiaValueAnimator.UseInterpolator
  fullName: DrawnUi.Draw.SkiaValueAnimator.UseInterpolator
- uid: DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_
  name: TickFrame(long)
  nameWithType: SkiaValueAnimator.TickFrame(long)
  fullName: DrawnUi.Draw.SkiaValueAnimator.TickFrame(long)
  nameWithType.vb: SkiaValueAnimator.TickFrame(Long)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.TickFrame(Long)
  name.vb: TickFrame(Long)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
    name: TickFrame
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.TickFrame(System.Int64)
    name: TickFrame
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_TickFrame_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Repeat
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Repeat
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Repeat
  name: Repeat
  nameWithType: SkiaValueAnimator.Repeat
  fullName: DrawnUi.Draw.SkiaValueAnimator.Repeat
- uid: DrawnUi.Draw.SkiaValueAnimator.mValue
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mValue
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mValue
  name: mValue
  nameWithType: SkiaValueAnimator.mValue
  fullName: DrawnUi.Draw.SkiaValueAnimator.mValue
- uid: DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mStartValueIsSet
  name: mStartValueIsSet
  nameWithType: SkiaValueAnimator.mStartValueIsSet
  fullName: DrawnUi.Draw.SkiaValueAnimator.mStartValueIsSet
- uid: DrawnUi.Draw.SkiaValueAnimator.mMaxValue
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mMaxValue
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mMaxValue
  name: mMaxValue
  nameWithType: SkiaValueAnimator.mMaxValue
  fullName: DrawnUi.Draw.SkiaValueAnimator.mMaxValue
- uid: DrawnUi.Draw.SkiaValueAnimator.mMinValue
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.mMinValue
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_mMinValue
  name: mMinValue
  nameWithType: SkiaValueAnimator.mMinValue
  fullName: DrawnUi.Draw.SkiaValueAnimator.mMinValue
- uid: DrawnUi.Draw.SkiaValueAnimator.Easing
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Easing
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Easing
  name: Easing
  nameWithType: SkiaValueAnimator.Easing
  fullName: DrawnUi.Draw.SkiaValueAnimator.Easing
- uid: DrawnUi.Draw.SkiaValueAnimator.Speed
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Speed
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Speed
  name: Speed
  nameWithType: SkiaValueAnimator.Speed
  fullName: DrawnUi.Draw.SkiaValueAnimator.Speed
- uid: DrawnUi.Draw.SkiaValueAnimator.Debug
  commentId: F:DrawnUi.Draw.SkiaValueAnimator.Debug
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Debug
  name: Debug
  nameWithType: SkiaValueAnimator.Debug
  fullName: DrawnUi.Draw.SkiaValueAnimator.Debug
- uid: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds
  name: GetNanoseconds()
  nameWithType: SkiaValueAnimator.GetNanoseconds()
  fullName: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
    name: GetNanoseconds
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.GetNanoseconds
    name: GetNanoseconds
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_GetNanoseconds
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UpdateValue_System_Int64_System_Int64_
  name: UpdateValue(long, long)
  nameWithType: SkiaValueAnimator.UpdateValue(long, long)
  fullName: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(long, long)
  nameWithType.vb: SkiaValueAnimator.UpdateValue(Long, Long)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(Long, Long)
  name.vb: UpdateValue(Long, Long)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
    name: UpdateValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UpdateValue_System_Int64_System_Int64_
  - name: (
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: ','
  - name: " "
  - uid: System.Int64
    name: long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.UpdateValue(System.Int64,System.Int64)
    name: UpdateValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_UpdateValue_System_Int64_System_Int64_
  - name: (
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: ','
  - name: " "
  - uid: System.Int64
    name: Long
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int64
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ElapsedMs
  name: ElapsedMs
  nameWithType: SkiaValueAnimator.ElapsedMs
  fullName: DrawnUi.Draw.SkiaValueAnimator.ElapsedMs
- uid: DrawnUi.Draw.SkiaValueAnimator.Progress
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.Progress
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Progress
  name: Progress
  nameWithType: SkiaValueAnimator.Progress
  fullName: DrawnUi.Draw.SkiaValueAnimator.Progress
- uid: DrawnUi.Draw.SkiaValueAnimator.OnUpdated
  commentId: P:DrawnUi.Draw.SkiaValueAnimator.OnUpdated
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_OnUpdated
  name: OnUpdated
  nameWithType: SkiaValueAnimator.OnUpdated
  fullName: DrawnUi.Draw.SkiaValueAnimator.OnUpdated
- uid: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
  parent: DrawnUi.Draw.SkiaValueAnimator
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ClampOnStart
  name: ClampOnStart()
  nameWithType: SkiaValueAnimator.ClampOnStart()
  fullName: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart()
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
    name: ClampOnStart
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ClampOnStart
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.ClampOnStart
    name: ClampOnStart
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_ClampOnStart
  - name: (
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Start_System_Double_
  name: Start(double)
  nameWithType: SkiaValueAnimator.Start(double)
  fullName: DrawnUi.Draw.SkiaValueAnimator.Start(double)
  nameWithType.vb: SkiaValueAnimator.Start(Double)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.Start(Double)
  name.vb: Start(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
    name: Start
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Start_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.Start(System.Double)
    name: Start
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_Start_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_
  name: SetValue(double)
  nameWithType: SkiaValueAnimator.SetValue(double)
  fullName: DrawnUi.Draw.SkiaValueAnimator.SetValue(double)
  nameWithType.vb: SkiaValueAnimator.SetValue(Double)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.SetValue(Double)
  name.vb: SetValue(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
    name: SetValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetValue(System.Double)
    name: SetValue
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetValue_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
  commentId: M:DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
  parent: DrawnUi.Draw.SkiaValueAnimator
  isExternal: true
  href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_
  name: SetSpeed(double)
  nameWithType: SkiaValueAnimator.SetSpeed(double)
  fullName: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(double)
  nameWithType.vb: SkiaValueAnimator.SetSpeed(Double)
  fullName.vb: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(Double)
  name.vb: SetSpeed(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
    name: SetSpeed
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.SkiaValueAnimator.SetSpeed(System.Double)
    name: SetSpeed
    href: DrawnUi.Draw.SkiaValueAnimator.html#DrawnUi_Draw_SkiaValueAnimator_SetSpeed_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.IsPostAnimator
  commentId: P:DrawnUi.Draw.AnimatorBase.IsPostAnimator
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsPostAnimator
  name: IsPostAnimator
  nameWithType: AnimatorBase.IsPostAnimator
  fullName: DrawnUi.Draw.AnimatorBase.IsPostAnimator
- uid: DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
  commentId: P:DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsHiddenInViewTree
  name: IsHiddenInViewTree
  nameWithType: AnimatorBase.IsHiddenInViewTree
  fullName: DrawnUi.Draw.AnimatorBase.IsHiddenInViewTree
- uid: DrawnUi.Draw.AnimatorBase.Radians(System.Double)
  commentId: M:DrawnUi.Draw.AnimatorBase.Radians(System.Double)
  parent: DrawnUi.Draw.AnimatorBase
  isExternal: true
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_
  name: Radians(double)
  nameWithType: AnimatorBase.Radians(double)
  fullName: DrawnUi.Draw.AnimatorBase.Radians(double)
  nameWithType.vb: AnimatorBase.Radians(Double)
  fullName.vb: DrawnUi.Draw.AnimatorBase.Radians(Double)
  name.vb: Radians(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Radians(System.Double)
    name: Radians
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Radians(System.Double)
    name: Radians
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Radians_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.runDelayMs
  commentId: F:DrawnUi.Draw.AnimatorBase.runDelayMs
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_runDelayMs
  name: runDelayMs
  nameWithType: AnimatorBase.runDelayMs
  fullName: DrawnUi.Draw.AnimatorBase.runDelayMs
- uid: DrawnUi.Draw.AnimatorBase.Register
  commentId: M:DrawnUi.Draw.AnimatorBase.Register
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register
  name: Register()
  nameWithType: AnimatorBase.Register()
  fullName: DrawnUi.Draw.AnimatorBase.Register()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Register
    name: Register
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Register
    name: Register
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Register
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Unregister
  commentId: M:DrawnUi.Draw.AnimatorBase.Unregister
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister
  name: Unregister()
  nameWithType: AnimatorBase.Unregister()
  fullName: DrawnUi.Draw.AnimatorBase.Unregister()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Unregister
    name: Unregister
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Unregister
    name: Unregister
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Unregister
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Cancel
  commentId: M:DrawnUi.Draw.AnimatorBase.Cancel
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel
  name: Cancel()
  nameWithType: AnimatorBase.Cancel()
  fullName: DrawnUi.Draw.AnimatorBase.Cancel()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Cancel
    name: Cancel
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Cancel
    name: Cancel
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Cancel
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Pause
  commentId: M:DrawnUi.Draw.AnimatorBase.Pause
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause
  name: Pause()
  nameWithType: AnimatorBase.Pause()
  fullName: DrawnUi.Draw.AnimatorBase.Pause()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Pause
    name: Pause
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Pause
    name: Pause
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Pause
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.Resume
  commentId: M:DrawnUi.Draw.AnimatorBase.Resume
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume
  name: Resume()
  nameWithType: AnimatorBase.Resume()
  fullName: DrawnUi.Draw.AnimatorBase.Resume()
  spec.csharp:
  - uid: DrawnUi.Draw.AnimatorBase.Resume
    name: Resume
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume
  - name: (
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.AnimatorBase.Resume
    name: Resume
    href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Resume
  - name: (
  - name: )
- uid: DrawnUi.Draw.AnimatorBase.IsPaused
  commentId: P:DrawnUi.Draw.AnimatorBase.IsPaused
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsPaused
  name: IsPaused
  nameWithType: AnimatorBase.IsPaused
  fullName: DrawnUi.Draw.AnimatorBase.IsPaused
- uid: DrawnUi.Draw.AnimatorBase.OnStop
  commentId: P:DrawnUi.Draw.AnimatorBase.OnStop
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_OnStop
  name: OnStop
  nameWithType: AnimatorBase.OnStop
  fullName: DrawnUi.Draw.AnimatorBase.OnStop
- uid: DrawnUi.Draw.AnimatorBase.OnStart
  commentId: P:DrawnUi.Draw.AnimatorBase.OnStart
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_OnStart
  name: OnStart
  nameWithType: AnimatorBase.OnStart
  fullName: DrawnUi.Draw.AnimatorBase.OnStart
- uid: DrawnUi.Draw.AnimatorBase.Parent
  commentId: P:DrawnUi.Draw.AnimatorBase.Parent
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Parent
  name: Parent
  nameWithType: AnimatorBase.Parent
  fullName: DrawnUi.Draw.AnimatorBase.Parent
- uid: DrawnUi.Draw.AnimatorBase.IsDeactivated
  commentId: P:DrawnUi.Draw.AnimatorBase.IsDeactivated
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsDeactivated
  name: IsDeactivated
  nameWithType: AnimatorBase.IsDeactivated
  fullName: DrawnUi.Draw.AnimatorBase.IsDeactivated
- uid: DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
  commentId: P:DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_LastFrameTimeNanos
  name: LastFrameTimeNanos
  nameWithType: AnimatorBase.LastFrameTimeNanos
  fullName: DrawnUi.Draw.AnimatorBase.LastFrameTimeNanos
- uid: DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
  commentId: P:DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_StartFrameTimeNanos
  name: StartFrameTimeNanos
  nameWithType: AnimatorBase.StartFrameTimeNanos
  fullName: DrawnUi.Draw.AnimatorBase.StartFrameTimeNanos
- uid: DrawnUi.Draw.AnimatorBase.Uid
  commentId: P:DrawnUi.Draw.AnimatorBase.Uid
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_Uid
  name: Uid
  nameWithType: AnimatorBase.Uid
  fullName: DrawnUi.Draw.AnimatorBase.Uid
- uid: DrawnUi.Draw.AnimatorBase.IsRunning
  commentId: P:DrawnUi.Draw.AnimatorBase.IsRunning
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_IsRunning
  name: IsRunning
  nameWithType: AnimatorBase.IsRunning
  fullName: DrawnUi.Draw.AnimatorBase.IsRunning
- uid: DrawnUi.Draw.AnimatorBase.WasStarted
  commentId: P:DrawnUi.Draw.AnimatorBase.WasStarted
  parent: DrawnUi.Draw.AnimatorBase
  href: DrawnUi.Draw.AnimatorBase.html#DrawnUi_Draw_AnimatorBase_WasStarted
  name: WasStarted
  nameWithType: AnimatorBase.WasStarted
  fullName: DrawnUi.Draw.AnimatorBase.WasStarted
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.ColorBlendAnimator.#ctor*
  commentId: Overload:DrawnUi.Draw.ColorBlendAnimator.#ctor
  href: DrawnUi.Draw.ColorBlendAnimator.html#DrawnUi_Draw_ColorBlendAnimator__ctor_DrawnUi_Draw_IDrawnBase_
  name: ColorBlendAnimator
  nameWithType: ColorBlendAnimator.ColorBlendAnimator
  fullName: DrawnUi.Draw.ColorBlendAnimator.ColorBlendAnimator
  nameWithType.vb: ColorBlendAnimator.New
  fullName.vb: DrawnUi.Draw.ColorBlendAnimator.New
  name.vb: New
- uid: DrawnUi.Draw.IDrawnBase
  commentId: T:DrawnUi.Draw.IDrawnBase
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.IDrawnBase.html
  name: IDrawnBase
  nameWithType: IDrawnBase
  fullName: DrawnUi.Draw.IDrawnBase
- uid: DrawnUi.Draw.ProgressAnimator.OnProgressChanged(System.Double)
  commentId: M:DrawnUi.Draw.ProgressAnimator.OnProgressChanged(System.Double)
  parent: DrawnUi.Draw.ProgressAnimator
  isExternal: true
  href: DrawnUi.Draw.ProgressAnimator.html#DrawnUi_Draw_ProgressAnimator_OnProgressChanged_System_Double_
  name: OnProgressChanged(double)
  nameWithType: ProgressAnimator.OnProgressChanged(double)
  fullName: DrawnUi.Draw.ProgressAnimator.OnProgressChanged(double)
  nameWithType.vb: ProgressAnimator.OnProgressChanged(Double)
  fullName.vb: DrawnUi.Draw.ProgressAnimator.OnProgressChanged(Double)
  name.vb: OnProgressChanged(Double)
  spec.csharp:
  - uid: DrawnUi.Draw.ProgressAnimator.OnProgressChanged(System.Double)
    name: OnProgressChanged
    href: DrawnUi.Draw.ProgressAnimator.html#DrawnUi_Draw_ProgressAnimator_OnProgressChanged_System_Double_
  - name: (
  - uid: System.Double
    name: double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
  spec.vb:
  - uid: DrawnUi.Draw.ProgressAnimator.OnProgressChanged(System.Double)
    name: OnProgressChanged
    href: DrawnUi.Draw.ProgressAnimator.html#DrawnUi_Draw_ProgressAnimator_OnProgressChanged_System_Double_
  - name: (
  - uid: System.Double
    name: Double
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.double
  - name: )
- uid: DrawnUi.Draw.ColorBlendAnimator.OnProgressChanged*
  commentId: Overload:DrawnUi.Draw.ColorBlendAnimator.OnProgressChanged
  href: DrawnUi.Draw.ColorBlendAnimator.html#DrawnUi_Draw_ColorBlendAnimator_OnProgressChanged_System_Double_
  name: OnProgressChanged
  nameWithType: ColorBlendAnimator.OnProgressChanged
  fullName: DrawnUi.Draw.ColorBlendAnimator.OnProgressChanged
- uid: System.Double
  commentId: T:System.Double
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.double
  name: double
  nameWithType: double
  fullName: double
  nameWithType.vb: Double
  fullName.vb: Double
  name.vb: Double
- uid: DrawnUi.Draw.ColorBlendAnimator.Color*
  commentId: Overload:DrawnUi.Draw.ColorBlendAnimator.Color
  href: DrawnUi.Draw.ColorBlendAnimator.html#DrawnUi_Draw_ColorBlendAnimator_Color
  name: Color
  nameWithType: ColorBlendAnimator.Color
  fullName: DrawnUi.Draw.ColorBlendAnimator.Color
- uid: Microsoft.Maui.Graphics.Color
  commentId: T:Microsoft.Maui.Graphics.Color
  parent: Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  name: Color
  nameWithType: Color
  fullName: Microsoft.Maui.Graphics.Color
- uid: Microsoft.Maui.Graphics
  commentId: N:Microsoft.Maui.Graphics
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui.Graphics
  nameWithType: Microsoft.Maui.Graphics
  fullName: Microsoft.Maui.Graphics
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  - name: .
  - uid: Microsoft.Maui.Graphics
    name: Graphics
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics
- uid: DrawnUi.Draw.ColorBlendAnimator.Color1*
  commentId: Overload:DrawnUi.Draw.ColorBlendAnimator.Color1
  href: DrawnUi.Draw.ColorBlendAnimator.html#DrawnUi_Draw_ColorBlendAnimator_Color1
  name: Color1
  nameWithType: ColorBlendAnimator.Color1
  fullName: DrawnUi.Draw.ColorBlendAnimator.Color1
- uid: DrawnUi.Draw.ColorBlendAnimator.Color2*
  commentId: Overload:DrawnUi.Draw.ColorBlendAnimator.Color2
  href: DrawnUi.Draw.ColorBlendAnimator.html#DrawnUi_Draw_ColorBlendAnimator_Color2
  name: Color2
  nameWithType: ColorBlendAnimator.Color2
  fullName: DrawnUi.Draw.ColorBlendAnimator.Color2
- uid: DrawnUi.Draw.ColorBlendAnimator.OnColorChanged*
  commentId: Overload:DrawnUi.Draw.ColorBlendAnimator.OnColorChanged
  href: DrawnUi.Draw.ColorBlendAnimator.html#DrawnUi_Draw_ColorBlendAnimator_OnColorChanged
  name: OnColorChanged
  nameWithType: ColorBlendAnimator.OnColorChanged
  fullName: DrawnUi.Draw.ColorBlendAnimator.OnColorChanged
- uid: System.Action{Microsoft.Maui.Graphics.Color}
  commentId: T:System.Action{Microsoft.Maui.Graphics.Color}
  parent: System
  definition: System.Action`1
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<Color>
  nameWithType: Action<Color>
  fullName: System.Action<Microsoft.Maui.Graphics.Color>
  nameWithType.vb: Action(Of Color)
  fullName.vb: System.Action(Of Microsoft.Maui.Graphics.Color)
  name.vb: Action(Of Color)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - uid: Microsoft.Maui.Graphics.Color
    name: Color
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - uid: Microsoft.Maui.Graphics.Color
    name: Color
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui.graphics.color
  - name: )
- uid: System.Action`1
  commentId: T:System.Action`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<T>
  nameWithType: Action<T>
  fullName: System.Action<T>
  nameWithType.vb: Action(Of T)
  fullName.vb: System.Action(Of T)
  name.vb: Action(Of T)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.ColorBlendAnimator.GetColor*
  commentId: Overload:DrawnUi.Draw.ColorBlendAnimator.GetColor
  href: DrawnUi.Draw.ColorBlendAnimator.html#DrawnUi_Draw_ColorBlendAnimator_GetColor_Microsoft_Maui_Graphics_Color_Microsoft_Maui_Graphics_Color_System_Single_
  name: GetColor
  nameWithType: ColorBlendAnimator.GetColor
  fullName: DrawnUi.Draw.ColorBlendAnimator.GetColor
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.ColorBlendAnimator.AnimateAsync*
  commentId: Overload:DrawnUi.Draw.ColorBlendAnimator.AnimateAsync
  href: DrawnUi.Draw.ColorBlendAnimator.html#DrawnUi_Draw_ColorBlendAnimator_AnimateAsync_System_Action_Microsoft_Maui_Graphics_Color__Microsoft_Maui_Graphics_Color_Microsoft_Maui_Graphics_Color_System_Double_Microsoft_Maui_Easing_System_Threading_CancellationTokenSource_
  name: AnimateAsync
  nameWithType: ColorBlendAnimator.AnimateAsync
  fullName: DrawnUi.Draw.ColorBlendAnimator.AnimateAsync
- uid: Microsoft.Maui.Easing
  commentId: T:Microsoft.Maui.Easing
  parent: Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft.maui.easing
  name: Easing
  nameWithType: Easing
  fullName: Microsoft.Maui.Easing
- uid: System.Threading.CancellationTokenSource
  commentId: T:System.Threading.CancellationTokenSource
  parent: System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.cancellationtokensource
  name: CancellationTokenSource
  nameWithType: CancellationTokenSource
  fullName: System.Threading.CancellationTokenSource
- uid: System.Threading.Tasks.Task
  commentId: T:System.Threading.Tasks.Task
  parent: System.Threading.Tasks
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.task
  name: Task
  nameWithType: Task
  fullName: System.Threading.Tasks.Task
- uid: Microsoft.Maui
  commentId: N:Microsoft.Maui
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/microsoft
  name: Microsoft.Maui
  nameWithType: Microsoft.Maui
  fullName: Microsoft.Maui
  spec.csharp:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
  spec.vb:
  - uid: Microsoft
    name: Microsoft
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft
  - name: .
  - uid: Microsoft.Maui
    name: Maui
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/microsoft.maui
- uid: System.Threading
  commentId: N:System.Threading
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading
  nameWithType: System.Threading
  fullName: System.Threading
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
- uid: System.Threading.Tasks
  commentId: N:System.Threading.Tasks
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Threading.Tasks
  nameWithType: System.Threading.Tasks
  fullName: System.Threading.Tasks
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Threading
    name: Threading
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading
  - name: .
  - uid: System.Threading.Tasks
    name: Tasks
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks
