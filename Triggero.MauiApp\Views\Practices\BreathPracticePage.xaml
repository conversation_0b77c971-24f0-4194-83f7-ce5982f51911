﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.BreathPracticePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:extensions="http://xamarin.com/schemas/2020/toolkit"
    
    
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="this"
    BackgroundColor="White">
    <ContentPage.Content>

        <Grid
            x:Name="ContentStack"
            HorizontalOptions="FillAndExpand"
            Opacity="0.001"
            VerticalOptions="FillAndExpand">

            <!--<ContentView
                VerticalOptions="FillAndExpand"
                HorizontalOptions="FillAndExpand"
                x:Name="PlayerContainer"/>-->

            <draw:Canvas
                x:Name="DrawnCanvas"
                BackgroundColor="CornflowerBlue"
                HorizontalOptions="Fill"
                VerticalOptions="Fill">

                <draw:SkiaLayout
                    HorizontalOptions="Fill"
                    VerticalOptions="Fill">

                    <draw:SkiaImage
                        Aspect="Fill"
                        HorizontalOptions="Fill"
                        Source="resource://Resources.Images.breath.jpg"
                        VerticalOptions="Fill" />

                    <draw:SkiaLottie
                        x:Name="BreathAnimation"
                        AutoPlay="False"
                        HeightRequest="160"
                        HorizontalOptions="Center"
                        LockRatio="1"
                        Repeat="-1"
                        Source="Lottie/breath.json"
                        SpeedRatio="1.0"
                        VerticalOptions="Center" />

                </draw:SkiaLayout>

            </draw:Canvas>

            <Grid
                Margin="{x:OnPlatform Android='0,50,8,0',
                                    iOS='0,50,8,0'}"
                HeightRequest="55"
                HorizontalOptions="End"
                VerticalOptions="Start"
                WidthRequest="55">
                <Grid.GestureRecognizers>
                    <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=Close}" />
                </Grid.GestureRecognizers>

                <ImageButton
                    BackgroundColor="Transparent"
                    Command="{Binding Source={x:Reference this}, Path=Close}"
                    CornerRadius="0"
                    HeightRequest="17"
                    HorizontalOptions="Center"
                    Source="closeWhite.png"
                    VerticalOptions="Center"
                    WidthRequest="17" />

            </Grid>

            <Label
                x:Name="counterLabel"
                Margin="0,-170,0,0"
                FontSize="48"
                HorizontalOptions="Center"
                IsVisible="False"
                Text=""
                TextColor="#FFFFFF"
                VerticalOptions="Center" />

            <StackLayout
                Margin="0,0,0,170"
                HorizontalOptions="Center"
                Spacing="12"
                VerticalOptions="End">
                <Label
                    x:Name="actionLabel"
                    FontSize="{x:OnPlatform Android=17,
                                          iOS=17}"
                    HorizontalOptions="Center"
                    Text="Сделай глубокий вдох"
                    TextColor="#FFFFFF"
                    VerticalOptions="Start" />
                <Label
                    x:Name="timeLabel"
                    FontSize="{x:OnPlatform Android=17,
                                          iOS=17}"
                    HorizontalOptions="Center"
                    Opacity="0.5"
                    Text="0:59"
                    TextColor="#FFFFFF"
                    VerticalOptions="Start" />
            </StackLayout>

        </Grid>
    </ContentPage.Content>
</ContentPage>