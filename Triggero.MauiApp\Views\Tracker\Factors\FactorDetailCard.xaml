﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Name="this"
             x:Class="Triggero.Controls.Cards.Tracker.Factors.FactorDetailCard">
  <ContentView.Content>
      <Frame 
          CornerRadius="10"
          BackgroundColor="Transparent"
          HasShadow="False"
          Padding="0">
            <Frame.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Frame.GestureRecognizers>
            <Frame.Style>
                <Style TargetType="Frame">
                    <Style.Triggers>
                        <DataTrigger TargetType="Frame" Binding="{Binding Source={x:Reference this},Path=IsSelected}" Value="True">
                            <Setter Property="BackgroundColor" Value="#F8F9FC" />
                            <Setter Property="BorderColor" Value="#CEE3F4" />
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Frame.Style>
            <Grid>
                <StackLayout
                    HorizontalOptions="Center"
                    VerticalOptions="Start"
                    Spacing="6">
                    <Image 
                        Margin="0,5,0,0"
                        x:Name="img"
                        HeightRequest="48"
                        WidthRequest="48"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"/>
                    <Label 
                        x:Name="titleLabel"
                        Margin="3,0,3,0"
                        TextColor="Black"
                        FontSize="{x:OnPlatform Android=10,iOS=11}"
                        HorizontalTextAlignment="Center"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"/>
                </StackLayout>
            </Grid>
      </Frame>
  </ContentView.Content>
</ContentView>