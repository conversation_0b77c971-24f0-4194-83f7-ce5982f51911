### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaLayout.StructureChange
  commentId: T:DrawnUi.Draw.SkiaLayout.StructureChange
  id: SkiaLayout.StructureChange
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaLayout.StructureChange.Count
  - DrawnUi.Draw.SkiaLayout.StructureChange.InsertAtIndex
  - DrawnUi.Draw.SkiaLayout.StructureChange.IsInsertOperation
  - DrawnUi.Draw.SkiaLayout.StructureChange.IsVisible
  - DrawnUi.Draw.SkiaLayout.StructureChange.Items
  - DrawnUi.Draw.SkiaLayout.StructureChange.MeasuredItems
  - DrawnUi.Draw.SkiaLayout.StructureChange.OffsetOthers
  - DrawnUi.Draw.SkiaLayout.StructureChange.StartIndex
  - DrawnUi.Draw.SkiaLayout.StructureChange.StartingPosition
  - DrawnUi.Draw.SkiaLayout.StructureChange.TargetIndex
  - DrawnUi.Draw.SkiaLayout.StructureChange.Type
  langs:
  - csharp
  - vb
  name: SkiaLayout.StructureChange
  nameWithType: SkiaLayout.StructureChange
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StructureChange
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 85
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public class SkiaLayout.StructureChange
    content.vb: Public Class SkiaLayout.StructureChange
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.Type
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.Type
  id: Type
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: Type
  nameWithType: SkiaLayout.StructureChange.Type
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.Type
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Type
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 91
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SkiaLayout.StructureChangeType Type { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaLayout.StructureChangeType
    content.vb: Public Property Type As SkiaLayout.StructureChangeType
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.Type*
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.OffsetOthers
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.OffsetOthers
  id: OffsetOthers
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: OffsetOthers
  nameWithType: SkiaLayout.StructureChange.OffsetOthers
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.OffsetOthers
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: OffsetOthers
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 92
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public Vector2? OffsetOthers { get; set; }
    parameters: []
    return:
      type: System.Nullable{System.Numerics.Vector2}
    content.vb: Public Property OffsetOthers As Vector2?
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.OffsetOthers*
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.StartIndex
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.StartIndex
  id: StartIndex
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: StartIndex
  nameWithType: SkiaLayout.StructureChange.StartIndex
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.StartIndex
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartIndex
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 93
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int StartIndex { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property StartIndex As Integer
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.StartIndex*
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.Count
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.Count
  id: Count
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: Count
  nameWithType: SkiaLayout.StructureChange.Count
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.Count
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Count
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 94
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int Count { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property Count As Integer
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.Count*
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.Items
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.Items
  id: Items
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: Items
  nameWithType: SkiaLayout.StructureChange.Items
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.Items
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Items
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 95
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public List<object> Items { get; set; }
    parameters: []
    return:
      type: System.Collections.Generic.List{System.Object}
    content.vb: Public Property Items As List(Of Object)
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.Items*
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.TargetIndex
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.TargetIndex
  id: TargetIndex
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: TargetIndex
  nameWithType: SkiaLayout.StructureChange.TargetIndex
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.TargetIndex
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: TargetIndex
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 96
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int TargetIndex { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property TargetIndex As Integer
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.TargetIndex*
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.MeasuredItems
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.MeasuredItems
  id: MeasuredItems
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: MeasuredItems
  nameWithType: SkiaLayout.StructureChange.MeasuredItems
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.MeasuredItems
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: MeasuredItems
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 97
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public List<SkiaLayout.MeasuredItemInfo> MeasuredItems { get; set; }
    parameters: []
    return:
      type: System.Collections.Generic.List{DrawnUi.Draw.SkiaLayout.MeasuredItemInfo}
    content.vb: Public Property MeasuredItems As List(Of SkiaLayout.MeasuredItemInfo)
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.MeasuredItems*
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.InsertAtIndex
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.InsertAtIndex
  id: InsertAtIndex
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: InsertAtIndex
  nameWithType: SkiaLayout.StructureChange.InsertAtIndex
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.InsertAtIndex
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: InsertAtIndex
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 98
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int? InsertAtIndex { get; set; }
    parameters: []
    return:
      type: System.Nullable{System.Int32}
    content.vb: Public Property InsertAtIndex As Integer?
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.InsertAtIndex*
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.IsInsertOperation
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.IsInsertOperation
  id: IsInsertOperation
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: IsInsertOperation
  nameWithType: SkiaLayout.StructureChange.IsInsertOperation
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.IsInsertOperation
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsInsertOperation
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 99
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsInsertOperation { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsInsertOperation As Boolean
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.IsInsertOperation*
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.IsVisible
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.IsVisible
  id: IsVisible
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: IsVisible
  nameWithType: SkiaLayout.StructureChange.IsVisible
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.IsVisible
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsVisible
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 100
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsVisible { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsVisible As Boolean
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.IsVisible*
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.StartingPosition
  commentId: P:DrawnUi.Draw.SkiaLayout.StructureChange.StartingPosition
  id: StartingPosition
  parent: DrawnUi.Draw.SkiaLayout.StructureChange
  langs:
  - csharp
  - vb
  name: StartingPosition
  nameWithType: SkiaLayout.StructureChange.StartingPosition
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.StartingPosition
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartingPosition
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 103
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SkiaLayout.BackgroundMeasurementStartingPosition StartingPosition { get; set; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
    content.vb: Public Property StartingPosition As SkiaLayout.BackgroundMeasurementStartingPosition
  overload: DrawnUi.Draw.SkiaLayout.StructureChange.StartingPosition*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.Type*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.Type
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_Type
  name: Type
  nameWithType: SkiaLayout.StructureChange.Type
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.Type
- uid: DrawnUi.Draw.SkiaLayout.StructureChangeType
  commentId: T:DrawnUi.Draw.SkiaLayout.StructureChangeType
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLayout.html
  name: SkiaLayout.StructureChangeType
  nameWithType: SkiaLayout.StructureChangeType
  fullName: DrawnUi.Draw.SkiaLayout.StructureChangeType
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.StructureChangeType
    name: StructureChangeType
    href: DrawnUi.Draw.SkiaLayout.StructureChangeType.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.StructureChangeType
    name: StructureChangeType
    href: DrawnUi.Draw.SkiaLayout.StructureChangeType.html
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.OffsetOthers*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.OffsetOthers
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_OffsetOthers
  name: OffsetOthers
  nameWithType: SkiaLayout.StructureChange.OffsetOthers
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.OffsetOthers
- uid: System.Nullable{System.Numerics.Vector2}
  commentId: T:System.Nullable{System.Numerics.Vector2}
  parent: System
  definition: System.Nullable`1
  href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  name: Vector2?
  nameWithType: Vector2?
  fullName: System.Numerics.Vector2?
  spec.csharp:
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: '?'
  spec.vb:
  - uid: System.Numerics.Vector2
    name: Vector2
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.numerics.vector2
  - name: '?'
- uid: System.Nullable`1
  commentId: T:System.Nullable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  name: Nullable<T>
  nameWithType: Nullable<T>
  fullName: System.Nullable<T>
  nameWithType.vb: Nullable(Of T)
  fullName.vb: System.Nullable(Of T)
  name.vb: Nullable(Of T)
  spec.csharp:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.StartIndex*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.StartIndex
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_StartIndex
  name: StartIndex
  nameWithType: SkiaLayout.StructureChange.StartIndex
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.StartIndex
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.Count*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.Count
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_Count
  name: Count
  nameWithType: SkiaLayout.StructureChange.Count
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.Count
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.Items*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.Items
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_Items
  name: Items
  nameWithType: SkiaLayout.StructureChange.Items
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.Items
- uid: System.Collections.Generic.List{System.Object}
  commentId: T:System.Collections.Generic.List{System.Object}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<object>
  nameWithType: List<object>
  fullName: System.Collections.Generic.List<object>
  nameWithType.vb: List(Of Object)
  fullName.vb: System.Collections.Generic.List(Of Object)
  name.vb: List(Of Object)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Collections.Generic.List`1
  commentId: T:System.Collections.Generic.List`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<T>
  nameWithType: List<T>
  fullName: System.Collections.Generic.List<T>
  nameWithType.vb: List(Of T)
  fullName.vb: System.Collections.Generic.List(Of T)
  name.vb: List(Of T)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: System.Collections.Generic
  commentId: N:System.Collections.Generic
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Collections.Generic
  nameWithType: System.Collections.Generic
  fullName: System.Collections.Generic
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Collections
    name: Collections
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections
  - name: .
  - uid: System.Collections.Generic
    name: Generic
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.TargetIndex*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.TargetIndex
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_TargetIndex
  name: TargetIndex
  nameWithType: SkiaLayout.StructureChange.TargetIndex
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.TargetIndex
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.MeasuredItems*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.MeasuredItems
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_MeasuredItems
  name: MeasuredItems
  nameWithType: SkiaLayout.StructureChange.MeasuredItems
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.MeasuredItems
- uid: System.Collections.Generic.List{DrawnUi.Draw.SkiaLayout.MeasuredItemInfo}
  commentId: T:System.Collections.Generic.List{DrawnUi.Draw.SkiaLayout.MeasuredItemInfo}
  parent: System.Collections.Generic
  definition: System.Collections.Generic.List`1
  href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  name: List<SkiaLayout.MeasuredItemInfo>
  nameWithType: List<SkiaLayout.MeasuredItemInfo>
  fullName: System.Collections.Generic.List<DrawnUi.Draw.SkiaLayout.MeasuredItemInfo>
  nameWithType.vb: List(Of SkiaLayout.MeasuredItemInfo)
  fullName.vb: System.Collections.Generic.List(Of DrawnUi.Draw.SkiaLayout.MeasuredItemInfo)
  name.vb: List(Of SkiaLayout.MeasuredItemInfo)
  spec.csharp:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: <
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo
    name: MeasuredItemInfo
    href: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.html
  - name: '>'
  spec.vb:
  - uid: System.Collections.Generic.List`1
    name: List
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.collections.generic.list-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo
    name: MeasuredItemInfo
    href: DrawnUi.Draw.SkiaLayout.MeasuredItemInfo.html
  - name: )
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.InsertAtIndex*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.InsertAtIndex
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_InsertAtIndex
  name: InsertAtIndex
  nameWithType: SkiaLayout.StructureChange.InsertAtIndex
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.InsertAtIndex
- uid: System.Nullable{System.Int32}
  commentId: T:System.Nullable{System.Int32}
  parent: System
  definition: System.Nullable`1
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int?
  nameWithType: int?
  fullName: int?
  nameWithType.vb: Integer?
  fullName.vb: Integer?
  name.vb: Integer?
  spec.csharp:
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: '?'
  spec.vb:
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: '?'
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.IsInsertOperation*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.IsInsertOperation
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_IsInsertOperation
  name: IsInsertOperation
  nameWithType: SkiaLayout.StructureChange.IsInsertOperation
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.IsInsertOperation
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.IsVisible*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.IsVisible
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_IsVisible
  name: IsVisible
  nameWithType: SkiaLayout.StructureChange.IsVisible
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.IsVisible
- uid: DrawnUi.Draw.SkiaLayout.StructureChange.StartingPosition*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.StructureChange.StartingPosition
  href: DrawnUi.Draw.SkiaLayout.StructureChange.html#DrawnUi_Draw_SkiaLayout_StructureChange_StartingPosition
  name: StartingPosition
  nameWithType: SkiaLayout.StructureChange.StartingPosition
  fullName: DrawnUi.Draw.SkiaLayout.StructureChange.StartingPosition
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  commentId: T:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaLayout.html
  name: SkiaLayout.BackgroundMeasurementStartingPosition
  nameWithType: SkiaLayout.BackgroundMeasurementStartingPosition
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
  spec.csharp:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
    name: BackgroundMeasurementStartingPosition
    href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.html
  spec.vb:
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition
    name: BackgroundMeasurementStartingPosition
    href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementStartingPosition.html
