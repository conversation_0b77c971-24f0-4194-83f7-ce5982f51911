﻿using MobileAPIWrapper;
using MobileAPIWrapper.Methods.General.Users;
using System.Text.Json;
using System.Net.Http;

using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Triggero.MauiMobileApp.Enums;
using Triggero.MauiMobileApp.Extensions.Helpers;
using Triggero.Models;
using Triggero.Models.General;
using Triggero.Models.General.Influence;
using Triggero.Models.General.UserStats;
using Triggero.Models.Localization;
using Triggero.Models.MoodTracker;
using Triggero.Models.MoodTracker.User;
using Triggero.Models.Plans;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;
using Triggero.Models.Tests;
using Triggero.MauiMobileApp.Helpers;

namespace Triggero.MauiMobileApp.Extensions.Helpers.Modules
{
    /// <summary>
    /// Класс для хранения и выгрузки данных, если нет интернета
    /// </summary>
    public class SavedData
    {
        public static string FilePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + @"/savedData.json";

        public List<UserAvatar> Avatars { get; private set; } = new List<UserAvatar>();
        public List<NeedToHandle> NeedToHandleItems { get; private set; } = new List<NeedToHandle>();

        public InterfaceLocalization InterfaceLocalization { get; set; }


        #region Categories 
        public List<ExerciseCategory> ExerciseCategories { get; private set; } = new List<ExerciseCategory>();
        public List<TopicCategory> TopicCategories { get; private set; } = new List<TopicCategory>();
        public List<PracticeCategory> PracticeCategories { get; private set; } = new List<PracticeCategory>();
        public List<TestCategory> TestCategories { get; private set; } = new List<TestCategory>();

        public async Task<List<ExerciseCategory>> GetExerciseCategories()
        {
            if (ConnectionHelper.HasInternet())
            {
                ExerciseCategories = await TriggeroMobileAPI.LibraryMethods.ExercisesMethods.GetExerciseCategories();
                //SaveChangesToMemory();
            }
            return ExerciseCategories;
        }
        public async Task<List<TopicCategory>> GetTopicCategories()
        {
            if (ConnectionHelper.HasInternet())
            {
                TopicCategories = await TriggeroMobileAPI.LibraryMethods.TopicsMethods.GetTopicCategories();
                //SaveChangesToMemory();
            }
            return TopicCategories;
        }
        public async Task<List<PracticeCategory>> GetPracticeCategories()
        {
            if (ConnectionHelper.HasInternet())
            {
                PracticeCategories = await TriggeroMobileAPI.LibraryMethods.PracticesMethods.GetPracticeCategories();
                //SaveChangesToMemory();
            }
            return PracticeCategories;
        }
        public async Task<List<TestCategory>> GetTestCategories()
        {
            if (ConnectionHelper.HasInternet())
            {
                TestCategories = await TriggeroMobileAPI.TestsMethods.GetTestCategories();
                //SaveChangesToMemory();
            }
            return TestCategories;
        }

        #endregion

        #region Content
        public List<Exercise> Exercises { get; private set; } = new List<Exercise>();
        public List<Topic> Topics { get; private set; } = new List<Topic>();
        public List<Practice> Practices { get; private set; } = new List<Practice>();
        public List<Test> Tests { get; private set; } = new List<Test>();

        public Test TutorialTest { get; private set; } = new Test();

        public List<BreathPractice> BreathPractices { get; private set; } = new List<BreathPractice>();

        [System.Text.Json.Serialization.JsonIgnore]
        private bool isExercisesLoadedFromApi = false;
        public async Task<List<Exercise>> GetExercises(int catId = 0)
        {
            if (ConnectionHelper.HasInternet())
            {
                if (catId == 0)
                {
                    if (!isExercisesLoadedFromApi)
                    {
                        Exercises = await TriggeroMobileAPI.LibraryMethods.ExercisesMethods.GetExercises();
                        isExercisesLoadedFromApi = true;
                    }
                }
                else
                {
                    if (!isExercisesLoadedFromApi)
                    {
                        Exercises = await TriggeroMobileAPI.LibraryMethods.ExercisesMethods.GetExercisesByCategory(catId);
                    }
                    else
                    {
                        return Exercises.Where(o => o.CategoryId == catId).ToList();
                    }
                }
                //SaveChangesToMemory();
            }
            return Exercises;
        }


        [System.Text.Json.Serialization.JsonIgnore]
        private bool isPracticesLoadedFromApi = false;
        public async Task<List<Practice>> GetPractices(int catId = 0)
        {
            if (ConnectionHelper.HasInternet())
            {
                if (catId == 0)
                {
                    if (!isPracticesLoadedFromApi)
                    {
                        Practices = await TriggeroMobileAPI.LibraryMethods.PracticesMethods.GetPractices();
                        isPracticesLoadedFromApi = true;
                    }
                }
                else
                {
                    if (!isPracticesLoadedFromApi)
                    {
                        Practices = await TriggeroMobileAPI.LibraryMethods.PracticesMethods.GetPracticesByCategory(catId);
                    }
                    else
                    {
                        return Practices.Where(o => o.CategoryId == catId).ToList();
                    }
                }
                //SaveChangesToMemory();
            }
            return Practices;
        }


        [System.Text.Json.Serialization.JsonIgnore]
        private bool isTopicsLoadedFromApi = false;
        public async Task<List<Topic>> GetTopics(int catId = 0)
        {
            if (ConnectionHelper.HasInternet())
            {
                if (catId == 0)
                {
                    if (!isTopicsLoadedFromApi)
                    {
                        Topics = await TriggeroMobileAPI.LibraryMethods.TopicsMethods.GetTopics();
                        isTopicsLoadedFromApi = true;
                    }
                }
                else
                {
                    if (!isTopicsLoadedFromApi)
                    {
                        Topics = await TriggeroMobileAPI.LibraryMethods.TopicsMethods.GetTopicsByCategory(catId);
                    }
                    else
                    {
                        return Topics.Where(o => o.CategoryId == catId).ToList();
                    }
                }
                //SaveChangesToMemory();
            }
            return Topics;
        }



        [System.Text.Json.Serialization.JsonIgnore]
        private bool isTestsLoadedFromApi = false;
        public async Task<List<Test>> GetTests(int catId = 0)
        {
            if (ConnectionHelper.HasInternet())
            {
                if (catId == 0)
                {
                    if (!isTestsLoadedFromApi)
                    {
                        Tests = (await TriggeroMobileAPI.TestsMethods.GetTests(1000, 0)).GetTestsFromJson();
                        isTestsLoadedFromApi = true;
                    }
                }
                else
                {
                    if (!isTestsLoadedFromApi)
                    {
                        Tests = await TriggeroMobileAPI.TestsMethods.GetTestsByCategory(catId);
                    }
                    else
                    {
                        return Tests.Where(o => o.CategoryId == catId).ToList();
                    }
                }
                //SaveChangesToMemory();
            }

            return Tests;
        }



        public async Task<Test> GetTutorialTest()
        {
            if (ConnectionHelper.HasInternet())
            {
                TutorialTest = await TriggeroMobileAPI.TestsMethods.GetTutorialTest();
                //SaveChangesToMemory();
            }
            return TutorialTest;
        }


        public async Task<List<BreathPractice>> GetBreathPractices()
        {
            if (ConnectionHelper.HasInternet())
            {
                BreathPractices = await TriggeroMobileAPI.LibraryMethods.BreathPractices.GetBreathPractices();
                //SaveChangesToMemory();
            }
            return BreathPractices;
        }
        public async Task<BreathPractice> GetRandomBreathPractice()
        {
            var practices = await GetBreathPractices();
            if (practices.Count > 0)
            {
                return practices[new Random().Next(0, BreathPractices.Count - 1)];
            }
            return null;
        }

        #endregion


        #region Mood tracker 
        public List<Factor> Factors { get; private set; } = new List<Factor>();
        public List<FactorDetail> FactorDetails { get; private set; } = new List<FactorDetail>();

        public async Task<List<Factor>> GetFactors()
        {
            if (ConnectionHelper.HasInternet())
            {
                Factors = await TriggeroMobileAPI.Common.GetFactors();
                //SaveChangesToMemory();
            }
            return Factors;
        }
        public async Task<List<FactorDetail>> GetFactorDetails()
        {
            if (ConnectionHelper.HasInternet())
            {
                FactorDetails = await TriggeroMobileAPI.Common.GetFactorDetails();
                //SaveChangesToMemory();
            }
            return FactorDetails;
        }
        #endregion

        #region Mood tracker item

        public List<MoodtrackerItem> MoodtrackerItems { get; private set; } = new List<MoodtrackerItem>();
        public async Task AddMoodTrackerItem(MoodtrackerItem item)
        {
            if (ConnectionHelper.HasInternet())
            {
                await TriggeroMobileAPI.GeneralMethods.UserMethods.UserMoodTrackerMethods.AddMoodTrackerItem(AuthHelper.UserId, item);
            }
            else
            {
                string url = UserMoodTrackerMethods.BASE_HOST + $"AddMoodTrackerItem?userId={AuthHelper.UserId}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put));
            }
            MoodtrackerItems.Add(item);
            //ApplicationState.SaveToFile(ApplicationState.ConfigData, ConfigData.FilePath);
        }
        public async Task<List<MoodtrackerItem>> GetMoodtrackerItemsAtPeriod(DateTime from, DateTime to)
        {
            if (ConnectionHelper.HasInternet())
            {
                var items = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserMoodTrackerMethods.GetMoodTrackerItemsByPeriod(AuthHelper.UserId, from, to);
                AddNewItems(items);

                //ApplicationState.SaveToFile(ApplicationState.ConfigData, ConfigData.FilePath);
            }
            return MoodtrackerItems.Where(o => o.Date >= from && o.Date <= to).ToList();
        }
        public async Task<List<MoodtrackerItem>> GetMoodtrackerItems()
        {
            if (ConnectionHelper.HasInternet())
            {
                MoodtrackerItems = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserMoodTrackerMethods.GetMoodTrackerItems(AuthHelper.UserId);
                //ApplicationState.SaveToFile(ApplicationState.ConfigData, ConfigData.FilePath);
            }
            return MoodtrackerItems.ToList();
        }


        private void AddNewItems(List<MoodtrackerItem> items)
        {
            foreach (var item in items)
            {
                if (!MoodtrackerItems.Any(o => o.GlobalId == item.GlobalId))
                {
                    MoodtrackerItems.Add(item);
                }
            }
        }

        #endregion

        #region Mood tracker notes
        public List<MoodtrackerNote> MoodtrackerNotes { get; private set; } = new List<MoodtrackerNote>();
        public async Task<List<MoodtrackerNote>> GetMoodtrackerNotes()
        {
            if (ConnectionHelper.HasInternet())
            {
                MoodtrackerNotes = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserMoodTrackerMethods.GetMoodTrackerNotes(AuthHelper.UserId);
                //ApplicationState.SaveToFile(ApplicationState.ConfigData, ConfigData.FilePath);
            }
            return MoodtrackerNotes.ToList();
        }

        public async Task AddMoodTrackerNote(string text)
        {
            if (ConnectionHelper.HasInternet())
            {
                var newItem = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserMoodTrackerMethods.AddMoodtrackerNote(AuthHelper.UserId, text);
                MoodtrackerNotes.Add(newItem);
                //ApplicationState.SaveToFile(ApplicationState.ConfigData, ConfigData.FilePath);

                GlobalEvents.OnNoteChanged(newItem);
            }
            else
            {
                string url = UserMoodTrackerMethods.BASE_HOST + $"AddMoodtrackerNote?userId={AuthHelper.UserId}&text={text}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Post));
            }
        }
        public async Task UpdateMoodTrackerNote(int id, string text)
        {
            if (ConnectionHelper.HasInternet())
            {
                var updated = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserMoodTrackerMethods.UpdateMoodtrackerNote(id, text);

                MoodtrackerNotes.Remove(MoodtrackerNotes.FirstOrDefault(o => o.GlobalId == updated.GlobalId));
                MoodtrackerNotes.Add(updated);

                GlobalEvents.OnNoteChanged(updated);
            }
            else
            {
                string url = UserMoodTrackerMethods.BASE_HOST + $"UpdateMoodtrackerNote?id={id}&text={text}";
                ApplicationState.PostponedRequests.AddRequest(new PostponedRequestModel(url, HttpMethod.Put));

                var found = MoodtrackerNotes.FirstOrDefault(o => o.Id == id);
                found.Text = text;
                GlobalEvents.OnNoteChanged(found);
            }

            //ApplicationState.SaveToFile(ApplicationState.ConfigData, ConfigData.FilePath);
        }

        #endregion


        #region Plans
        public List<Plan> Plans { get; private set; } = new List<Plan>();
        public List<PlanOption> PlanOptions { get; private set; } = new List<PlanOption>();

        public async Task<List<Plan>> GetPlans()
        {
            if (ConnectionHelper.HasInternet())
            {
                Plans = await TriggeroMobileAPI.GeneralMethods.UserMethods.SubscriptionMethods.GetSubscriptions();
                //SaveChangesToMemory();
            }
            return Plans;
        }
        public async Task<List<PlanOption>> GetPlansOptions()
        {
            if (ConnectionHelper.HasInternet())
            {
                PlanOptions = await TriggeroMobileAPI.GeneralMethods.UserMethods.SubscriptionMethods.GetSubscriptionOptions();
                //SaveChangesToMemory();
            }
            return PlanOptions;
        }

        #endregion

        #region SearchPageQueries
        public Dictionary<string, int> SearchPageQueries { get; set; } = new Dictionary<string, int>();

        public void AddSearchPageQuery(string query)
        {
            if (!SearchPageQueries.ContainsKey(query))
            {
                SearchPageQueries.Add(query, 1);
            }
            else
            {
                SearchPageQueries[query] = SearchPageQueries[query] + 1;
            }
            //SaveChangesToMemory();
        }
        public List<string> GetOftenSearchPageQueries()
        {
            var items = new List<string>();

            var queries = SearchPageQueries.OrderByDescending(o => o.Value).Take(4);
            foreach (var query in queries)
            {
                items.Add(query.Key);
            }

            return items;
        }
        #endregion


        #region Stats
        public List<TestPassingResult> TestPassingResults { get; private set; } = new List<TestPassingResult>();
        public List<ExercisePassingResult> ExercisePassingResults { get; private set; } = new List<ExercisePassingResult>();
        public async Task<List<TestPassingResult>> GetTestPassingResults()
        {
            if (ConnectionHelper.HasInternet())
            {
                TestPassingResults = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserStatisticsMethods.GetPassedTests(AuthHelper.UserId);
                //SaveChangesToMemory();
            }
            return TestPassingResults;
        }
        public async Task<List<ExercisePassingResult>> GetExercisePassingResults()
        {
            if (ConnectionHelper.HasInternet())
            {
                ExercisePassingResults = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserStatisticsMethods.GetPassedExercises(AuthHelper.UserId);
                //SaveChangesToMemory();
            }
            return ExercisePassingResults;
        }

        #endregion

        public async Task<List<UserAvatar>> GetAvatars()
        {
            if (ConnectionHelper.HasInternet())
            {
                Avatars = await TriggeroMobileAPI.Common.GetAvatars();
                //SaveChangesToMemory();
            }
            return Avatars;
        }
        public async Task<List<NeedToHandle>> GetWhatNeedToHandle()
        {
            if (ConnectionHelper.HasInternet())
            {
                NeedToHandleItems = await TriggeroMobileAPI.Common.GetWhatNeedToHandle(AuthHelper.UserId);
                //SaveChangesToMemory();
            }

            return NeedToHandleItems;
        }
        public async Task<InterfaceLocalization> GetInterfaceLocalization()
        {
            if (ConnectionHelper.HasInternet())
            {
                InterfaceLocalization = await TriggeroMobileAPI.Common.GetInterfaceLocalization(LanguageHelper.LangCode);
                //SaveChangesToMemory();
            }
            return InterfaceLocalization;
        }


        public RecommendationModel GetRandomRecommendation()
        {
            int _counter = 0;
            var rnd = new Random();

            while (true)
            {
                if (_counter++ > 30) new RecommendationModel();

                var type = (RecomendationType)rnd.Next(1, 4);
                switch (type)
                {
                    case RecomendationType.Test:
                        if (ApplicationState.Data.Tests.Any())
                        {
                            var item = ApplicationState.Data.Tests[rnd.Next(0, ApplicationState.Data.Tests.Count)];
                            return new RecommendationModel(item);
                        }
                        break;
                    case RecomendationType.Practice:
                        if (ApplicationState.Data.Practices.Any())
                        {
                            var item = ApplicationState.Data.Practices[rnd.Next(0, ApplicationState.Data.Practices.Count)];
                            return new RecommendationModel(item);
                        }
                        break;
                    case RecomendationType.Topic:
                        if (ApplicationState.Data.Topics.Any())
                        {
                            var item = ApplicationState.Data.Topics[rnd.Next(0, ApplicationState.Data.Topics.Count)];
                            return new RecommendationModel(item);
                        }
                        break;
                    case RecomendationType.Exercise:
                        if (ApplicationState.Data.Exercises.Any())
                        {
                            var item = ApplicationState.Data.Exercises[rnd.Next(0, ApplicationState.Data.Exercises.Count)];
                            return new RecommendationModel(item);
                        }
                        break;
                }
            }
        }




        public void SaveChangesToMemory()
        {
            try
            {
                var json = JsonSerializer.Serialize(this);
                File.WriteAllText(FilePath, json);
            }
            catch { }
        }
    }
}
