﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Profile.ProfilePage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:pancakeview="clr-namespace:Triggero.MauiMobileApp.Controls"
    
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    x:Name="this"
 
    Background="White"
    BackgroundColor="White">
    <ContentPage.Content>
        <Grid
            Margin="{x:OnPlatform Android='0',
                                iOS='0,0,0,0'}"
            RowSpacing="0"
            VerticalOptions="FillAndExpand">

            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Image
                Grid.RowSpan="2"
                Aspect="Fill"
                Source="trackerBgGradient.png" />
            
            <ScrollView Grid.Row="1">

                <Grid>



                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="250" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>

                        <Grid Grid.Row="0">


                            <Grid
                                Margin="0,20,5,0"
                                HeightRequest="55"
                                HorizontalOptions="End"
                                VerticalOptions="Start"
                                WidthRequest="55">
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=Close}" />
                                </Grid.GestureRecognizers>

                                <ImageButton
                                    BackgroundColor="Transparent"
                                    Command="{Binding Source={x:Reference this}, Path=Close}"
                                    CornerRadius="0"
                                    HeightRequest="17"
                                    HorizontalOptions="Center"
                                    Source="close.png"
                                    VerticalOptions="Center"
                                    WidthRequest="17" />
                            </Grid>


                            <!--<ImageButton
                                Command="{Binding Source={x:Reference this},Path=Close}"
                                CornerRadius="0"
                                WidthRequest="14"
                                HeightRequest="14"
                                Aspect="Fill"
                                Source="close.png"
                                Margin="0,40,25,0"
                                HorizontalOptions="End"
                                VerticalOptions="Start"
                                BackgroundColor="Transparent"/>-->

                            <StackLayout
                                Margin="0,0,0,30"
                                HorizontalOptions="Center"
                                Spacing="0"
                                VerticalOptions="End">

                                <pancakeview:PancakeView
                                    StrokeThickness="3"
                                    Stroke="{x:StaticResource blueColor}"
                                    Padding="0"
                                    BackgroundColor="#DEEDF9"
                                    StrokeShape="RoundRectangle 50"
                                    HeightRequest="100"
                                    HorizontalOptions="Center"
                                    VerticalOptions="Start"
                                    WidthRequest="100">
                                    
                                    <Image
                                        x:Name="avatar"
                                        HorizontalOptions="Fill"
                                        Aspect="AspectFill"
                                        VerticalOptions="Fill">
                                        <Image.GestureRecognizers>
                                            <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=ShowAvatars}" />
                                        </Image.GestureRecognizers>
                                    </Image>
                                    
                                </pancakeview:PancakeView>

                                   <Label
                                         Margin="0,12,0,0"
                                        HorizontalOptions="Center"
                                        x:Name="nameTextEdit"
                                        IsEnabled="{Binding Source={x:Reference this}, Path=IsEditing}"
                                        Text="..."
                                        FontSize="22"
                                        VerticalOptions="Center" />
                                

                                <!--<StackLayout
                                    Margin="0,12,0,0"
                                    HorizontalOptions="Center"
                                    Orientation="Horizontal"
                                    Spacing="0">
                                    <editors:TextEdit
                                        x:Name="nameTextEdit"
                                        BorderThickness="0"
                                        BoxMinWidth="10"
                                        BoxPadding="0"
                                        ClearIconVisibility="Never"
                                        DisabledBorderThickness="0"
                                        FocusedBorderThickness="0"
                                        HorizontalOptions="Start"
                                        IconColor="Transparent"
                                        IsEnabled="{Binding Source={x:Reference this}, Path=IsEditing}"
                                        IsEndIconVisible="False"
                                        IsStartIconVisible="False"
                                        PlaceholderText="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.Name}"
                                        Text=""
                                        TextChangedCommand="{Binding Source={x:Reference this}, Path=NameChanged}"
                                        TextChangedCommandParameter="{Binding Source={x:Reference nameTextEdit}, Path=Text}"
                                        TextFontSize="{x:OnPlatform Android=22,
                                                                  iOS=22}"
                                        VerticalOptions="Center" />
                                    <ImageButton
                                        x:Name="toggleEditingBtn"
                                        Margin="15,0,0,0"
                                        BackgroundColor="Transparent"
                                        Command="{Binding Source={x:Reference this}, Path=ToggleEditing}"
                                        CornerRadius="0"
                                        HeightRequest="14"
                                        HorizontalOptions="Start"
                                        Opacity="0.5"
                                        Source="editName.png"
                                        VerticalOptions="Center"
                                        WidthRequest="14" />
                                </StackLayout>-->

                                <Label
                                    x:Name="emailLabel"
                                    FontFamily="FontTextLight"
                                    FontSize="{x:OnPlatform Android=14,
                                                          iOS=17}"
                                    HorizontalOptions="Center"
                                    Opacity="0.6"
                                    Text=""
                                    TextColor="Black"
                                    VerticalOptions="Start" />

                            </StackLayout>

                        </Grid>

                        <StackLayout Grid.Row="1">


                            <Frame
                                Margin="20,0,20,0"
                                HeightRequest="100"
                                VerticalOptions="Start"
                                Padding="0"
                                Background="#FFFFFF"
                                CornerRadius="20"
                                HasShadow="False">
                                <Frame.Shadow>
                                    <Shadow Brush="#27527A"
                                            Offset="2,2"
                                            Radius="12"
                                            Opacity="0.06" />
                                </Frame.Shadow>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="1*" />
                                            <ColumnDefinition Width="1*" />
                                            <ColumnDefinition Width="1*" />
                                        </Grid.ColumnDefinitions>

                                        <Grid Grid.Column="0">


                                            <BoxView
                                                BackgroundColor="{x:StaticResource lightBlueColor}"
                                                HeightRequest="67"
                                                HorizontalOptions="End"
                                                VerticalOptions="Start"
                                                WidthRequest="1" />

                                            <StackLayout
                                                HorizontalOptions="Center"
                                                Spacing="0"
                                                VerticalOptions="Center">

                                                <Label
                                                    x:Name="triggeroDaysCountLabel"
                                                    FontFamily="FontTextLight"
                                                    FontSize="28"
                                                    HorizontalOptions="Center"
                                                    Text=""
                                                    TextColor="{x:StaticResource blueColor}"
                                                    VerticalOptions="Start" />

                                                <Label
                                                    FontFamily="FontTextLight"
                                                    FontSize="{x:OnPlatform Android=12,
                                                                          iOS=12}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    Opacity="0.7"
                                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.DaysWithTriggero}"
                                                    TextColor="Black"
                                                    VerticalOptions="Start"
                                                    WidthRequest="60" />
                                            </StackLayout>

                                        </Grid>

                                        <Grid Grid.Column="1">

                                            <BoxView
                                                BackgroundColor="{x:StaticResource lightBlueColor}"
                                                HeightRequest="67"
                                                HorizontalOptions="End"
                                                VerticalOptions="Start"
                                                WidthRequest="1" />


                                            <StackLayout
                                                HorizontalOptions="Center"
                                                Spacing="0"
                                                VerticalOptions="Center">

                                                <Label
                                                    x:Name="testPassedCountLabel"
                                                    FontFamily="FontTextLight"
                                                    FontSize="28"
                                                    HorizontalOptions="Center"
                                                    TextColor="{x:StaticResource blueColor}"
                                                    VerticalOptions="Start" />

                                                <Label
                                                    FontFamily="FontTextLight"
                                                    FontSize="{x:OnPlatform Android=12,
                                                                          iOS=12}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    Opacity="0.7"
                                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.TestsPassed}"
                                                    TextColor="Black"
                                                    VerticalOptions="Start"
                                                    WidthRequest="66" />
                                            </StackLayout>


                                        </Grid>

                                        <Grid Grid.Column="2">

                                            <StackLayout
                                                HorizontalOptions="Center"
                                                Spacing="0"
                                                VerticalOptions="Center">

                                                <Label
                                                    x:Name="practicesPassedCountLabel"
                                                    FontFamily="FontTextLight"
                                                    FontSize="28"
                                                    HorizontalOptions="Center"
                                                    Text=""
                                                    TextColor="{x:StaticResource blueColor}"
                                                    VerticalOptions="Start" />

                                                <Label
                                                    FontFamily="FontTextLight"
                                                    FontSize="{x:OnPlatform Android=12,
                                                                          iOS=12}"
                                                    HorizontalOptions="Center"
                                                    HorizontalTextAlignment="Center"
                                                    Opacity="0.7"
                                                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.PracticesListened}"
                                                    TextColor="Black"
                                                    VerticalOptions="Start"
                                                    WidthRequest="90" />
                                            </StackLayout>


                                        </Grid>


                                    </Grid>
                            </Frame>


                            <Frame
                                Margin="20,30,20,0"
                                VerticalOptions="Start"
                                Padding="0,4"
                                Background="#FFFFFF"
                                CornerRadius="20"
                                HasShadow="False">
                                <Frame.Shadow>
                                    <Shadow Brush="#27527A"
                                            Offset="2,2"
                                            Radius="12"
                                            Opacity="0.06" />
                                </Frame.Shadow>

                                    <StackLayout>

                                        <Grid
                                            HeightRequest="60"
                                            x:Name="subGrid"
                                            IsVisible="False">
                                            <Grid.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToSubscription}" />
                                            </Grid.GestureRecognizers>

                                            <Image
                                                Margin="20,0,0,0"
                                                HeightRequest="20"
                                                HorizontalOptions="Start"
                                                Source="subscriptionAqua.png"
                                                VerticalOptions="Center"
                                                WidthRequest="20" />

                                            <Label
                                                Margin="60,0,0,0"
                                                FontFamily="FontTextLight"
                                                FontSize="{x:OnPlatform Android=14,
                                                                      iOS=16}"
                                                HorizontalOptions="Start"
                                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.Subscription}"
                                                TextColor="{x:StaticResource greyTextColor}"
                                                VerticalOptions="Center" />

                                            <Image
                                                Margin="0,0,20,0"
                                                HeightRequest="10"
                                                HorizontalOptions="End"
                                                Source="arrowForwardBlack.png"
                                                VerticalOptions="Center"
                                                WidthRequest="5" />

                                            <BoxView
                                                Margin="20,0,0,0"
                                                BackgroundColor="{x:StaticResource lightBlueColor}"
                                                HeightRequest="1"
                                                HorizontalOptions="Fill"
                                                Opacity="0.6"
                                                VerticalOptions="End" />

                                        </Grid>

                                        <!--<Grid  
                                                   HeightRequest="60">
                                        
                                            <Grid.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToDownloadData}" />
                                            </Grid.GestureRecognizers>

                                            <Image
                                                Margin="20,0,0,0"
                                                HeightRequest="20"
                                                HorizontalOptions="Start"
                                                Source="downloadDataAqua.png"
                                                VerticalOptions="Center"
                                                WidthRequest="20" />

                                            <Label
                                                Margin="60,-3,0,0"
                                                FontFamily="FontTextLight"
                                                FontSize="{x:OnPlatform Android=14,
                                                                      iOS=16}"
                                                HorizontalOptions="Start"
                                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.DownloadYourData}"
                                                TextColor="{x:StaticResource greyTextColor}"
                                                VerticalOptions="Center" />

                                            <Image
                                                Margin="0,0,20,0"
                                                HeightRequest="10"
                                                HorizontalOptions="End"
                                                Source="arrowForwardBlack.png"
                                                VerticalOptions="Center"
                                                WidthRequest="5" />

                                            <BoxView
                                                Margin="20,0,0,0"
                                                BackgroundColor="{x:StaticResource lightBlueColor}"
                                                HeightRequest="1"
                                                HorizontalOptions="Fill"
                                                Opacity="0.6"
                                                VerticalOptions="End" />


                                        </Grid>-->

                                        <Grid            HeightRequest="60" >
                                            <Grid.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToNotifications}" />
                                            </Grid.GestureRecognizers>

                                            <Image
                                                Margin="20,0,0,0"
                                                HeightRequest="20"
                                                HorizontalOptions="Start"
                                                Source="notificationsAqua.png"
                                                VerticalOptions="Center"
                                                WidthRequest="20" />

                                            <Label
                                                Margin="60,0,0,0"
                                                FontFamily="FontTextLight"
                                                FontSize="{x:OnPlatform Android=14,
                                                                      iOS=16}"
                                                HorizontalOptions="Start"
                                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.Notifications}"
                                                TextColor="{x:StaticResource greyTextColor}"
                                                VerticalOptions="Center" />

                                            <Image
                                                Margin="0,0,20,0"
                                                HeightRequest="10"
                                                HorizontalOptions="End"
                                                Source="arrowForwardBlack.png"
                                                VerticalOptions="Center"
                                                WidthRequest="5" />

                                            <BoxView
                                                Margin="20,0,0,0"
                                                BackgroundColor="{x:StaticResource lightBlueColor}"
                                                HeightRequest="1"
                                                HorizontalOptions="Fill"
                                                Opacity="0.6"
                                                VerticalOptions="End" />


                                        </Grid>

                                        <Grid             HeightRequest="60">
                                            <Grid.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToSupport}" />
                                            </Grid.GestureRecognizers>

                                            <Image
                                                Margin="20,0,0,0"
                                                HeightRequest="20"
                                                HorizontalOptions="Start"
                                                Source="supportAqua.png"
                                                VerticalOptions="Center"
                                                WidthRequest="20" />

                                            <Label
                                                Margin="60,0,0,0"
                                                FontFamily="FontTextLight"
                                                FontSize="{x:OnPlatform Android=14,
                                                                      iOS=16}"
                                                HorizontalOptions="Start"
                                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.Support}"
                                                TextColor="{x:StaticResource greyTextColor}"
                                                VerticalOptions="Center" />

                                            <Image
                                                Margin="0,0,20,0"
                                                HeightRequest="10"
                                                HorizontalOptions="End"
                                                Source="arrowForwardBlack.png"
                                                VerticalOptions="Center"
                                                WidthRequest="5" />

                                            <BoxView
                                                Margin="20,0,0,0"
                                                BackgroundColor="{x:StaticResource lightBlueColor}"
                                                HeightRequest="1"
                                                HorizontalOptions="Fill"
                                                Opacity="0.6"
                                                VerticalOptions="End" />

                                        </Grid>

                                        <Grid             HeightRequest="60">
                                            <Grid.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToTerms}" />
                                            </Grid.GestureRecognizers>

                                            <Image
                                                Margin="20,0,0,0"
                                                HeightRequest="20"
                                                HorizontalOptions="Start"
                                                Source="termsAqua.png"
                                                VerticalOptions="Center"
                                                WidthRequest="20" />

                                            <Label
                                                Margin="60,0,0,0"
                                                FontFamily="FontTextLight"
                                                FontSize="{x:OnPlatform Android=14,
                                                                      iOS=16}"
                                                HorizontalOptions="Start"
                                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.Terms}"
                                                TextColor="{x:StaticResource greyTextColor}"
                                                VerticalOptions="Center" />

                                            <Image
                                                Margin="0,0,20,0"
                                                HeightRequest="10"
                                                HorizontalOptions="End"
                                                Source="arrowForwardBlack.png"
                                                VerticalOptions="Center"
                                                WidthRequest="5" />

                                            <BoxView
                                                Margin="20,0,0,0"
                                                BackgroundColor="{x:StaticResource lightBlueColor}"
                                                HeightRequest="1"
                                                HorizontalOptions="Fill"
                                                Opacity="0.6"
                                                VerticalOptions="End" />

                                        </Grid>

                                        <Grid             HeightRequest="60">
                                            <Grid.GestureRecognizers>
                                                <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=GoToPrivacy}" />
                                            </Grid.GestureRecognizers>

                                            <Image
                                                Margin="20,0,0,0"
                                                HeightRequest="20"
                                                HorizontalOptions="Start"
                                                Source="privacyAqua.png"
                                                VerticalOptions="Center"
                                                WidthRequest="20" />

                                            <Label
                                                Margin="60,0,0,0"
                                                FontFamily="FontTextLight"
                                                FontSize="{x:OnPlatform Android=14,
                                                                      iOS=16}"
                                                HorizontalOptions="Start"
                                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.PrivacyPolicy}"
                                                TextColor="{x:StaticResource greyTextColor}"
                                                VerticalOptions="Center" />

                                            <Image
                                                Margin="0,0,20,0"
                                                HeightRequest="10"
                                                HorizontalOptions="End"
                                                Source="arrowForwardBlack.png"
                                                VerticalOptions="Center"
                                                WidthRequest="5" />

                                        </Grid>

                                    </StackLayout>

                            </Frame>

                            <Button
                                Margin="20,40,20,30"
                                Command="{Binding Source={x:Reference this}, Path=Logout}"
                                HeightRequest="59"
                                HorizontalOptions="Fill"
                                Style="{x:StaticResource yellow_btn}"
                                Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Profile.ProfileMain.Logout}"
                                VerticalOptions="Center" />

                            <Button
                                Margin="20,-20,20,30"
                                Command="{Binding Source={x:Reference this}, Path=DeleteAccount}"
                                HeightRequest="59"
                                HorizontalOptions="Fill"
                                Style="{x:StaticResource yellow_btn}"
                                Text="Удалить аккаунт"
                                VerticalOptions="Center" />

                            <!--  version  -->
                            <Label
                                Margin="0,0,0,24"
                                FontFamily="FontTextLight"
                                FontSize="12"
                                HorizontalOptions="Center"
                                LineBreakMode="TailTruncation"
                                Opacity="0.75"
                                Text="{x:Static triggeroV2:Globals.Version}"
                                TextColor="{x:StaticResource greyTextColor}" />

                        </StackLayout>


                    </Grid>
                </Grid>
            </ScrollView>

        </Grid>
    </ContentPage.Content>
</ContentPage>