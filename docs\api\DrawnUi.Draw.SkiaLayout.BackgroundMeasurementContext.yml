### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext
  commentId: T:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext
  id: SkiaLayout.BackgroundMeasurementContext
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.EndMeasuringAt
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertAtIndex
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertCount
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsInsertOperation
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsSingleItemRemeasurement
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.SingleItemIndex
  - DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.StartMeasuringFrom
  langs:
  - csharp
  - vb
  name: SkiaLayout.BackgroundMeasurementContext
  nameWithType: SkiaLayout.BackgroundMeasurementContext
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: BackgroundMeasurementContext
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 122
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Context for background measurement operations
  example: []
  syntax:
    content: public class SkiaLayout.BackgroundMeasurementContext
    content.vb: Public Class SkiaLayout.BackgroundMeasurementContext
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertAtIndex
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertAtIndex
  id: InsertAtIndex
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext
  langs:
  - csharp
  - vb
  name: InsertAtIndex
  nameWithType: SkiaLayout.BackgroundMeasurementContext.InsertAtIndex
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertAtIndex
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: InsertAtIndex
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 124
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int? InsertAtIndex { get; set; }
    parameters: []
    return:
      type: System.Nullable{System.Int32}
    content.vb: Public Property InsertAtIndex As Integer?
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertAtIndex*
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertCount
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertCount
  id: InsertCount
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext
  langs:
  - csharp
  - vb
  name: InsertCount
  nameWithType: SkiaLayout.BackgroundMeasurementContext.InsertCount
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertCount
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: InsertCount
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 125
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int? InsertCount { get; set; }
    parameters: []
    return:
      type: System.Nullable{System.Int32}
    content.vb: Public Property InsertCount As Integer?
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertCount*
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.StartMeasuringFrom
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.StartMeasuringFrom
  id: StartMeasuringFrom
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext
  langs:
  - csharp
  - vb
  name: StartMeasuringFrom
  nameWithType: SkiaLayout.BackgroundMeasurementContext.StartMeasuringFrom
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.StartMeasuringFrom
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: StartMeasuringFrom
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 126
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int StartMeasuringFrom { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property StartMeasuringFrom As Integer
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.StartMeasuringFrom*
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsInsertOperation
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsInsertOperation
  id: IsInsertOperation
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext
  langs:
  - csharp
  - vb
  name: IsInsertOperation
  nameWithType: SkiaLayout.BackgroundMeasurementContext.IsInsertOperation
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsInsertOperation
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsInsertOperation
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 127
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsInsertOperation { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsInsertOperation As Boolean
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsInsertOperation*
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsSingleItemRemeasurement
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsSingleItemRemeasurement
  id: IsSingleItemRemeasurement
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext
  langs:
  - csharp
  - vb
  name: IsSingleItemRemeasurement
  nameWithType: SkiaLayout.BackgroundMeasurementContext.IsSingleItemRemeasurement
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsSingleItemRemeasurement
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: IsSingleItemRemeasurement
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 128
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public bool IsSingleItemRemeasurement { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property IsSingleItemRemeasurement As Boolean
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsSingleItemRemeasurement*
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.SingleItemIndex
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.SingleItemIndex
  id: SingleItemIndex
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext
  langs:
  - csharp
  - vb
  name: SingleItemIndex
  nameWithType: SkiaLayout.BackgroundMeasurementContext.SingleItemIndex
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.SingleItemIndex
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SingleItemIndex
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 129
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int? SingleItemIndex { get; set; }
    parameters: []
    return:
      type: System.Nullable{System.Int32}
    content.vb: Public Property SingleItemIndex As Integer?
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.SingleItemIndex*
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.EndMeasuringAt
  commentId: P:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.EndMeasuringAt
  id: EndMeasuringAt
  parent: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext
  langs:
  - csharp
  - vb
  name: EndMeasuringAt
  nameWithType: SkiaLayout.BackgroundMeasurementContext.EndMeasuringAt
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.EndMeasuringAt
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: EndMeasuringAt
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ListView.cs
    startLine: 130
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public int? EndMeasuringAt { get; set; }
    parameters: []
    return:
      type: System.Nullable{System.Int32}
    content.vb: Public Property EndMeasuringAt As Integer?
  overload: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.EndMeasuringAt*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertAtIndex*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertAtIndex
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementContext_InsertAtIndex
  name: InsertAtIndex
  nameWithType: SkiaLayout.BackgroundMeasurementContext.InsertAtIndex
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertAtIndex
- uid: System.Nullable{System.Int32}
  commentId: T:System.Nullable{System.Int32}
  parent: System
  definition: System.Nullable`1
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int?
  nameWithType: int?
  fullName: int?
  nameWithType.vb: Integer?
  fullName.vb: Integer?
  name.vb: Integer?
  spec.csharp:
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: '?'
  spec.vb:
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: '?'
- uid: System.Nullable`1
  commentId: T:System.Nullable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  name: Nullable<T>
  nameWithType: Nullable<T>
  fullName: System.Nullable<T>
  nameWithType.vb: Nullable(Of T)
  fullName.vb: System.Nullable(Of T)
  name.vb: Nullable(Of T)
  spec.csharp:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertCount*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertCount
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementContext_InsertCount
  name: InsertCount
  nameWithType: SkiaLayout.BackgroundMeasurementContext.InsertCount
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.InsertCount
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.StartMeasuringFrom*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.StartMeasuringFrom
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementContext_StartMeasuringFrom
  name: StartMeasuringFrom
  nameWithType: SkiaLayout.BackgroundMeasurementContext.StartMeasuringFrom
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.StartMeasuringFrom
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsInsertOperation*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsInsertOperation
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementContext_IsInsertOperation
  name: IsInsertOperation
  nameWithType: SkiaLayout.BackgroundMeasurementContext.IsInsertOperation
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsInsertOperation
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsSingleItemRemeasurement*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsSingleItemRemeasurement
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementContext_IsSingleItemRemeasurement
  name: IsSingleItemRemeasurement
  nameWithType: SkiaLayout.BackgroundMeasurementContext.IsSingleItemRemeasurement
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.IsSingleItemRemeasurement
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.SingleItemIndex*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.SingleItemIndex
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementContext_SingleItemIndex
  name: SingleItemIndex
  nameWithType: SkiaLayout.BackgroundMeasurementContext.SingleItemIndex
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.SingleItemIndex
- uid: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.EndMeasuringAt*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.EndMeasuringAt
  href: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.html#DrawnUi_Draw_SkiaLayout_BackgroundMeasurementContext_EndMeasuringAt
  name: EndMeasuringAt
  nameWithType: SkiaLayout.BackgroundMeasurementContext.EndMeasuringAt
  fullName: DrawnUi.Draw.SkiaLayout.BackgroundMeasurementContext.EndMeasuringAt
