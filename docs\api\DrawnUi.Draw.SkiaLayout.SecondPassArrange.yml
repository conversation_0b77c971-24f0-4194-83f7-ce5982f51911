### YamlMime:ManagedReference
items:
- uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange
  commentId: T:DrawnUi.Draw.SkiaLayout.SecondPassArrange
  id: SkiaLayout.SecondPassArrange
  parent: DrawnUi.Draw
  children:
  - DrawnUi.Draw.SkiaLayout.SecondPassArrange.#ctor(DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
  - DrawnUi.Draw.SkiaLayout.SecondPassArrange.Cell
  - DrawnUi.Draw.SkiaLayout.SecondPassArrange.Child
  - DrawnUi.Draw.SkiaLayout.SecondPassArrange.Scale
  langs:
  - csharp
  - vb
  name: SkiaLayout.SecondPassArrange
  nameWithType: SkiaLayout.SecondPassArrange
  fullName: DrawnUi.Draw.SkiaLayout.SecondPassArrange
  type: Class
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: SecondPassArrange
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs
    startLine: 122
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Cell.Area contains the area for layout
  example: []
  syntax:
    content: 'public record SkiaLayout.SecondPassArrange : IEquatable<SkiaLayout.SecondPassArrange>'
    content.vb: Public Class SkiaLayout.SecondPassArrange Implements IEquatable(Of SkiaLayout.SecondPassArrange)
  inheritance:
  - System.Object
  implements:
  - System.IEquatable{DrawnUi.Draw.SkiaLayout.SecondPassArrange}
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
  extensionMethods:
  - System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
- uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange.#ctor(DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
  commentId: M:DrawnUi.Draw.SkiaLayout.SecondPassArrange.#ctor(DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)
  id: '#ctor(DrawnUi.Draw.ControlInStack,DrawnUi.Draw.SkiaControl,System.Single)'
  parent: DrawnUi.Draw.SkiaLayout.SecondPassArrange
  langs:
  - csharp
  - vb
  name: SecondPassArrange(ControlInStack, SkiaControl, float)
  nameWithType: SkiaLayout.SecondPassArrange.SecondPassArrange(ControlInStack, SkiaControl, float)
  fullName: DrawnUi.Draw.SkiaLayout.SecondPassArrange.SecondPassArrange(DrawnUi.Draw.ControlInStack, DrawnUi.Draw.SkiaControl, float)
  type: Constructor
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: .ctor
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs
    startLine: 122
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  summary: Cell.Area contains the area for layout
  example: []
  syntax:
    content: public SecondPassArrange(ControlInStack Cell, SkiaControl Child, float Scale)
    parameters:
    - id: Cell
      type: DrawnUi.Draw.ControlInStack
    - id: Child
      type: DrawnUi.Draw.SkiaControl
    - id: Scale
      type: System.Single
    content.vb: Public Sub New(Cell As ControlInStack, Child As SkiaControl, Scale As Single)
  overload: DrawnUi.Draw.SkiaLayout.SecondPassArrange.#ctor*
  nameWithType.vb: SkiaLayout.SecondPassArrange.New(ControlInStack, SkiaControl, Single)
  fullName.vb: DrawnUi.Draw.SkiaLayout.SecondPassArrange.New(DrawnUi.Draw.ControlInStack, DrawnUi.Draw.SkiaControl, Single)
  name.vb: New(ControlInStack, SkiaControl, Single)
- uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Cell
  commentId: P:DrawnUi.Draw.SkiaLayout.SecondPassArrange.Cell
  id: Cell
  parent: DrawnUi.Draw.SkiaLayout.SecondPassArrange
  langs:
  - csharp
  - vb
  name: Cell
  nameWithType: SkiaLayout.SecondPassArrange.Cell
  fullName: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Cell
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Cell
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs
    startLine: 122
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public ControlInStack Cell { get; init; }
    parameters: []
    return:
      type: DrawnUi.Draw.ControlInStack
    content.vb: Public Property Cell As ControlInStack
  overload: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Cell*
- uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Child
  commentId: P:DrawnUi.Draw.SkiaLayout.SecondPassArrange.Child
  id: Child
  parent: DrawnUi.Draw.SkiaLayout.SecondPassArrange
  langs:
  - csharp
  - vb
  name: Child
  nameWithType: SkiaLayout.SecondPassArrange.Child
  fullName: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Child
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Child
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs
    startLine: 122
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public SkiaControl Child { get; init; }
    parameters: []
    return:
      type: DrawnUi.Draw.SkiaControl
    content.vb: Public Property Child As SkiaControl
  overload: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Child*
- uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Scale
  commentId: P:DrawnUi.Draw.SkiaLayout.SecondPassArrange.Scale
  id: Scale
  parent: DrawnUi.Draw.SkiaLayout.SecondPassArrange
  langs:
  - csharp
  - vb
  name: Scale
  nameWithType: SkiaLayout.SecondPassArrange.Scale
  fullName: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Scale
  type: Property
  source:
    remote:
      path: src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs
      branch: 2-A
      repo: https://github.com/taublast/DrawnUi.git
    id: Scale
    path: ../src/Maui/DrawnUi/Draw/Layout/SkiaLayout.ColumnRow.cs
    startLine: 122
  assemblies:
  - DrawnUi.Maui
  namespace: DrawnUi.Draw
  syntax:
    content: public float Scale { get; init; }
    parameters: []
    return:
      type: System.Single
    content.vb: Public Property Scale As Single
  overload: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Scale*
references:
- uid: DrawnUi.Draw
  commentId: N:DrawnUi.Draw
  href: DrawnUi.html
  name: DrawnUi.Draw
  nameWithType: DrawnUi.Draw
  fullName: DrawnUi.Draw
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Draw
    name: Draw
    href: DrawnUi.Draw.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.IEquatable{DrawnUi.Draw.SkiaLayout.SecondPassArrange}
  commentId: T:System.IEquatable{DrawnUi.Draw.SkiaLayout.SecondPassArrange}
  parent: System
  definition: System.IEquatable`1
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<SkiaLayout.SecondPassArrange>
  nameWithType: IEquatable<SkiaLayout.SecondPassArrange>
  fullName: System.IEquatable<DrawnUi.Draw.SkiaLayout.SecondPassArrange>
  nameWithType.vb: IEquatable(Of SkiaLayout.SecondPassArrange)
  fullName.vb: System.IEquatable(Of DrawnUi.Draw.SkiaLayout.SecondPassArrange)
  name.vb: IEquatable(Of SkiaLayout.SecondPassArrange)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange
    name: SecondPassArrange
    href: DrawnUi.Draw.SkiaLayout.SecondPassArrange.html
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - uid: DrawnUi.Draw.SkiaLayout
    name: SkiaLayout
    href: DrawnUi.Draw.SkiaLayout.html
  - name: .
  - uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange
    name: SecondPassArrange
    href: DrawnUi.Draw.SkiaLayout.SecondPassArrange.html
  - name: )
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System.Object.DrawnUi.Extensions.InternalExtensions.FromPlatform
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  parent: DrawnUi.Extensions.InternalExtensions
  definition: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.IEquatable`1
  commentId: T:System.IEquatable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  name: IEquatable<T>
  nameWithType: IEquatable<T>
  fullName: System.IEquatable<T>
  nameWithType.vb: IEquatable(Of T)
  fullName.vb: System.IEquatable(Of T)
  name.vb: IEquatable(Of T)
  spec.csharp:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.IEquatable`1
    name: IEquatable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.iequatable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  commentId: M:DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
  isExternal: true
  href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  name: FromPlatform(object)
  nameWithType: InternalExtensions.FromPlatform(object)
  fullName: DrawnUi.Extensions.InternalExtensions.FromPlatform(object)
  nameWithType.vb: InternalExtensions.FromPlatform(Object)
  fullName.vb: DrawnUi.Extensions.InternalExtensions.FromPlatform(Object)
  name.vb: FromPlatform(Object)
  spec.csharp:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: DrawnUi.Extensions.InternalExtensions.FromPlatform(System.Object)
    name: FromPlatform
    href: DrawnUi.Extensions.InternalExtensions.html#DrawnUi_Extensions_InternalExtensions_FromPlatform_System_Object_
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: DrawnUi.Extensions.InternalExtensions
  commentId: T:DrawnUi.Extensions.InternalExtensions
  parent: DrawnUi.Extensions
  href: DrawnUi.Extensions.InternalExtensions.html
  name: InternalExtensions
  nameWithType: InternalExtensions
  fullName: DrawnUi.Extensions.InternalExtensions
- uid: DrawnUi.Extensions
  commentId: N:DrawnUi.Extensions
  href: DrawnUi.html
  name: DrawnUi.Extensions
  nameWithType: DrawnUi.Extensions
  fullName: DrawnUi.Extensions
  spec.csharp:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
  spec.vb:
  - uid: DrawnUi
    name: DrawnUi
    href: DrawnUi.html
  - name: .
  - uid: DrawnUi.Extensions
    name: Extensions
    href: DrawnUi.Extensions.html
- uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange.#ctor*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.SecondPassArrange.#ctor
  href: DrawnUi.Draw.SkiaLayout.SecondPassArrange.html#DrawnUi_Draw_SkiaLayout_SecondPassArrange__ctor_DrawnUi_Draw_ControlInStack_DrawnUi_Draw_SkiaControl_System_Single_
  name: SecondPassArrange
  nameWithType: SkiaLayout.SecondPassArrange.SecondPassArrange
  fullName: DrawnUi.Draw.SkiaLayout.SecondPassArrange.SecondPassArrange
  nameWithType.vb: SkiaLayout.SecondPassArrange.New
  fullName.vb: DrawnUi.Draw.SkiaLayout.SecondPassArrange.New
  name.vb: New
- uid: DrawnUi.Draw.ControlInStack
  commentId: T:DrawnUi.Draw.ControlInStack
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.ControlInStack.html
  name: ControlInStack
  nameWithType: ControlInStack
  fullName: DrawnUi.Draw.ControlInStack
- uid: DrawnUi.Draw.SkiaControl
  commentId: T:DrawnUi.Draw.SkiaControl
  parent: DrawnUi.Draw
  href: DrawnUi.Draw.SkiaControl.html
  name: SkiaControl
  nameWithType: SkiaControl
  fullName: DrawnUi.Draw.SkiaControl
- uid: System.Single
  commentId: T:System.Single
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.single
  name: float
  nameWithType: float
  fullName: float
  nameWithType.vb: Single
  fullName.vb: Single
  name.vb: Single
- uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Cell*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.SecondPassArrange.Cell
  href: DrawnUi.Draw.SkiaLayout.SecondPassArrange.html#DrawnUi_Draw_SkiaLayout_SecondPassArrange_Cell
  name: Cell
  nameWithType: SkiaLayout.SecondPassArrange.Cell
  fullName: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Cell
- uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Child*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.SecondPassArrange.Child
  href: DrawnUi.Draw.SkiaLayout.SecondPassArrange.html#DrawnUi_Draw_SkiaLayout_SecondPassArrange_Child
  name: Child
  nameWithType: SkiaLayout.SecondPassArrange.Child
  fullName: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Child
- uid: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Scale*
  commentId: Overload:DrawnUi.Draw.SkiaLayout.SecondPassArrange.Scale
  href: DrawnUi.Draw.SkiaLayout.SecondPassArrange.html#DrawnUi_Draw_SkiaLayout_SecondPassArrange_Scale
  name: Scale
  nameWithType: SkiaLayout.SecondPassArrange.Scale
  fullName: DrawnUi.Draw.SkiaLayout.SecondPassArrange.Scale
