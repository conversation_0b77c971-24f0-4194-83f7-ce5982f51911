﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage
    x:Class="Triggero.MauiMobileApp.Views.Pages.Library.TopicPage"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:custom="clr-namespace:Triggero.MauiMobileApp.Custom"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp"
    xmlns:triggeroV2="clr-namespace:Triggero.MauiMobileApp"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="using:Triggero.MauiMobileApp.Views.Drawn"
    x:Name="this"
    BackgroundColor="White">
    <ContentPage.Content>

        <Grid RowSpacing="0">
            <Grid.RowDefinitions>
                <RowDefinition Height="{x:Static triggeroV2:Globals.TopInsets}" />
                <RowDefinition Height="70" />
                <RowDefinition Height="*" />
            </Grid.RowDefinitions>

            <Grid Grid.Row="1">

                <!--HEADER-->
                <Grid HorizontalOptions="Fill">

                    <!--BTN CLOSE-->
                    <Grid
                        Margin="0,0,0,0"
                        HeightRequest="55"
                        HorizontalOptions="Start"
                        VerticalOptions="Center"
                        WidthRequest="55">
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Command="{Binding Source={x:Reference this}, Path=Close}" />
                        </Grid.GestureRecognizers>

                        <ImageButton
                            BackgroundColor="Transparent"
                            Command="{Binding Source={x:Reference this}, Path=Close}"
                            CornerRadius="0"
                            HeightRequest="17"
                            HorizontalOptions="Center"
                            Source="close.png"
                            VerticalOptions="Center"
                            WidthRequest="17" />

                    </Grid>

                    <!--HEADER TITLE-->
                    <Label
                        Margin="0,0,0,0"
                        HorizontalOptions="Center"
                        Style="{x:StaticResource StyleHeaderNavigation}"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.Topics}"
                        VerticalOptions="Center" />

                    <!--BTN FAV-->

                    <draw:Canvas
                        Gestures="Enabled"
                        HeightRequest="55"
                        HorizontalOptions="End"
                        VerticalOptions="Center"
                        WidthRequest="55">

                        <drawn:FavCheck
                            UseCache="Operations"
                            HorizontalOptions="Fill"
                            VerticalOptions="Fill"
                            x:Name="favoriteRb"
                            Toggled="toggleFavorite"/>

                    </draw:Canvas>

                            <!--<Grid
                        Margin="0,0,0,0"
                        HeightRequest="55"
                        HorizontalOptions="End"
                        VerticalOptions="Center"
                        WidthRequest="55">
                        
                        <Grid.GestureRecognizers>
                            <TapGestureRecognizer Tapped="toggleFavorite" />
                        </Grid.GestureRecognizers>

                        <RadioButton
                            BackgroundColor="Green"
                            x:Name="favoriteRb"
                            CornerRadius="0"
                            HorizontalOptions="Center"
                            InputTransparent="True"
                            Style="{x:StaticResource favorite_hearted_rb}"
                            VerticalOptions="Center"/>

                    </Grid>-->

                    <BoxView
                        BackgroundColor="{x:StaticResource greyTextColor}"
                        HeightRequest="1"
                        HorizontalOptions="Fill"
                        VerticalOptions="End" />

                </Grid>

            </Grid>

            <Grid Grid.Row="2">

                <ScrollView
                    Margin="0,5,0,0"
                    VerticalScrollBarVisibility="Never">

                    <StackLayout
                        x:Name="mainStackLayout"
                        HorizontalOptions="Fill"
                        Opacity="0.001">

                        <!--  TITLE  -->
                        <Label
                            x:Name="titleLabel"
                            Margin="20,20,20,0"
                            HorizontalOptions="Start"
                            Style="{x:StaticResource StyleArticleTitle}"
                            Text="" />

                        <!--  AUTHOR  -->
                        <Label
                            x:Name="authorLabel"
                            Margin="20,0,20,0"
                            HorizontalOptions="Start"
                            IsVisible="False"
                            Style="{x:StaticResource StyleArticleSubTitle}"
                            Text="" />


                        <!--  BANNER  -->
                        <draw:Canvas
                            Margin="20,30,20,0"
                            HeightRequest="240"
                            HorizontalOptions="Fill">
                            <draw:SkiaShape
                                CornerRadius="16"
                                HorizontalOptions="Fill"
                                VerticalOptions="Fill">

                                <draw:SkiaImage
                                    x:Name="img"
                                    Aspect="AspectCover"
                                    HorizontalOptions="Fill"
                                    VerticalOptions="Fill" />

                            </draw:SkiaShape>
                        </draw:Canvas>

                        <!--<Frame
                            Margin="20,30,20,0"
                            Padding="0"
                            CornerRadius="16"
                            HasShadow="False"
                            HeightRequest="240"
                            HorizontalOptions="Fill"
                            IsClippedToBounds="True"
                            VerticalOptions="Start">

                            <Image
                                x:Name="img"
                                Aspect="AspectFill" />

                        </Frame>-->


                        <!--  TEXT CONTENT  -->
                        <StackLayout
                            Margin="20,22,0,0"
                            Orientation="Horizontal"
                            Spacing="30">

                            <StackLayout
                                HorizontalOptions="Start"
                                Spacing="1"
                                VerticalOptions="Center">

                                <Image
                                    HeightRequest="24"
                                    HorizontalOptions="Center"
                                    Source="timeCircleAqua.png"
                                    VerticalOptions="Center"
                                    WidthRequest="24" />

                                <Label
                                    Margin="0,3,0,0"
                                    FontSize="{x:OnPlatform Android=10,
                                                          iOS=12}"
                                    HorizontalOptions="Center"
                                    TextColor="#9A9D9F"
                                    VerticalOptions="Start">
                                    <Label.FormattedText>
                                        <FormattedString>
                                            <FormattedString.Spans>
                                                <Span Text="{Binding Source={x:Reference this}, Path=Topic.PassingTimeInMinutes, Mode=OneWay}" />
                                                <Span Text=" мин" />
                                            </FormattedString.Spans>
                                        </FormattedString>
                                    </Label.FormattedText>
                                </Label>

                            </StackLayout>

                            <!--  SEEN  -->
                            <StackLayout
                                HorizontalOptions="Start"
                                Spacing="1"
                                VerticalOptions="Center">

                                <Image
                                    HeightRequest="24"
                                    HorizontalOptions="Center"
                                    Source="watchesAqua.png"
                                    VerticalOptions="Center"
                                    WidthRequest="24" />

                                <Label
                                    Margin="0,3,0,0"
                                    FontSize="11"
                                    HorizontalOptions="Center"
                                    Text="{Binding Source={x:Reference this}, Path=Topic.Watches, Mode=OneWay}"
                                    TextColor="#9A9D9F"
                                    VerticalOptions="Start" />

                            </StackLayout>

                            <!--  LIKES  -->
                            <StackLayout
                                HorizontalOptions="Start"
                                Spacing="1"
                                VerticalOptions="Center">
                                <Image
                                    HeightRequest="24"
                                    HorizontalOptions="Center"
                                    Source="thumbLikeAqua.png"
                                    VerticalOptions="Center"
                                    WidthRequest="24" />
                                <Label
                                    x:Name="likesCountLabel"
                                    Margin="0,3,0,0"
                                    FontSize="11"
                                    HorizontalOptions="Center"
                                    Text=""
                                    TextColor="#9A9D9F"
                                    VerticalOptions="Start" />
                            </StackLayout>

                        </StackLayout>

                        <!--  RICH TEXT  -->
                        <custom:AlfaHtmlLabel
                            x:Name="descLabel"
                            Margin="20,30,20,0"
                            HorizontalOptions="Start" />

                        <!--REFERENCE LINK-->
                        <draw:Canvas
                            Gestures="Lock"
                            x:Name="LabelExternalLink"
                            HorizontalOptions="Start"
                            IsVisible="False">
                            <draw:SkiaLayout HorizontalOptions="Fill">

                                <draw:SkiaLabel
                                    Padding="24,8,24,8"
                                    draw:AddGestures.CommandTapped="{Binding Source={x:Reference this}, Path=CommandOpenReferenceLink}"
                                    BackgroundColor="White"
                                    FontFamily="FontTextMedium"
                                    FontSize="15"
                                    HorizontalOptions="Start"
                                    TextColor="{x:StaticResource blueColor}">
                                    <draw:SkiaLabel.Spans>

                                        <draw:TextSpan
                                            x:Name="ReferenceLinkSpan"
                                            Text="Источник" />

                                        <draw:TextSpan Text=" " />

                                        <draw:SvgSpan
                                            Width="17"
                                            Height="17"
                                            Source="Resources/Images/linkout.svg"
                                            TintColor="{x:StaticResource blueColor}"
                                            VerticalAlignement="Center" />

                                    </draw:SkiaLabel.Spans>

                                </draw:SkiaLabel>

                            </draw:SkiaLayout>
                        </draw:Canvas>

                        <!--  Понравилась статья?  -->
                        <Label
                            Margin="20,10,0,0"
                            FontAttributes="Bold"
                            FontSize="{x:OnPlatform Android=17,
                                                  iOS=17}"
                            HorizontalOptions="Start"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.Library.Library.TopicLiked}"
                            TextColor="{x:StaticResource greyTextColor}" />

                        <Grid
                            Margin="20,20,20,0"
                            ColumnSpacing="12"
                            HeightRequest="64">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                            </Grid.ColumnDefinitions>


                            <Grid
                                Grid.Column="0"
                                Padding="0">
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="likeSet" />
                                </Grid.GestureRecognizers>
                                <RadioButton
                                    x:Name="likeRb"
                                    GroupName="pxxx"
                                    InputTransparent="True"
                                    Style="{x:StaticResource thumb_like_rb}" />
                            </Grid>


                            <Grid
                                Grid.Column="1"
                                Padding="0">
                                <Grid.GestureRecognizers>
                                    <TapGestureRecognizer Tapped="dislikeSet" />
                                </Grid.GestureRecognizers>
                                <RadioButton
                                    x:Name="dislikeRb"
                                    GroupName="pxxx"
                                    InputTransparent="True"
                                    Style="{x:StaticResource thumb_dislike_rb}" />
                            </Grid>



                        </Grid>




                        <BoxView
                            Margin="0,40,0,0"
                            BackgroundColor="Black"
                            HeightRequest="1"
                            HorizontalOptions="Fill"
                            Opacity="0.2"
                            VerticalOptions="Start" />


                        <Grid
                            x:Name="nextItemLayout"
                            Margin="20,0,20,0"
                            HeightRequest="200"
                            VerticalOptions="Start">



                        </Grid>


                    </StackLayout>
                </ScrollView>
            </Grid>

        </Grid>
    </ContentPage.Content>
</ContentPage>