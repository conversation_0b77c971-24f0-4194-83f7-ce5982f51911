﻿<?xml version="1.0" encoding="UTF-8"?>
<models:BaseRecommendationView 
             
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:models="clr-namespace:Triggero.MauiPort.Models"

             x:Class="Triggero.Controls.Cards.TasksForToday.TestRecommendation"
             x:Name="this">
  <ContentView.Content>
      <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="70"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="20"/>
            </Grid.ColumnDefinitions>
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped"/>
            </Grid.GestureRecognizers>


            <Grid Grid.Column="0">
                <Frame 
                    Padding="0"
                    VerticalOptions="Center"
                    HorizontalOptions="Center"
                    Background="#CDE9FF"
                    HasShadow="False"
                    IsClippedToBounds="True"
                    CornerRadius="12"
                    HeightRequest="70"
                    WidthRequest="70">
                    <Image 
                        Aspect="Fill"
                        x:Name="img" />
                </Frame>
            </Grid>

            <Grid Grid.Column="1">
                <StackLayout
                    Margin="5,0,0,0"
                    VerticalOptions="Center">
                    
                    <Label 
                        x:Name="titleLabel"
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="{x:OnPlatform Android=13,iOS=17}"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Start"
                        Text=""/>
                    
                    <Label 
                        TextColor="{x:StaticResource greyTextColor}"
                        Opacity="0.5"
                        FontSize="12"
                        FontFamily="FontTextLight"
                        VerticalOptions="Center"
                        HorizontalOptions="Start">
                        <Label.FormattedText>
                            <FormattedString>
                                <FormattedString.Spans>
                                    <Span x:Name="minutesSpan" Text=""/>
                                    <Span Text=" мин"/>
                                </FormattedString.Spans>
                            </FormattedString>
                        </Label.FormattedText>
                    </Label>
                </StackLayout>
            </Grid>

            <Grid Grid.Column="2">
                <Image 
                    Source="arrowForwardLightBlue.png"
                    HorizontalOptions="Center"
                    VerticalOptions="Center"
                    WidthRequest="6"
                    HeightRequest="12" />
            </Grid>

        </Grid>
  </ContentView.Content>
</models:BaseRecommendationView>