﻿<?xml version="1.0" encoding="utf-8" ?>
<ResourceDictionary
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw">


    <Style
        x:Key="yellow_btn"
        TargetType="Button">
        <!--<Setter Property="FontFamily" Value="FontTextSemiBold"/>-->
        <Setter Property="BackgroundColor" Value="#FDCE72" />
        <Setter Property="BorderColor" Value="Transparent" />
        <Setter Property="CornerRadius" Value="16" />
        <Setter Property="TextColor" Value="#363B40" />
        <Setter Property="TextTransform" Value="None" />
        <Setter Property="FontSize" Value="{x:OnPlatform iOS=17, Android=14}" />
        <Setter Property="Padding" Value="0,0,0,0" />
    </Style>

    <Style
        x:Key="DrawnBtn"
        ApplyToDerivedTypes="True"
        TargetType="draw:SkiaButton">
        <Setter Property="FontFamily" Value="FontTextSemiBold" />
        <!-- TODO: TintColor property not found in current DrawnUI SkiaButton version - investigate correct property name -->
        <!-- <Setter Property="TintColor" Value="#FDCE72" /> -->
        <Setter Property="CornerRadius" Value="16" />
        <Setter Property="HorizontalOptions" Value="Fill" />
        <Setter Property="TextColor" Value="#363B40" />
        <Setter Property="FontSize" Value="14" />
    </Style>

    <Style
        x:Key="grey_cornered_btn"
        TargetType="Button">
        <!--<Setter Property="FontFamily" Value="FontTextSemiBold"/>-->
        <Setter Property="BackgroundColor" Value="Transparent" />
        <Setter Property="BorderColor" Value="#363B40" />
        <Setter Property="BorderWidth" Value="1" />
        <Setter Property="CornerRadius" Value="16" />
        <Setter Property="TextColor" Value="#363B40" />
        <Setter Property="TextTransform" Value="None" />
        <Setter Property="FontSize" Value="{x:OnPlatform iOS=17, Android=14}" />
        <Setter Property="Padding" Value="2" />
    </Style>

    <Style
        x:Key="transparent_btn"
        TargetType="Button">
        <!--<Setter Property="FontFamily" Value="FontTextSemiBold"/>-->
        <Setter Property="BackgroundColor" Value="Transparent" />
        <Setter Property="BorderColor" Value="Transparent" />
        <Setter Property="CornerRadius" Value="0" />
        <Setter Property="TextColor" Value="#363B40" />
        <Setter Property="TextTransform" Value="None" />
        <Setter Property="FontSize" Value="{x:OnPlatform iOS=17, Android=14}" />
        <Setter Property="Padding" Value="2" />
    </Style>


</ResourceDictionary>