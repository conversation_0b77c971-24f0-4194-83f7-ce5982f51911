﻿<?xml version="1.0" encoding="UTF-8"?>

<ContentView
    x:Class="Triggero.Controls.MoodTracker.TrackerAffectsView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:app="clr-namespace:Triggero"
    xmlns:mobile="clr-namespace:Triggero.MauiMobileApp">
    <ContentView.Content>


        <Frame
            Padding="10,0,10,0"
            BackgroundColor="White"
            CornerRadius="16"
            HasShadow="False">
            <Grid>
                <StackLayout Padding="0,10,0,20">

                    <StackLayout x:Name="influenceProgressesBlock">
                        <Label
                            FontAttributes="Bold"
                            FontSize="17"
                            HorizontalOptions="Start"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.MoodAffects}"
                            TextColor="{x:StaticResource greyTextColor}" />
                        <Label
                            FontSize="12"
                            HorizontalOptions="Start"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.MoodAffectsDescription}"
                            TextColor="{x:StaticResource ColorTextGray}" />

                        <StackLayout
                            x:Name="influenceProgressesLayout"
                            Margin="0,20,0,0"
                            Spacing="12">

                        </StackLayout>

                        <Grid
                            Margin="44,20,0,0"
                            HeightRequest="20"
                            HorizontalOptions="Fill"
                            VerticalOptions="Start">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                                <ColumnDefinition Width="1*" />
                            </Grid.ColumnDefinitions>

                            <Label
                                Grid.Column="0"
                                FontSize="12"

                                HorizontalOptions="Start"
                                Opacity="0.4"
                                Text="0"
                                TextColor="{x:StaticResource greyTextColor}" />

                            <Label
                                Grid.Column="1"
                                FontSize="12"

                                HorizontalOptions="Start"
                                Opacity="0.4"
                                Text="25"
                                TextColor="{x:StaticResource greyTextColor}" />

                            <Label
                                Grid.Column="2"
                                Margin="-5,0,0,0"
                                FontSize="12"

                                HorizontalOptions="Start"
                                Opacity="0.4"
                                Text="50"
                                TextColor="{x:StaticResource greyTextColor}" />

                            <Label
                                Grid.Column="3"
                                Margin="-12,0,0,0"
                                FontSize="12"

                                HorizontalOptions="Start"
                                Opacity="0.4"
                                Text="75"
                                TextColor="{x:StaticResource greyTextColor}" />

                            <Label
                                Grid.Column="3"
                                FontSize="12"

                                HorizontalOptions="End"
                                Opacity="0.4"
                                Text="100"
                                TextColor="{x:StaticResource greyTextColor}" />

                        </Grid>
                    </StackLayout>


                    <StackLayout
                        x:Name="influenceGroupsBlock"
                        Margin="0,30,0,0">
                        <Label
                            FontAttributes="Bold"
                            FontSize="17"
                            HorizontalOptions="Start"
                            Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MoodTracker.TrackerMainPage.MoodAffectsDetails}"
                            TextColor="{x:StaticResource greyTextColor}" />

                        <StackLayout
                            x:Name="influenceGroupsLayout"
                            Spacing="28">


                        </StackLayout>
                    </StackLayout>


                </StackLayout>
            </Grid>
        </Frame>


    </ContentView.Content>
</ContentView>