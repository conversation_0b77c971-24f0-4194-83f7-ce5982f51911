﻿<?xml version="1.0" encoding="UTF-8" ?>
<ContentView
    x:Class="Triggero.Controls.Cards.Tracker.TrackerNoteCard"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml">
    <ContentView.Content>
        <Grid>
            <Grid.GestureRecognizers>
                <TapGestureRecognizer Tapped="onTapped" />
            </Grid.GestureRecognizers>

            <StackLayout
                InputTransparent="True"
                Spacing="7">
                <Label
                    x:Name="noteLabel"
                    Margin="0,2,0,0"
                    FontAttributes="Bold"
                    FontFamily="FontTextLight"
                    FontSize="{x:OnPlatform Android=14,
                                          iOS=17}"
                    HeightRequest="18"
                    HorizontalOptions="Start"
                    Text="Мой день 29 сентября начался"
                    TextColor="Black"
                    VerticalOptions="Center" />
                <Label
                    x:Name="dateLabel"
                    FontFamily="FontTextLight"
                    FontSize="{x:OnPlatform Android=14,
                                          iOS=14}"
                    HorizontalOptions="Start"
                    Text="25.09.2022"
                    TextColor="Black"
                    VerticalOptions="Center" />
                <BoxView
                    Margin="0,10,0,3"
                    BackgroundColor="{x:StaticResource ColorTextGray}"
                    HeightRequest="0.75"
                    HorizontalOptions="Fill"
                    VerticalOptions="Start" />
            </StackLayout>



        </Grid>
    </ContentView.Content>
</ContentView>