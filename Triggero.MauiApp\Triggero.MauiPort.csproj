﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>

    <!--<TargetFrameworks>net9.0-windows10.0.19041.0</TargetFrameworks>-->

    <TargetFrameworks>net9.0-android;net9.0-ios</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>

		<OutputType>Exe</OutputType>
		<RootNamespace>Triggero.MobileMaui</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>Triggero.MobileMaui</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.triggero.mobilemaui</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>
    <Version>1</Version>

  </PropertyGroup>

  <PropertyGroup>
    <WindowsPackageType>None</WindowsPackageType>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">15.2</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
    <SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</SupportedOSPlatformVersion>
    <TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.19041.0</TargetPlatformMinVersion>
  </PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\**" />
		<!--<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />-->

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

 

	<ItemGroup>
	  <None Remove="Resources\Fonts\OpenSans-Bold.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-BoldItalic.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-ExtraBold.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-ExtraBoldItalic.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Italic.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Light.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-LightItalic.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Medium.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-MediumItalic.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Regular.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-Semibold.ttf" />
	  <None Remove="Resources\Fonts\OpenSans-SemiBoldItalic.ttf" />
	</ItemGroup>

  <ItemGroup Condition="$(Configuration) == 'Debug'">
    <!--https://github.com/BretJohnson/hot-preview-->
    <PackageReference Include="HotPreview.App.Maui" Version="0.13.27" />
  </ItemGroup>

  <!--optional personal dev provisioning for iPhone-->
  <Import Project="../../TriggeroProvisioning.targets" Condition="Exists('../../TriggeroProvisioning.targets')" />

  <ItemGroup>
    <PackageReference Include="Plugin.Firebase.CloudMessaging" Version="3.1.2" />
    <PackageReference Include="Plugin.Maui.Audio" Version="4.0.0" />

    <PackageReference Include="Microsoft.Maui.Controls" Version="9.0.70" />
    <PackageReference Include="CommunityToolkit.Maui" Version="12.0.0" />
    <PackageReference Include="HtmlAgilityPack" Version="1.12.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
    <PackageReference Include="Sharpnado.MaterialFrame.Maui" Version="2.0.0" />
	</ItemGroup>

  <ItemGroup>
    <PackageReference Include="Syncfusion.Maui.Buttons" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Calendar" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Carousel" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Charts" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Gauges" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Picker" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.ProgressBar" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Sliders" Version="30.1.37" />
    <PackageReference Include="Syncfusion.Maui.Rotator" Version="30.1.37" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Common\Triggero.Common.csproj" />
    <!-- REMOVED: MobileAPIWrapper project - replaced with MobileApiRequestHelper for camelCase JSON -->
    <ProjectReference Include="..\Triggero.Web\Triggero.ChatBot\Triggero.ChatBot.csproj" />
    <ProjectReference Include="..\Triggero.Web\Triggero.Models\Triggero.Models.csproj" />
    <ProjectReference Include="..\Triggero.Web\TriggeroWeb.Models\Triggero.Domain.csproj" />
    <ProjectReference Include="..\..\DrawnUi.Maui\src\Maui\DrawnUi\DrawnUi.Maui.csproj" />
  </ItemGroup>

 
   

</Project>
