﻿using AppoMobi.Specials;
using DrawnUi.Views;
using DrawnUi.Views;
using SkiaSharp;
using Triggero.MauiMobileApp.ViewModels;

namespace Triggero.MauiMobileApp.Views
{
    public partial class TestsCategoriesDrawn : Canvas
    {
        private readonly BaseCategoriesViewModel _vm;

        public TestsCategoriesDrawn()
        {
            _vm = new TestsCategoriesViewModel();

            InitializeComponent();

            BindingContext = _vm;
        }

        bool once;
        protected override void Draw(DrawingContext context)
        {
            base.Draw(context);

            if (!once)
            {
                once = true;
                Tasks.StartDelayed(TimeSpan.FromMilliseconds(30), async () =>
                {
                    await _vm.InitializeAsyc();
                });
            }
        }

 
    }


}