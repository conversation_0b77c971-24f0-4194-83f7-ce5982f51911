﻿<?xml version="1.0" encoding="UTF-8" ?>
<drawn:FastCellWithBanner
    x:Class="Triggero.Controls.Templates.CellCategoryDrawn"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:categories="clr-namespace:Triggero.Models.Practices.Categories;assembly=Triggero.Models"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    xmlns:drawn="clr-namespace:Triggero.MauiMobileApp.Views.Drawn"
    xmlns:viewModels="clr-namespace:Triggero.MauiMobileApp.ViewModels"
    x:Name="this"
    Padding="0,0,0,0"
    x:DataType="categories:AbstractCategory"
    HeightRequest="104"
    HorizontalOptions="Fill"
    UseCache="ImageDoubleBuffered"
    VerticalOptions="Start">

    <draw:SkiaShape
        x:Name="Frame"
        Padding="0"
        BackgroundColor="Gray"
        CornerRadius="0,16,16,0"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <draw:SkiaLayout
                Margin="16,0,90,0"
                HorizontalOptions="Start"
                Type="Column"
                UseCache="Operations"
                VerticalOptions="Center">

                <!--TITLE-->
                <draw:SkiaLabel
                    x:Name="titleLabel"
                    FontFamily="FontTextBold"
                    FontSize="16"
                    HorizontalOptions="Start"
                    MaxLines="1"
                    TextColor="{x:StaticResource greyTextColor}" />

                <!--DESCRIPTION-->
                <draw:SkiaLabel
                    x:Name="descLabel"
                    FontSize="10"
                    HorizontalOptions="Start"
                    MaxLines="3"
                    TextColor="#B2363B40" />
            </draw:SkiaLayout>

            <draw:SkiaLayout
                HorizontalOptions="End"
                VerticalOptions="Fill"
                WidthRequest="90">

                <draw:SkiaImage
                    LoadSourceOnFirstDraw="False"
                    x:Name="img"
                    Grid.Column="1"
                    Aspect="AspectFit"
                    EraseChangedContent="True"
                    HeightRequest="70"
                    HorizontalOptions="Start"
                    VerticalOptions="Center"
                    WidthRequest="70" />

            </draw:SkiaLayout>

        </draw:SkiaLayout>

    </draw:SkiaShape>

</drawn:FastCellWithBanner>