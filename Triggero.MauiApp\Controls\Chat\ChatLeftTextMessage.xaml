﻿<?xml version="1.0" encoding="UTF-8"?>
<ContentView xmlns="http://schemas.microsoft.com/dotnet/2021/maui" 
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="Triggero.Controls.Chat.ChatLeftTextMessage">
  <ContentView.Content>
      <Frame
          CornerRadius="12"
          BackgroundColor="#EEF5FB"
          HasShadow="False"
          Padding="0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="50"/>
                </Grid.ColumnDefinitions>

                <Grid Grid.Column="0">
                    <Label 
                        TextColor="{x:StaticResource greyTextColor}"
                        FontSize="17"
                        Margin="20,8,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Start"
                        Text="Привет, Кристина!"/>
                </Grid>

                <Grid Grid.Column="1">
                    <Label 
                        TextColor="{x:StaticResource greyTextColor}"
                        Opacity="0.5"
                        FontSize="12"
                        Margin="0,12,0,0"
                        VerticalOptions="Start"
                        HorizontalOptions="Center"
                        Text="8:38"/>
                </Grid>

            </Grid>
      </Frame>
  </ContentView.Content>
</ContentView>