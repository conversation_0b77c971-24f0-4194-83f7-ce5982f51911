﻿using Newtonsoft.Json;
using MobileAPIWrapper.Helpers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Domain.Models;
using Triggero.Models.Messengers.ChatBot;
using Triggero.Models.Messengers.Support;
using Triggero.Models.Tests;

namespace MobileAPIWrapper.Methods.Messengers
{
    public class SupportMessengerMethods
    {
        private string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("SupportMessenger/");


        public async Task<SupportChat> GetChat(int userId)
        {
            string url = BASE_HOST + $"GetChat?userId={userId}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<SupportChat>(response.Content);
            return obj;
        }

        public async Task SendMessage(int userId, SupportChatMessage message)
        {
            string url = BASE_HOST + $"SendMessage?userId={userId}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Put, message);
        }

        public async Task<SupportChatLongPollItem> LongPolling(int userId)
        {
            string url = BASE_HOST + $"LongPolling?userId={userId}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<SupportChatLongPollItem>(response.Content);
            return obj;
        }
        public async Task<SupportChatLongPollItem> LongPollingV2(int userId, int startMessagesCount)
        {
            string url = BASE_HOST + $"LongPollingV2?userId={userId}&startMessagesCount={startMessagesCount}";
            var response = await MobileRequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<SupportChatLongPollItem>(response.Content);
            if (obj == null)
            {

            }
            return obj;
        }
    }
}
